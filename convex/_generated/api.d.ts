/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as dispatchers_match from "../dispatchers/match.js";
import type * as dispatchers_matchmaking from "../dispatchers/matchmaking.js";
import type * as dispatchers_sagas_matchCreatedSaga from "../dispatchers/sagas/matchCreatedSaga.js";
import type * as dispatchers_sagas_matchEndedSaga from "../dispatchers/sagas/matchEndedSaga.js";
import type * as dispatchers_sagas_playerAddedToMatchingQueueSaga from "../dispatchers/sagas/playerAddedToMatchingQueueSaga.js";
import type * as dispatchers_sagas_playerRegistrationCancelledSaga from "../dispatchers/sagas/playerRegistrationCancelledSaga.js";
import type * as helpers from "../helpers.js";
import type * as http from "../http.js";
import type * as mutations_addPlayerToMatchMakingQueue from "../mutations/addPlayerToMatchMakingQueue.js";
import type * as mutations_cancelMatchRegistration from "../mutations/cancelMatchRegistration.js";
import type * as mutations_createCatalogCard from "../mutations/createCatalogCard.js";
import type * as mutations_createGame from "../mutations/createGame.js";
import type * as mutations_deleteCatalogCard from "../mutations/deleteCatalogCard.js";
import type * as mutations_deleteGame from "../mutations/deleteGame.js";
import type * as mutations_leaveMatch from "../mutations/leaveMatch.js";
import type * as mutations_saveDeck from "../mutations/saveDeck.js";
import type * as mutations_updateCatalogCard from "../mutations/updateCatalogCard.js";
import type * as mutations_updateDeck from "../mutations/updateDeck.js";
import type * as mutations_updateGame from "../mutations/updateGame.js";
import type * as queries_debug from "../queries/debug.js";
import type * as queries_getCurrentUser from "../queries/getCurrentUser.js";
import type * as queries_loadAvailableFilters from "../queries/loadAvailableFilters.js";
import type * as queries_loadCatalogCardsByGameId from "../queries/loadCatalogCardsByGameId.js";
import type * as queries_loadDeckBuilderSettingsByGameId from "../queries/loadDeckBuilderSettingsByGameId.js";
import type * as queries_loadDeckById from "../queries/loadDeckById.js";
import type * as queries_loadDecksByUserIdAndGameId from "../queries/loadDecksByUserIdAndGameId.js";
import type * as queries_loadGameById from "../queries/loadGameById.js";
import type * as queries_loadGameList from "../queries/loadGameList.js";
import type * as queries_loadGameSettingsByGameId from "../queries/loadGameSettingsByGameId.js";
import type * as queries_match_loadMatchById from "../queries/match/loadMatchById.js";
import type * as queries_match_matchEvents from "../queries/match/matchEvents.js";
import type * as queries_match_onMatchEnded from "../queries/match/onMatchEnded.js";
import type * as queries_onMatchCreated from "../queries/onMatchCreated.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  "dispatchers/match": typeof dispatchers_match;
  "dispatchers/matchmaking": typeof dispatchers_matchmaking;
  "dispatchers/sagas/matchCreatedSaga": typeof dispatchers_sagas_matchCreatedSaga;
  "dispatchers/sagas/matchEndedSaga": typeof dispatchers_sagas_matchEndedSaga;
  "dispatchers/sagas/playerAddedToMatchingQueueSaga": typeof dispatchers_sagas_playerAddedToMatchingQueueSaga;
  "dispatchers/sagas/playerRegistrationCancelledSaga": typeof dispatchers_sagas_playerRegistrationCancelledSaga;
  helpers: typeof helpers;
  http: typeof http;
  "mutations/addPlayerToMatchMakingQueue": typeof mutations_addPlayerToMatchMakingQueue;
  "mutations/cancelMatchRegistration": typeof mutations_cancelMatchRegistration;
  "mutations/createCatalogCard": typeof mutations_createCatalogCard;
  "mutations/createGame": typeof mutations_createGame;
  "mutations/deleteCatalogCard": typeof mutations_deleteCatalogCard;
  "mutations/deleteGame": typeof mutations_deleteGame;
  "mutations/leaveMatch": typeof mutations_leaveMatch;
  "mutations/saveDeck": typeof mutations_saveDeck;
  "mutations/updateCatalogCard": typeof mutations_updateCatalogCard;
  "mutations/updateDeck": typeof mutations_updateDeck;
  "mutations/updateGame": typeof mutations_updateGame;
  "queries/debug": typeof queries_debug;
  "queries/getCurrentUser": typeof queries_getCurrentUser;
  "queries/loadAvailableFilters": typeof queries_loadAvailableFilters;
  "queries/loadCatalogCardsByGameId": typeof queries_loadCatalogCardsByGameId;
  "queries/loadDeckBuilderSettingsByGameId": typeof queries_loadDeckBuilderSettingsByGameId;
  "queries/loadDeckById": typeof queries_loadDeckById;
  "queries/loadDecksByUserIdAndGameId": typeof queries_loadDecksByUserIdAndGameId;
  "queries/loadGameById": typeof queries_loadGameById;
  "queries/loadGameList": typeof queries_loadGameList;
  "queries/loadGameSettingsByGameId": typeof queries_loadGameSettingsByGameId;
  "queries/match/loadMatchById": typeof queries_match_loadMatchById;
  "queries/match/matchEvents": typeof queries_match_matchEvents;
  "queries/match/onMatchEnded": typeof queries_match_onMatchEnded;
  "queries/onMatchCreated": typeof queries_onMatchCreated;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
