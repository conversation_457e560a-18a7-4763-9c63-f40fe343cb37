import {MutationCtx} from "../../_generated/server";
import {
  CleanUpMatchMakingQueueCommandHandler
} from "@/src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler";
import {
  UpdatePlayersStatusCommandHandler
} from "@/src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";
import {
  ConvexMatchmakingQueueRepository
} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {
  ConvexAppUserRepository
} from "@/src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";

export async function matchCreatedSaga(
  ctx: MutationCtx,
  payload: {
    matchId: string;
    players: string[];
  }
) {
  const eventBus = new ConvexEventBus(ctx);
  const cleanUpMatchMakingQueueCommandHandler = new CleanUpMatchMakingQueueCommandHandler(
    eventBus,
    new ConvexMatchRepository(ctx),
    new ConvexMatchmakingQueueRepository(ctx)
  );
  const updatePlayersStatusCommandHandler = new UpdatePlayersStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );

  await Promise.all([
    cleanUpMatchMakingQueueCommandHandler.handle({matchId: payload.matchId as string, players: payload.players}),
    updatePlayersStatusCommandHandler.handle({players: payload.players, status: "playing"}),
  ]);
}
