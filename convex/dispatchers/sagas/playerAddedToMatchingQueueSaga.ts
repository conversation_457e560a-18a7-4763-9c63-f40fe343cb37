import {MutationCtx} from "../../_generated/server";
import {Id} from "../../_generated/dataModel";
import {MakeMatchCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler";
import {UpdatePlayersStatusCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";
import {ConvexAppUserRepository} from "@/src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";

export async function playerAddedToMatchMakingQueueSaga(
  ctx: MutationCtx,
  payload: {
    gameId: string;
    playerId: string;
    queueId: string;
  }
) {
  const eventBus = new ConvexEventBus(ctx);
  const makeMatchCommandHandler = new MakeMatchCommandHandler(
    eventBus,
    new ConvexMatchmakingQueueRepository(ctx),
    new ConvexMatchRepository(ctx)
  );
  const updatePlayersStatusCommandHandler = new UpdatePlayersStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );

  await Promise.all([
    makeMatchCommandHandler.handle({ gameId: payload.gameId as string,  playerId: payload.playerId,  queueId: payload.queueId as string}),
    updatePlayersStatusCommandHandler.handle({players: [payload.playerId], status: "waiting-for-opponent"}),
  ]);
}
