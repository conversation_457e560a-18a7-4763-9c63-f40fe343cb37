import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {ConvexDeckRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository";
import {UpdateDeckCommandHandler} from "@/src/server/DeckBuilding/application/commands/UpdateDeck/UpdateDeckCommandHandler";
import {DeckNotOwnedError} from "@/src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError";

export const endpoint = protectedMutation({
  args: {
    deckId: v.id("decks"),
    name: v.string(),
    cards: v.array(v.object({cardId: v.string(), quantity: v.number()})),
  },
  handler: async (ctx, {deckId, name, cards}) => {
    const repository = new ConvexDeckRepository(ctx);
    const handler = new UpdateDeckCommandHandler(repository);
    try {
      await handler.handle({
        deckId: deckId as string,
        userId: ctx.userId,
        name,
        cards,
      });
    } catch (error) {
      if (error instanceof DeckNotOwnedError) {
        throw error;
      }
    }
  },
});
