import {protectedQuery} from "../helpers";
import {v} from "convex/values";
import {
  LoadDeckByIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadDeckById/LoadDeckByIdQueryHandler";
import {ConvexDeckReadRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckReadRepository';
import {LoadDeckByIdWebPresenter} from '@/src/server/DeckBuilding/presentation/presenters/LoadDeckByIdWebPresenter';

export const endpoint = protectedQuery({
  args: {deckId: v.id("decks")},
  handler: async (ctx, {deckId}) => {
    const repository = new ConvexDeckReadRepository(ctx);
    const queryHandler = new LoadDeckByIdQueryHandler(repository);
    const presenter = new LoadDeckByIdWebPresenter();

    await queryHandler.handle({deckId}, presenter);

    return presenter.getViewModel();
  },
});
