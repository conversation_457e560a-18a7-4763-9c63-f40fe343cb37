import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {LoadCatalogCardsByGameIdQueryHandler} from "@/src/server/DeckBuilding/application/queries/LoadCatalogCardsByGameId/LoadCatalogCardsByGameIdQueryHandler";
import {ConvexCatalogCardListRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {LoadCatalogCardsByGameIdWebPresenter} from "@/src/server/DeckBuilding/presentation/presenters/LoadCatalogCardsByGameIdWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexCatalogCardListRepository(ctx);
    const presenter = new LoadCatalogCardsByGameIdWebPresenter();
    const queryHandler = new LoadCatalogCardsByGameIdQueryHandler(repository);

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});
