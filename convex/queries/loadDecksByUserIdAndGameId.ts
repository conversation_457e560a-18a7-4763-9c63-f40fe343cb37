import {protectedQuery} from "../helpers";
import {v} from "convex/values";
import {LoadDecksByUserIdAndGameIdQueryHandler} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQueryHandler";
import {ConvexDeckListRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/DeckList/ConvexDeckListRepository";
import {LoadDecksByUserIdAndGameIdWebPresenter} from "@/src/server/DeckBuilding/presentation/presenters/LoadDecksByUserIdAndGameIdWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexDeckListRepository(ctx);
    const queryHandler = new LoadDecksByUserIdAndGameIdQueryHandler(repository);
    const presenter = new LoadDecksByUserIdAndGameIdWebPresenter();

    await queryHandler.handle({gameId, userId: ctx.userId}, presenter);

    return presenter.getViewModel();
  },
});
