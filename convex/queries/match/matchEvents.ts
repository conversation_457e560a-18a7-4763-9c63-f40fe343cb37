import {v} from "convex/values";
import {protectedQuery} from "@/convex/helpers";
import {LoadMatchEventsQueryHandler} from "@/src/server/Gaming/application/queries/LoadMatchEvents/LoadMatchEventsQueryHandler";
import {ConvexMatchEventListRepository} from "@/src/server/Gaming/infrastructure/repositories/MatchEventList/ConvexMatchEventListRepository";
import {LoadMatchEventsWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchEventsWebPresenter";

export const endpoint = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchEventListRepository(ctx);
    const handler = new LoadMatchEventsQueryHandler(repository);
    const presenter = new LoadMatchEventsWebPresenter();

    await handler.handle({matchId}, presenter);

    return presenter.getViewModel();
  },
});
