import {v} from "convex/values";
import {protectedQuery} from "@/convex/helpers";
import {LoadMatchEndedEventQueryHandler} from "@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQueryHandler";
import {ConvexMatchEndedEventRepository} from "@/src/server/Gaming/infrastructure/repositories/MatchEndedEvent/ConvexMatchEndedEventRepository";
import {LoadMatchEndedEventWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchEndedEventWebPresenter";

export const endpoint = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchEndedEventRepository(ctx);
    const queryHandler = new LoadMatchEndedEventQueryHandler(repository);
    const presenter = new LoadMatchEndedEventWebPresenter();

    await queryHandler.handle({matchId}, presenter);

    return presenter.getViewModel();
  },
});
