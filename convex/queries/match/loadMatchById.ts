import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {LoadMatchByIdQueryHandler} from "@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQueryHandler";
import {ConvexMatchReadRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchReadRepository";
import {LoadMatchByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchByIdWebPresenter";

export const endpoint = protectedQuery({
  args: {
    matchId: v.string(),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchReadRepository(ctx);
    const handler = new LoadMatchByIdQueryHandler(repository);
    const presenter = new LoadMatchByIdWebPresenter();

    await handler.handle({matchId, userId: ctx.userId}, presenter);

    return presenter.getViewModel();
  },
});
