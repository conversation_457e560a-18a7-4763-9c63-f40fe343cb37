import {query} from "../_generated/server";
import {GetCurrentUserQueryHandler} from "@/src/server/Authentication/application/queries/GetCurrentUser/GetCurrentUserQueryHandler";
import {ConvexCurrentUserRepository} from "@/src/server/Authentication/infrastructure/repositories/CurrentUser/ConvexCurrentUserRepository";
import {GetCurrentUserWebPresenter} from "@/src/server/Authentication/presentation/presenters/GetCurrentUserWebPresenter";

export const endpoint = query({
  args: {},
  handler: async (ctx) => {
    const repository = new ConvexCurrentUserRepository(ctx);
    const queryHandler = new GetCurrentUserQueryHandler(repository);
    const presenter = new GetCurrentUserWebPresenter();

    await queryHandler.handle(presenter);

    return presenter.getViewModel();
  },
});
