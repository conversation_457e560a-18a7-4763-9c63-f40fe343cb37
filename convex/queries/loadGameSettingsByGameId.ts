import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {LoadGameSettingsByGameIdQueryHandler} from "@/src/server/Gaming/application/queries/LoadGameSettingsByGameId/LoadGameSettingsByGameIdQueryHandler";
import {ConvexGameSettingsRepository} from "@/src/server/Gaming/infrastructure/repositories/GameSettings/ConvexGameSettingsRepository";
import {LoadGameSettingsByGameIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameSettingsByGameIdWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameSettingsRepository(ctx);
    const queryHandler = new LoadGameSettingsByGameIdQueryHandler(repository);
    const presenter = new LoadGameSettingsByGameIdWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});
