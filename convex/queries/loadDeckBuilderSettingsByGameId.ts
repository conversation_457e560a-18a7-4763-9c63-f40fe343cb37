import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  LoadDeckBuilderSettingsByIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadDeckBuilderSettingsById/LoadDeckBuilderSettingsByIdQueryHandler";
import {ConvexDeckBuilderSettingsRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/DeckBuilderSettings/ConvexDeckBuilderSettingsRepository";
import {LoadDeckBuilderSettingsByIdWebPresenter} from "@/src/server/DeckBuilding/presentation/presenters/LoadDeckBuilderSettingsByIdWebPresenter";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexDeckBuilderSettingsRepository(ctx);
    const queryHandler = new LoadDeckBuilderSettingsByIdQueryHandler(repository);
    const presenter = new LoadDeckBuilderSettingsByIdWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});