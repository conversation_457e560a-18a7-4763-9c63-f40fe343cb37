# Repository Instructions

## Important
- Run `npm install` before starting to work and wait for it to complete (be patient as it can take a while).
- If packages install is not working, stop the task immediately and inform the user.

## General Workflow
- Follow a **Test-Driven Development** approach.
- After each iteration or change, run `npm run lint` and `npm test`.
- Both commands must succeed before committing.
- Keep commits focused and describe the functional change.

## Testing Guidelines
- Tests use **Vitest** with jest-extended matchers.
- Use `npm test` to run them.
- Structure tests with the sections:
  ```ts
  // Arrange
  // Act
  // Assert
  ```
- Only perform **one** action in the `// Act` section.
- Use sociable tests that exercise use cases (command or query handlers).
- These act as acceptance tests; the domain is never tested directly.
- Avoid overly technical tests and solitary unit tests.
- Do **not** dispatch multiple Redux actions in one test; setup must be manual.
- `describe` blocks should use `describe("When ...")` to give context.
- Each `it` statement must start with `should` and stay close to the functional behaviour.
- Never leave `.only` or `.skip` in committed tests.

## Project Architecture
- The project follows **Clean Architecture + DDD + CQRS + TDD** organized by **Bounded Contexts**:
  - **Structure**: `src/client/[BoundedContext]` and `src/server/[BoundedContext]`
  - **Bounded Contexts**: `Authentication`, `DeckBuilding`, `Gaming`, `MatchMaking`, `Shared`
  - **Layers**: `domain`, `application`, `infrastructure`, `presentation`, `specs`
  - **ESLint boundary rules** enforce these constraints

## Key Patterns
- **CQRS**: Commands and Queries with dedicated handlers
- **Repository Pattern**: Interfaces in application, implementations in infrastructure
- **Presenter Pattern**: For view models and display logic
- **Domain Entities**: With factory methods, snapshots, and business logic
- **Value Objects**: Immutable objects representing domain concepts

## File Naming Conventions
- **Commands**: `[Action]Command.ts`, `[Action]CommandHandler.ts`
- **Queries**: `[Action]Query.ts`, `[Action]QueryHandler.ts`
- **Repositories**: `[Entity]Repository.ts` (interface), `Convex[Entity]Repository.ts`, `InMemory[Entity]Repository.ts`
- **Presenters**: `[Action]Presenter.ts` (interface), `[Action]WebPresenter.ts`
- **Tests**: `[action].spec.ts`
- **Domain**: `[Entity].ts`, `[ValueObject].ts`

## Import Conventions
- Path alias `@/*` points to the repository root
- Always use absolute imports with the `@/` alias
- Import from interfaces, not implementations
- Follow dependency direction: domain ← application ← infrastructure/presentation

## Tools
- **Linting**: `npm run lint` (TypeScript + ESLint with boundary rules)
- **Testing**: `npm test` (Vitest)
- **Coverage**: `npm run test:coverage`
- **Development**: `npm run dev:all` (Next.js + Convex)

## Frontend Specific
- **Framework**: Next.js 15 with React 19
- **UI**: Radix UI components and themes
- **State**: Redux with clean architecture patterns
- **Internationalization**: Supports `en` and `fr` locales
- **Styling**: Tailwind CSS

## Commit Requirements
- Keep the working directory clean before committing
- Include relevant tests for new features or fixes
- Ensure `npm run lint` and `npm test` pass before every commit
- Follow conventional commit messages describing functional changes
- Branch names should be descriptive and related to the task or feature in english
