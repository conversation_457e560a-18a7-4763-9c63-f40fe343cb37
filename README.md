# Front & Back Clean Architecture NextJS template

## Content
- React 19
- NextJS 15
- Clean architecture template
- iOctopus ioc container (set up for back & front)
- radix-ui + radix-ui themes
- tanstack query + tanstack devtools
- vitest + vitest-mock-extended + vite-tsconfig-paths + @vitest/coverage-v8
- testing library
- auto-animate
- depcruise (with clean architecture rules)

Note: Edit the tools/github.js file to change the repository url.

## Environment setup

When running in a fresh container without Node.js pre-installed, install Node and npm first:

```bash
apt-get update && apt-get install -y nodejs npm
```

Then install project dependencies. This step can take a few minutes so wait until it completes:

```bash
npm install
```

Once dependencies are installed, you can run the linter:

```bash
npm run lint
```

You can also run the tests:

```bash
npm test
```

### Cypress tests

Run Cypress tests locally with:

```bash
npm run cy:local
```

Run Cypress tests in CI with:

```bash
npm run cy:ci
```

