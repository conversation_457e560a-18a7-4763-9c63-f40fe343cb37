import {FC} from "react";
import {Flex} from "@radix-ui/themes";
import DeckBuildingFilters
  from "@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters";
import DeckBuilderCardsGrid
  from "@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid";
import DeckBuilderPanel
  from "@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel";
import {
  EditDeckInitializer
} from "@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer";

import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {convexAuthNextjsToken} from "@convex-dev/auth/nextjs/server";

type Props = {
  params: Promise<{ gameId: string; locale: string }>;
  searchParams: Promise<{ deckId?: string }>;
};

const DeckBuilderPageContainer: FC<Props> = async ({params, searchParams}) => {
  const {gameId} = await params;
  const {deckId} = await searchParams;

  const loadAvailableFilters = fetchQuery(
    api.queries.loadAvailableFilters.endpoint,
    {gameId: gameId as Id<"games">},
    {token: await convexAuthNextjsToken()}
  );

  const deckPromise = deckId
    ? fetchQuery(
        api.queries.loadDeckById.endpoint,
        {deckId: deckId as Id<'decks'>},
        {token: await convexAuthNextjsToken()}
      )
    : Promise.resolve(undefined);

  const [{data}, deckResult] = await Promise.all([
    loadAvailableFilters,
    deckPromise,
  ]);
  const deck = deckResult?.data;

  if (!data) {
    throw new Error("Failed to load available filters");
  }

  return (
    <Flex direction="row" gap="1" p="3" className="h-full">
      <Flex direction="column" className="min-w-[240px]">
        <DeckBuildingFilters groupedFilters={data.groupedFilters} availableFilters={data.availableFilters}/>
      </Flex>

      <Flex direction="column" className="h-full w-full">
        {deck && <EditDeckInitializer deck={deck}/>}
        <DeckBuilderCardsGrid/>
      </Flex>

      <Flex direction="column" className="h-full min-w-[400px]">
        <DeckBuilderPanel/>
      </Flex>
    </Flex>
  );
};

export default DeckBuilderPageContainer;
