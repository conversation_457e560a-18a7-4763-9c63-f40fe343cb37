'use client';

import {FC, PropsWithChildren} from "react";
import {Flex} from "@radix-ui/themes";
import {ReduxProvider} from "@/src/client/Shared/providers/ReduxProvider/ReduxProvider";
import {ConvexQueryCacheProvider} from "convex-helpers/react/cache";

const DeckBuilderLayout: FC<PropsWithChildren> = ({children}) => (
  <Flex direction="column" className="h-screen">
    <Flex direction="column" className="h-[calc(100vh-40px)]">
      <ConvexQueryCacheProvider>
        <ReduxProvider>
          {children}
        </ReduxProvider>
      </ConvexQueryCacheProvider>
    </Flex>
  </Flex>
);

export default DeckBuilderLayout;