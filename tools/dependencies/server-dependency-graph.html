<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <title>dependency graph</title>
    <style>
      .node:active path,
.node:hover path,
.node.current path,
.node:active polygon,
.node:hover polygon,
.node.current polygon {
  stroke: fuchsia;
  stroke-width: 2;
}

.edge:active path,
.edge:hover path,
.edge.current path,
.edge:active ellipse,
.edge:hover ellipse,
.edge.current ellipse {
  stroke: url(#edgeGradient);
  stroke-width: 3;
  stroke-opacity: 1;
}

.edge:active polygon,
.edge:hover polygon,
.edge.current polygon {
  stroke: fuchsia;
  stroke-width: 3;
  fill: fuchsia;
  stroke-opacity: 1;
  fill-opacity: 1;
}

.edge:active text,
.edge:hover text {
  fill: fuchsia;
}

.cluster path {
  stroke-width: 3;
}

.cluster:active path,
.cluster:hover path {
  fill: #ffff0011;
}

div.hint {
  background-color: #000000aa;
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  border-radius: 1rem;
  position: fixed;
  top: calc(50% - 4em);
  right: calc(50% - 10em);
  border: none;
  padding: 1em 3em 1em 1em;
}

.hint button {
  position: absolute;
  font-weight: bolder;
  right: 0.6em;
  top: 0.6em;
  color: inherit;
  background-color: inherit;
  border: 1px solid currentColor;
  border-radius: 1em;
  margin-left: 0.6em;
}

.hint a {
  color: inherit;
}

#button_help {
  color: white;
  background-color: #00000011;
  border-radius: 1em;
  position: fixed;
  top: 1em;
  right: 1em;
  font-size: 24pt;
  font-weight: bolder;
  width: 2em;
  height: 2em;
  border: none;
}

#button_help:hover {
  cursor: pointer;
  background-color: #00000077;
}

@media print {
  #button_help {
    display: none;
  }

  div.hint {
    display: none;
  }
}

    </style>
  </head>
  <body>
    <button id="button_help">?</button>
    <div id="hints" class="hint" style="display: none">
      <button id="close-hints">x</button>
      <span id="hint-text"></span>
      <ul>
        <li><b>Hover</b> - highlight</li>
        <li><b>Right-click</b> - pin highlight</li>
        <li><b>ESC</b> - clear</li>
      </ul>
    </div>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.0 (20241103.1931)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="936pt" height="3122pt"
 viewBox="0.00 0.00 935.50 3122.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 3118)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-3118 931.5,-3118 931.5,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M107.38,-8C107.38,-8 440.25,-8 440.25,-8 446.25,-8 452.25,-14 452.25,-20 452.25,-20 452.25,-822 452.25,-822 452.25,-828 446.25,-834 440.25,-834 440.25,-834 107.38,-834 107.38,-834 101.38,-834 95.38,-828 95.38,-822 95.38,-822 95.38,-20 95.38,-20 95.38,-14 101.38,-8 107.38,-8"/>
<text text-anchor="middle" x="273.81" y="-821.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_app/[locale]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M115.38,-16C115.38,-16 247.62,-16 247.62,-16 253.62,-16 259.62,-22 259.62,-28 259.62,-28 259.62,-738 259.62,-738 259.62,-744 253.62,-750 247.62,-750 247.62,-750 115.38,-750 115.38,-750 109.38,-750 103.38,-744 103.38,-738 103.38,-738 103.38,-28 103.38,-28 103.38,-22 109.38,-16 115.38,-16"/>
<text text-anchor="middle" x="181.5" y="-737.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[locale]</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_app/[locale]/(connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M123.38,-24C123.38,-24 239.62,-24 239.62,-24 245.62,-24 251.62,-30 251.62,-36 251.62,-36 251.62,-502 251.62,-502 251.62,-508 245.62,-514 239.62,-514 239.62,-514 123.38,-514 123.38,-514 117.38,-514 111.38,-508 111.38,-502 111.38,-502 111.38,-36 111.38,-36 111.38,-30 117.38,-24 123.38,-24"/>
<text text-anchor="middle" x="181.5" y="-501.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(connected)</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_app/[locale]/(connected)/games</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M131.38,-32C131.38,-32 231.62,-32 231.62,-32 237.62,-32 243.62,-38 243.62,-44 243.62,-44 243.62,-379 243.62,-379 243.62,-385 237.62,-391 231.62,-391 231.62,-391 131.38,-391 131.38,-391 125.38,-391 119.38,-385 119.38,-379 119.38,-379 119.38,-44 119.38,-44 119.38,-38 125.38,-32 131.38,-32"/>
<text text-anchor="middle" x="181.5" y="-378.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">games</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M139.38,-40C139.38,-40 223.62,-40 223.62,-40 229.62,-40 235.62,-46 235.62,-52 235.62,-52 235.62,-326 235.62,-326 235.62,-332 229.62,-338 223.62,-338 223.62,-338 139.38,-338 139.38,-338 133.38,-338 127.38,-332 127.38,-326 127.38,-326 127.38,-52 127.38,-52 127.38,-46 133.38,-40 139.38,-40"/>
<text text-anchor="middle" x="181.5" y="-325.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[gameId]</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M147.38,-48C147.38,-48 215.62,-48 215.62,-48 221.62,-48 227.62,-54 227.62,-60 227.62,-60 227.62,-212 227.62,-212 227.62,-218 221.62,-224 215.62,-224 215.62,-224 147.38,-224 147.38,-224 141.38,-224 135.38,-218 135.38,-212 135.38,-212 135.38,-60 135.38,-60 135.38,-54 141.38,-48 147.38,-48"/>
<text text-anchor="middle" x="181.5" y="-211.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deck&#45;builder</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M155.38,-56C155.38,-56 207.62,-56 207.62,-56 213.62,-56 219.62,-62 219.62,-68 219.62,-68 219.62,-128 219.62,-128 219.62,-134 213.62,-140 207.62,-140 207.62,-140 155.38,-140 155.38,-140 149.38,-140 143.38,-134 143.38,-128 143.38,-128 143.38,-68 143.38,-68 143.38,-62 149.38,-56 155.38,-56"/>
<text text-anchor="middle" x="181.5" y="-127.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[deckId]</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/play</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M158.5,-232C158.5,-232 204.5,-232 204.5,-232 210.5,-232 216.5,-238 216.5,-244 216.5,-244 216.5,-273 216.5,-273 216.5,-279 210.5,-285 204.5,-285 204.5,-285 158.5,-285 158.5,-285 152.5,-285 146.5,-279 146.5,-273 146.5,-273 146.5,-244 146.5,-244 146.5,-238 152.5,-232 158.5,-232"/>
<text text-anchor="middle" x="181.5" y="-272.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">play</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_app/[locale]/(connected)/matches</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M150.5,-399C150.5,-399 212.5,-399 212.5,-399 218.5,-399 224.5,-405 224.5,-411 224.5,-411 224.5,-475 224.5,-475 224.5,-481 218.5,-487 212.5,-487 212.5,-487 150.5,-487 150.5,-487 144.5,-487 138.5,-481 138.5,-475 138.5,-475 138.5,-411 138.5,-411 138.5,-405 144.5,-399 150.5,-399"/>
<text text-anchor="middle" x="181.5" y="-474.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">matches</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_app/[locale]/(connected)/matches/[matchId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M158.5,-407C158.5,-407 204.5,-407 204.5,-407 210.5,-407 216.5,-413 216.5,-419 216.5,-419 216.5,-448 216.5,-448 216.5,-454 210.5,-460 204.5,-460 204.5,-460 158.5,-460 158.5,-460 152.5,-460 146.5,-454 146.5,-448 146.5,-448 146.5,-419 146.5,-419 146.5,-413 152.5,-407 158.5,-407"/>
<text text-anchor="middle" x="181.5" y="-447.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[matchId]</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M150.5,-522C150.5,-522 212.5,-522 212.5,-522 218.5,-522 224.5,-528 224.5,-534 224.5,-534 224.5,-624 224.5,-624 224.5,-630 218.5,-636 212.5,-636 212.5,-636 150.5,-636 150.5,-636 144.5,-636 138.5,-630 138.5,-624 138.5,-624 138.5,-534 138.5,-534 138.5,-528 144.5,-522 150.5,-522"/>
<text text-anchor="middle" x="181.5" y="-623.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(not&#45;connected)</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)/signin</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M158.5,-530C158.5,-530 204.5,-530 204.5,-530 210.5,-530 216.5,-536 216.5,-542 216.5,-542 216.5,-571 216.5,-571 216.5,-577 210.5,-583 204.5,-583 204.5,-583 158.5,-583 158.5,-583 152.5,-583 146.5,-577 146.5,-571 146.5,-571 146.5,-542 146.5,-542 146.5,-536 152.5,-530 158.5,-530"/>
<text text-anchor="middle" x="181.5" y="-570.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">signin</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_app/[locale]/access&#45;denied</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M154.5,-644C154.5,-644 209.5,-644 209.5,-644 215.5,-644 221.5,-650 221.5,-656 221.5,-656 221.5,-685 221.5,-685 221.5,-691 215.5,-697 209.5,-697 209.5,-697 154.5,-697 154.5,-697 148.5,-697 142.5,-691 142.5,-685 142.5,-685 142.5,-656 142.5,-656 142.5,-650 148.5,-644 154.5,-644"/>
<text text-anchor="middle" x="182" y="-684.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">access&#45;denied</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-842C20,-842 907.5,-842 907.5,-842 913.5,-842 919.5,-848 919.5,-854 919.5,-854 919.5,-3094 919.5,-3094 919.5,-3100 913.5,-3106 907.5,-3106 907.5,-3106 20,-3106 20,-3106 14,-3106 8,-3100 8,-3094 8,-3094 8,-854 8,-854 8,-848 14,-842 20,-842"/>
<text text-anchor="middle" x="463.75" y="-3093.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src/server</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M28,-850C28,-850 899.5,-850 899.5,-850 905.5,-850 911.5,-856 911.5,-862 911.5,-862 911.5,-3067 911.5,-3067 911.5,-3073 905.5,-3079 899.5,-3079 899.5,-3079 28,-3079 28,-3079 22,-3079 16,-3073 16,-3067 16,-3067 16,-862 16,-862 16,-856 22,-850 28,-850"/>
<text text-anchor="middle" x="463.75" y="-3066.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">server</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/server/Authentication</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M86.38,-1308C86.38,-1308 871.38,-1308 871.38,-1308 877.38,-1308 883.38,-1314 883.38,-1320 883.38,-1320 883.38,-1581 883.38,-1581 883.38,-1587 877.38,-1593 871.38,-1593 871.38,-1593 86.38,-1593 86.38,-1593 80.38,-1593 74.38,-1587 74.38,-1581 74.38,-1581 74.38,-1320 74.38,-1320 74.38,-1314 80.38,-1308 86.38,-1308"/>
<text text-anchor="middle" x="478.88" y="-1580.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Authentication</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/server/Authentication/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M350,-1447C350,-1447 476.75,-1447 476.75,-1447 482.75,-1447 488.75,-1453 488.75,-1459 488.75,-1459 488.75,-1554 488.75,-1554 488.75,-1560 482.75,-1566 476.75,-1566 476.75,-1566 350,-1566 350,-1566 344,-1566 338,-1560 338,-1554 338,-1554 338,-1459 338,-1459 338,-1453 344,-1447 350,-1447"/>
<text text-anchor="middle" x="413.38" y="-1553.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/server/Authentication/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M358,-1455C358,-1455 468.75,-1455 468.75,-1455 474.75,-1455 480.75,-1461 480.75,-1467 480.75,-1467 480.75,-1527 480.75,-1527 480.75,-1533 474.75,-1539 468.75,-1539 468.75,-1539 358,-1539 358,-1539 352,-1539 346,-1533 346,-1527 346,-1527 346,-1467 346,-1467 346,-1461 352,-1455 358,-1455"/>
<text text-anchor="middle" x="413.38" y="-1526.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/server/Authentication/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M567.5,-1316C567.5,-1316 863.38,-1316 863.38,-1316 869.38,-1316 875.38,-1322 875.38,-1328 875.38,-1328 875.38,-1554 875.38,-1554 875.38,-1560 869.38,-1566 863.38,-1566 863.38,-1566 567.5,-1566 567.5,-1566 561.5,-1566 555.5,-1560 555.5,-1554 555.5,-1554 555.5,-1328 555.5,-1328 555.5,-1322 561.5,-1316 567.5,-1316"/>
<text text-anchor="middle" x="715.44" y="-1553.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/server/Authentication/domain/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M620.62,-1451C620.62,-1451 855.38,-1451 855.38,-1451 861.38,-1451 867.38,-1457 867.38,-1463 867.38,-1463 867.38,-1527 867.38,-1527 867.38,-1533 861.38,-1539 855.38,-1539 855.38,-1539 620.62,-1539 620.62,-1539 614.62,-1539 608.62,-1533 608.62,-1527 608.62,-1527 608.62,-1463 608.62,-1463 608.62,-1457 614.62,-1451 620.62,-1451"/>
<text text-anchor="middle" x="738" y="-1526.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/server/Authentication/domain/AppUser/valueObjects</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M786.88,-1459C786.88,-1459 847.38,-1459 847.38,-1459 853.38,-1459 859.38,-1465 859.38,-1471 859.38,-1471 859.38,-1500 859.38,-1500 859.38,-1506 853.38,-1512 847.38,-1512 847.38,-1512 786.88,-1512 786.88,-1512 780.88,-1512 774.88,-1506 774.88,-1500 774.88,-1500 774.88,-1471 774.88,-1471 774.88,-1465 780.88,-1459 786.88,-1459"/>
<text text-anchor="middle" x="817.12" y="-1499.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">valueObjects</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/server/Authentication/domain/User</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M575.5,-1324C575.5,-1324 718,-1324 718,-1324 724,-1324 730,-1330 730,-1336 730,-1336 730,-1431 730,-1431 730,-1437 724,-1443 718,-1443 718,-1443 575.5,-1443 575.5,-1443 569.5,-1443 563.5,-1437 563.5,-1431 563.5,-1431 563.5,-1336 563.5,-1336 563.5,-1330 569.5,-1324 575.5,-1324"/>
<text text-anchor="middle" x="646.75" y="-1430.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">User</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/server/Authentication/domain/User/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M583.5,-1332C583.5,-1332 710,-1332 710,-1332 716,-1332 722,-1338 722,-1344 722,-1344 722,-1404 722,-1404 722,-1410 716,-1416 710,-1416 710,-1416 583.5,-1416 583.5,-1416 577.5,-1416 571.5,-1410 571.5,-1404 571.5,-1404 571.5,-1344 571.5,-1344 571.5,-1338 577.5,-1332 583.5,-1332"/>
<text text-anchor="middle" x="646.75" y="-1403.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/server/Authentication/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M94.38,-1317C94.38,-1317 268.62,-1317 268.62,-1317 274.62,-1317 280.62,-1323 280.62,-1329 280.62,-1329 280.62,-1550 280.62,-1550 280.62,-1556 274.62,-1562 268.62,-1562 268.62,-1562 94.38,-1562 94.38,-1562 88.38,-1562 82.38,-1556 82.38,-1550 82.38,-1550 82.38,-1329 82.38,-1329 82.38,-1323 88.38,-1317 94.38,-1317"/>
<text text-anchor="middle" x="181.5" y="-1549.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/server/Authentication/infrastructure/gateways</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M102.38,-1447C102.38,-1447 260.62,-1447 260.62,-1447 266.62,-1447 272.62,-1453 272.62,-1459 272.62,-1459 272.62,-1523 272.62,-1523 272.62,-1529 266.62,-1535 260.62,-1535 260.62,-1535 102.38,-1535 102.38,-1535 96.38,-1535 90.38,-1529 90.38,-1523 90.38,-1523 90.38,-1459 90.38,-1459 90.38,-1453 96.38,-1447 102.38,-1447"/>
<text text-anchor="middle" x="181.5" y="-1522.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">gateways</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/server/Authentication/infrastructure/gateways/AuthenticationGateway</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M110.38,-1455C110.38,-1455 252.62,-1455 252.62,-1455 258.62,-1455 264.62,-1461 264.62,-1467 264.62,-1467 264.62,-1496 264.62,-1496 264.62,-1502 258.62,-1508 252.62,-1508 252.62,-1508 110.38,-1508 110.38,-1508 104.38,-1508 98.38,-1502 98.38,-1496 98.38,-1496 98.38,-1467 98.38,-1467 98.38,-1461 104.38,-1455 110.38,-1455"/>
<text text-anchor="middle" x="181.5" y="-1495.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AuthenticationGateway</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/server/Authentication/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M109.5,-1325C109.5,-1325 253.5,-1325 253.5,-1325 259.5,-1325 265.5,-1331 265.5,-1337 265.5,-1337 265.5,-1427 265.5,-1427 265.5,-1433 259.5,-1439 253.5,-1439 253.5,-1439 109.5,-1439 109.5,-1439 103.5,-1439 97.5,-1433 97.5,-1427 97.5,-1427 97.5,-1337 97.5,-1337 97.5,-1331 103.5,-1325 109.5,-1325"/>
<text text-anchor="middle" x="181.5" y="-1426.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/server/Authentication/infrastructure/repositories/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M117.5,-1333C117.5,-1333 245.5,-1333 245.5,-1333 251.5,-1333 257.5,-1339 257.5,-1345 257.5,-1345 257.5,-1374 257.5,-1374 257.5,-1380 251.5,-1386 245.5,-1386 245.5,-1386 117.5,-1386 117.5,-1386 111.5,-1386 105.5,-1380 105.5,-1374 105.5,-1374 105.5,-1345 105.5,-1345 105.5,-1339 111.5,-1333 117.5,-1333"/>
<text text-anchor="middle" x="181.5" y="-1373.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/server/DeckBuilding</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M82.5,-858C82.5,-858 721.25,-858 721.25,-858 727.25,-858 733.25,-864 733.25,-870 733.25,-870 733.25,-1288 733.25,-1288 733.25,-1294 727.25,-1300 721.25,-1300 721.25,-1300 82.5,-1300 82.5,-1300 76.5,-1300 70.5,-1294 70.5,-1288 70.5,-1288 70.5,-870 70.5,-870 70.5,-864 76.5,-858 82.5,-858"/>
<text text-anchor="middle" x="401.88" y="-1287.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilding</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/server/DeckBuilding/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M90.5,-866C90.5,-866 488.25,-866 488.25,-866 494.25,-866 500.25,-872 500.25,-878 500.25,-878 500.25,-1104 500.25,-1104 500.25,-1110 494.25,-1116 488.25,-1116 488.25,-1116 90.5,-1116 90.5,-1116 84.5,-1116 78.5,-1110 78.5,-1104 78.5,-1104 78.5,-878 78.5,-878 78.5,-872 84.5,-866 90.5,-866"/>
<text text-anchor="middle" x="289.38" y="-1103.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/server/DeckBuilding/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M98.5,-874C98.5,-874 480.25,-874 480.25,-874 486.25,-874 492.25,-880 492.25,-886 492.25,-886 492.25,-985 492.25,-985 492.25,-991 486.25,-997 480.25,-997 480.25,-997 98.5,-997 98.5,-997 92.5,-997 86.5,-991 86.5,-985 86.5,-985 86.5,-886 86.5,-886 86.5,-880 92.5,-874 98.5,-874"/>
<text text-anchor="middle" x="289.38" y="-984.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/server/DeckBuilding/application/commands/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M106.5,-882C106.5,-882 472.25,-882 472.25,-882 478.25,-882 484.25,-888 484.25,-894 484.25,-894 484.25,-958 484.25,-958 484.25,-964 478.25,-970 472.25,-970 472.25,-970 106.5,-970 106.5,-970 100.5,-970 94.5,-964 94.5,-958 94.5,-958 94.5,-894 94.5,-894 94.5,-888 100.5,-882 106.5,-882"/>
<text text-anchor="middle" x="289.38" y="-957.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/server/DeckBuilding/application/commands/Deck/SaveDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M114.5,-890C114.5,-890 464.25,-890 464.25,-890 470.25,-890 476.25,-896 476.25,-902 476.25,-902 476.25,-931 476.25,-931 476.25,-937 470.25,-943 464.25,-943 464.25,-943 114.5,-943 114.5,-943 108.5,-943 102.5,-937 102.5,-931 102.5,-931 102.5,-902 102.5,-902 102.5,-896 108.5,-890 114.5,-890"/>
<text text-anchor="middle" x="289.38" y="-930.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeck</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/server/DeckBuilding/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M372.62,-1036C372.62,-1036 454.12,-1036 454.12,-1036 460.12,-1036 466.12,-1042 466.12,-1048 466.12,-1048 466.12,-1077 466.12,-1077 466.12,-1083 460.12,-1089 454.12,-1089 454.12,-1089 372.62,-1089 372.62,-1089 366.62,-1089 360.62,-1083 360.62,-1077 360.62,-1077 360.62,-1048 360.62,-1048 360.62,-1042 366.62,-1036 372.62,-1036"/>
<text text-anchor="middle" x="413.38" y="-1076.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/server/DeckBuilding/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M101,-1005C101,-1005 262,-1005 262,-1005 268,-1005 274,-1011 274,-1017 274,-1017 274,-1077 274,-1077 274,-1083 268,-1089 262,-1089 262,-1089 101,-1089 101,-1089 95,-1089 89,-1083 89,-1077 89,-1077 89,-1017 89,-1017 89,-1011 95,-1005 101,-1005"/>
<text text-anchor="middle" x="181.5" y="-1076.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/server/DeckBuilding/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M580.25,-1011C580.25,-1011 713.25,-1011 713.25,-1011 719.25,-1011 725.25,-1017 725.25,-1023 725.25,-1023 725.25,-1148 725.25,-1148 725.25,-1154 719.25,-1160 713.25,-1160 713.25,-1160 580.25,-1160 580.25,-1160 574.25,-1160 568.25,-1154 568.25,-1148 568.25,-1148 568.25,-1023 568.25,-1023 568.25,-1017 574.25,-1011 580.25,-1011"/>
<text text-anchor="middle" x="646.75" y="-1147.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/server/DeckBuilding/domain/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M588.25,-1019C588.25,-1019 705.25,-1019 705.25,-1019 711.25,-1019 717.25,-1025 717.25,-1031 717.25,-1031 717.25,-1121 717.25,-1121 717.25,-1127 711.25,-1133 705.25,-1133 705.25,-1133 588.25,-1133 588.25,-1133 582.25,-1133 576.25,-1127 576.25,-1121 576.25,-1121 576.25,-1031 576.25,-1031 576.25,-1025 582.25,-1019 588.25,-1019"/>
<text text-anchor="middle" x="646.75" y="-1120.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/server/DeckBuilding/domain/Deck/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M596.25,-1027C596.25,-1027 697.25,-1027 697.25,-1027 703.25,-1027 709.25,-1033 709.25,-1039 709.25,-1039 709.25,-1068 709.25,-1068 709.25,-1074 703.25,-1080 697.25,-1080 697.25,-1080 596.25,-1080 596.25,-1080 590.25,-1080 584.25,-1074 584.25,-1068 584.25,-1068 584.25,-1039 584.25,-1039 584.25,-1033 590.25,-1027 596.25,-1027"/>
<text text-anchor="middle" x="646.75" y="-1067.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/server/DeckBuilding/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M109,-1124C109,-1124 254,-1124 254,-1124 260,-1124 266,-1130 266,-1136 266,-1136 266,-1261 266,-1261 266,-1267 260,-1273 254,-1273 254,-1273 109,-1273 109,-1273 103,-1273 97,-1267 97,-1261 97,-1261 97,-1136 97,-1136 97,-1130 103,-1124 109,-1124"/>
<text text-anchor="middle" x="181.5" y="-1260.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/server/DeckBuilding/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M117,-1132C117,-1132 246,-1132 246,-1132 252,-1132 258,-1138 258,-1144 258,-1144 258,-1234 258,-1234 258,-1240 252,-1246 246,-1246 246,-1246 117,-1246 117,-1246 111,-1246 105,-1240 105,-1234 105,-1234 105,-1144 105,-1144 105,-1138 111,-1132 117,-1132"/>
<text text-anchor="middle" x="181.5" y="-1233.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/server/DeckBuilding/infrastructure/repositories/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M125,-1140C125,-1140 238,-1140 238,-1140 244,-1140 250,-1146 250,-1152 250,-1152 250,-1181 250,-1181 250,-1187 244,-1193 238,-1193 238,-1193 125,-1193 125,-1193 119,-1193 113,-1187 113,-1181 113,-1181 113,-1152 113,-1152 113,-1146 119,-1140 125,-1140"/>
<text text-anchor="middle" x="181.5" y="-1180.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/server/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M78.38,-1601C78.38,-1601 734.75,-1601 734.75,-1601 740.75,-1601 746.75,-1607 746.75,-1613 746.75,-1613 746.75,-2000 746.75,-2000 746.75,-2006 740.75,-2012 734.75,-2012 734.75,-2012 78.38,-2012 78.38,-2012 72.38,-2012 66.38,-2006 66.38,-2000 66.38,-2000 66.38,-1613 66.38,-1613 66.38,-1607 72.38,-1601 78.38,-1601"/>
<text text-anchor="middle" x="406.56" y="-1999.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/server/Gaming/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M86.38,-1609C86.38,-1609 492.38,-1609 492.38,-1609 498.38,-1609 504.38,-1615 504.38,-1621 504.38,-1621 504.38,-1816 504.38,-1816 504.38,-1822 498.38,-1828 492.38,-1828 492.38,-1828 86.38,-1828 86.38,-1828 80.38,-1828 74.38,-1822 74.38,-1816 74.38,-1816 74.38,-1621 74.38,-1621 74.38,-1615 80.38,-1609 86.38,-1609"/>
<text text-anchor="middle" x="289.38" y="-1815.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/server/Gaming/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M94.38,-1617C94.38,-1617 484.38,-1617 484.38,-1617 490.38,-1617 496.38,-1623 496.38,-1629 496.38,-1629 496.38,-1728 496.38,-1728 496.38,-1734 490.38,-1740 484.38,-1740 484.38,-1740 94.38,-1740 94.38,-1740 88.38,-1740 82.38,-1734 82.38,-1728 82.38,-1728 82.38,-1629 82.38,-1629 82.38,-1623 88.38,-1617 94.38,-1617"/>
<text text-anchor="middle" x="289.38" y="-1727.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/server/Gaming/application/commands/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M102.38,-1625C102.38,-1625 476.38,-1625 476.38,-1625 482.38,-1625 488.38,-1631 488.38,-1637 488.38,-1637 488.38,-1701 488.38,-1701 488.38,-1707 482.38,-1713 476.38,-1713 476.38,-1713 102.38,-1713 102.38,-1713 96.38,-1713 90.38,-1707 90.38,-1701 90.38,-1701 90.38,-1637 90.38,-1637 90.38,-1631 96.38,-1625 102.38,-1625"/>
<text text-anchor="middle" x="289.38" y="-1700.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/server/Gaming/application/commands/Match/LeaveMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M110.38,-1633C110.38,-1633 468.38,-1633 468.38,-1633 474.38,-1633 480.38,-1639 480.38,-1645 480.38,-1645 480.38,-1674 480.38,-1674 480.38,-1680 474.38,-1686 468.38,-1686 468.38,-1686 110.38,-1686 110.38,-1686 104.38,-1686 98.38,-1680 98.38,-1674 98.38,-1674 98.38,-1645 98.38,-1645 98.38,-1639 104.38,-1633 110.38,-1633"/>
<text text-anchor="middle" x="289.38" y="-1673.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatch</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/server/Gaming/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M370.75,-1748C370.75,-1748 456,-1748 456,-1748 462,-1748 468,-1754 468,-1760 468,-1760 468,-1789 468,-1789 468,-1795 462,-1801 456,-1801 456,-1801 370.75,-1801 370.75,-1801 364.75,-1801 358.75,-1795 358.75,-1789 358.75,-1789 358.75,-1760 358.75,-1760 358.75,-1754 364.75,-1748 370.75,-1748"/>
<text text-anchor="middle" x="413.38" y="-1788.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/server/Gaming/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M142.62,-1748C142.62,-1748 220.38,-1748 220.38,-1748 226.38,-1748 232.38,-1754 232.38,-1760 232.38,-1760 232.38,-1789 232.38,-1789 232.38,-1795 226.38,-1801 220.38,-1801 220.38,-1801 142.62,-1801 142.62,-1801 136.62,-1801 130.62,-1795 130.62,-1789 130.62,-1789 130.62,-1760 130.62,-1760 130.62,-1754 136.62,-1748 142.62,-1748"/>
<text text-anchor="middle" x="181.5" y="-1788.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/server/Gaming/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M566.75,-1692C566.75,-1692 726.75,-1692 726.75,-1692 732.75,-1692 738.75,-1698 738.75,-1704 738.75,-1704 738.75,-1860 738.75,-1860 738.75,-1866 732.75,-1872 726.75,-1872 726.75,-1872 566.75,-1872 566.75,-1872 560.75,-1872 554.75,-1866 554.75,-1860 554.75,-1860 554.75,-1704 554.75,-1704 554.75,-1698 560.75,-1692 566.75,-1692"/>
<text text-anchor="middle" x="646.75" y="-1859.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/server/Gaming/domain/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M574.75,-1700C574.75,-1700 718.75,-1700 718.75,-1700 724.75,-1700 730.75,-1706 730.75,-1712 730.75,-1712 730.75,-1833 730.75,-1833 730.75,-1839 724.75,-1845 718.75,-1845 718.75,-1845 574.75,-1845 574.75,-1845 568.75,-1845 562.75,-1839 562.75,-1833 562.75,-1833 562.75,-1712 562.75,-1712 562.75,-1706 568.75,-1700 574.75,-1700"/>
<text text-anchor="middle" x="646.75" y="-1832.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/server/Gaming/domain/Match/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M582.75,-1708C582.75,-1708 710.75,-1708 710.75,-1708 716.75,-1708 722.75,-1714 722.75,-1720 722.75,-1720 722.75,-1780 722.75,-1780 722.75,-1786 716.75,-1792 710.75,-1792 710.75,-1792 582.75,-1792 582.75,-1792 576.75,-1792 570.75,-1786 570.75,-1780 570.75,-1780 570.75,-1720 570.75,-1720 570.75,-1714 576.75,-1708 582.75,-1708"/>
<text text-anchor="middle" x="646.75" y="-1779.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/server/Gaming/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M107.12,-1836C107.12,-1836 255.88,-1836 255.88,-1836 261.88,-1836 267.88,-1842 267.88,-1848 267.88,-1848 267.88,-1973 267.88,-1973 267.88,-1979 261.88,-1985 255.88,-1985 255.88,-1985 107.12,-1985 107.12,-1985 101.12,-1985 95.12,-1979 95.12,-1973 95.12,-1973 95.12,-1848 95.12,-1848 95.12,-1842 101.12,-1836 107.12,-1836"/>
<text text-anchor="middle" x="181.5" y="-1972.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust53" class="cluster">
<title>cluster_src/server/Gaming/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M115.12,-1844C115.12,-1844 247.88,-1844 247.88,-1844 253.88,-1844 259.88,-1850 259.88,-1856 259.88,-1856 259.88,-1946 259.88,-1946 259.88,-1952 253.88,-1958 247.88,-1958 247.88,-1958 115.12,-1958 115.12,-1958 109.12,-1958 103.12,-1952 103.12,-1946 103.12,-1946 103.12,-1856 103.12,-1856 103.12,-1850 109.12,-1844 115.12,-1844"/>
<text text-anchor="middle" x="181.5" y="-1945.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust54" class="cluster">
<title>cluster_src/server/Gaming/infrastructure/repositories/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M123.12,-1852C123.12,-1852 239.88,-1852 239.88,-1852 245.88,-1852 251.88,-1858 251.88,-1864 251.88,-1864 251.88,-1893 251.88,-1893 251.88,-1899 245.88,-1905 239.88,-1905 239.88,-1905 123.12,-1905 123.12,-1905 117.12,-1905 111.12,-1899 111.12,-1893 111.12,-1893 111.12,-1864 111.12,-1864 111.12,-1858 117.12,-1852 123.12,-1852"/>
<text text-anchor="middle" x="181.5" y="-1892.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust55" class="cluster">
<title>cluster_src/server/MatchMaking</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M36,-2336C36,-2336 891.5,-2336 891.5,-2336 897.5,-2336 903.5,-2342 903.5,-2348 903.5,-2348 903.5,-3040 903.5,-3040 903.5,-3046 897.5,-3052 891.5,-3052 891.5,-3052 36,-3052 36,-3052 30,-3052 24,-3046 24,-3040 24,-3040 24,-2348 24,-2348 24,-2342 30,-2336 36,-2336"/>
<text text-anchor="middle" x="463.75" y="-3039.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMaking</text>
</g>
<g id="clust56" class="cluster">
<title>cluster_src/server/MatchMaking/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M44,-2344C44,-2344 534.75,-2344 534.75,-2344 540.75,-2344 546.75,-2350 546.75,-2356 546.75,-2356 546.75,-2856 546.75,-2856 546.75,-2862 540.75,-2868 534.75,-2868 534.75,-2868 44,-2868 44,-2868 38,-2868 32,-2862 32,-2856 32,-2856 32,-2356 32,-2356 32,-2350 38,-2344 44,-2344"/>
<text text-anchor="middle" x="289.38" y="-2855.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust57" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M52,-2352C52,-2352 526.75,-2352 526.75,-2352 532.75,-2352 538.75,-2358 538.75,-2364 538.75,-2364 538.75,-2768 538.75,-2768 538.75,-2774 532.75,-2780 526.75,-2780 526.75,-2780 52,-2780 52,-2780 46,-2780 40,-2774 40,-2768 40,-2768 40,-2364 40,-2364 40,-2358 46,-2352 52,-2352"/>
<text text-anchor="middle" x="289.38" y="-2767.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust58" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M60,-2360C60,-2360 518.75,-2360 518.75,-2360 524.75,-2360 530.75,-2366 530.75,-2372 530.75,-2372 530.75,-2741 530.75,-2741 530.75,-2747 524.75,-2753 518.75,-2753 518.75,-2753 60,-2753 60,-2753 54,-2753 48,-2747 48,-2741 48,-2741 48,-2372 48,-2372 48,-2366 54,-2360 60,-2360"/>
<text text-anchor="middle" x="289.38" y="-2740.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMaking</text>
</g>
<g id="clust59" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M68,-2490C68,-2490 510.75,-2490 510.75,-2490 516.75,-2490 522.75,-2496 522.75,-2502 522.75,-2502 522.75,-2531 522.75,-2531 522.75,-2537 516.75,-2543 510.75,-2543 510.75,-2543 68,-2543 68,-2543 62,-2543 56,-2537 56,-2531 56,-2531 56,-2502 56,-2502 56,-2496 62,-2490 68,-2490"/>
<text text-anchor="middle" x="289.38" y="-2530.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AddPlayerToMatchMakingQueue</text>
</g>
<g id="clust60" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M83.75,-2612C83.75,-2612 495,-2612 495,-2612 501,-2612 507,-2618 507,-2624 507,-2624 507,-2653 507,-2653 507,-2659 501,-2665 495,-2665 495,-2665 83.75,-2665 83.75,-2665 77.75,-2665 71.75,-2659 71.75,-2653 71.75,-2653 71.75,-2624 71.75,-2624 71.75,-2618 77.75,-2612 83.75,-2612"/>
<text text-anchor="middle" x="289.38" y="-2652.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CancelMatchRegistration</text>
</g>
<g id="clust61" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M75.88,-2368C75.88,-2368 502.88,-2368 502.88,-2368 508.88,-2368 514.88,-2374 514.88,-2380 514.88,-2380 514.88,-2409 514.88,-2409 514.88,-2415 508.88,-2421 502.88,-2421 502.88,-2421 75.88,-2421 75.88,-2421 69.88,-2421 63.88,-2415 63.88,-2409 63.88,-2409 63.88,-2380 63.88,-2380 63.88,-2374 69.88,-2368 75.88,-2368"/>
<text text-anchor="middle" x="289.38" y="-2408.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CleanUpMatchMakingQueue</text>
</g>
<g id="clust62" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking/MakeMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M111.88,-2429C111.88,-2429 466.88,-2429 466.88,-2429 472.88,-2429 478.88,-2435 478.88,-2441 478.88,-2441 478.88,-2470 478.88,-2470 478.88,-2476 472.88,-2482 466.88,-2482 466.88,-2482 111.88,-2482 111.88,-2482 105.88,-2482 99.88,-2476 99.88,-2470 99.88,-2470 99.88,-2441 99.88,-2441 99.88,-2435 105.88,-2429 111.88,-2429"/>
<text text-anchor="middle" x="289.38" y="-2469.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MakeMatch</text>
</g>
<g id="clust63" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M92.38,-2551C92.38,-2551 486.38,-2551 486.38,-2551 492.38,-2551 498.38,-2557 498.38,-2563 498.38,-2563 498.38,-2592 498.38,-2592 498.38,-2598 492.38,-2604 486.38,-2604 486.38,-2604 92.38,-2604 92.38,-2604 86.38,-2604 80.38,-2598 80.38,-2592 80.38,-2592 80.38,-2563 80.38,-2563 80.38,-2557 86.38,-2551 92.38,-2551"/>
<text text-anchor="middle" x="289.38" y="-2591.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdatePlayersStatus</text>
</g>
<g id="clust64" class="cluster">
<title>cluster_src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M81.5,-2673C81.5,-2673 497.25,-2673 497.25,-2673 503.25,-2673 509.25,-2679 509.25,-2685 509.25,-2685 509.25,-2714 509.25,-2714 509.25,-2720 503.25,-2726 497.25,-2726 497.25,-2726 81.5,-2726 81.5,-2726 75.5,-2726 69.5,-2720 69.5,-2714 69.5,-2714 69.5,-2685 69.5,-2685 69.5,-2679 75.5,-2673 81.5,-2673"/>
<text text-anchor="middle" x="289.38" y="-2713.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdateSinglePlayerStatus</text>
</g>
<g id="clust65" class="cluster">
<title>cluster_src/server/MatchMaking/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M341.88,-2788C341.88,-2788 484.88,-2788 484.88,-2788 490.88,-2788 496.88,-2794 496.88,-2800 496.88,-2800 496.88,-2829 496.88,-2829 496.88,-2835 490.88,-2841 484.88,-2841 484.88,-2841 341.88,-2841 341.88,-2841 335.88,-2841 329.88,-2835 329.88,-2829 329.88,-2829 329.88,-2800 329.88,-2800 329.88,-2794 335.88,-2788 341.88,-2788"/>
<text text-anchor="middle" x="413.38" y="-2828.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust66" class="cluster">
<title>cluster_src/server/MatchMaking/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M589,-2854C589,-2854 883.5,-2854 883.5,-2854 889.5,-2854 895.5,-2860 895.5,-2866 895.5,-2866 895.5,-2930 895.5,-2930 895.5,-2936 889.5,-2942 883.5,-2942 883.5,-2942 589,-2942 589,-2942 583,-2942 577,-2936 577,-2930 577,-2930 577,-2866 577,-2866 577,-2860 583,-2854 589,-2854"/>
<text text-anchor="middle" x="736.25" y="-2929.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust67" class="cluster">
<title>cluster_src/server/MatchMaking/domain/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M597,-2862C597,-2862 875.5,-2862 875.5,-2862 881.5,-2862 887.5,-2868 887.5,-2874 887.5,-2874 887.5,-2903 887.5,-2903 887.5,-2909 881.5,-2915 875.5,-2915 875.5,-2915 597,-2915 597,-2915 591,-2915 585,-2909 585,-2903 585,-2903 585,-2874 585,-2874 585,-2868 591,-2862 597,-2862"/>
<text text-anchor="middle" x="736.25" y="-2902.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<g id="clust68" class="cluster">
<title>cluster_src/server/MatchMaking/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M78.25,-2876C78.25,-2876 284.75,-2876 284.75,-2876 290.75,-2876 296.75,-2882 296.75,-2888 296.75,-2888 296.75,-3013 296.75,-3013 296.75,-3019 290.75,-3025 284.75,-3025 284.75,-3025 78.25,-3025 78.25,-3025 72.25,-3025 66.25,-3019 66.25,-3013 66.25,-3013 66.25,-2888 66.25,-2888 66.25,-2882 72.25,-2876 78.25,-2876"/>
<text text-anchor="middle" x="181.5" y="-3012.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust69" class="cluster">
<title>cluster_src/server/MatchMaking/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M86.25,-2884C86.25,-2884 276.75,-2884 276.75,-2884 282.75,-2884 288.75,-2890 288.75,-2896 288.75,-2896 288.75,-2986 288.75,-2986 288.75,-2992 282.75,-2998 276.75,-2998 276.75,-2998 86.25,-2998 86.25,-2998 80.25,-2998 74.25,-2992 74.25,-2986 74.25,-2986 74.25,-2896 74.25,-2896 74.25,-2890 80.25,-2884 86.25,-2884"/>
<text text-anchor="middle" x="181.5" y="-2985.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust70" class="cluster">
<title>cluster_src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M94.25,-2918C94.25,-2918 268.75,-2918 268.75,-2918 274.75,-2918 280.75,-2924 280.75,-2930 280.75,-2930 280.75,-2959 280.75,-2959 280.75,-2965 274.75,-2971 268.75,-2971 268.75,-2971 94.25,-2971 94.25,-2971 88.25,-2971 82.25,-2965 82.25,-2959 82.25,-2959 82.25,-2930 82.25,-2930 82.25,-2924 88.25,-2918 94.25,-2918"/>
<text text-anchor="middle" x="181.5" y="-2958.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<g id="clust71" class="cluster">
<title>cluster_src/server/Shared</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M113.75,-2020C113.75,-2020 469.38,-2020 469.38,-2020 475.38,-2020 481.38,-2026 481.38,-2032 481.38,-2032 481.38,-2316 481.38,-2316 481.38,-2322 475.38,-2328 469.38,-2328 469.38,-2328 113.75,-2328 113.75,-2328 107.75,-2328 101.75,-2322 101.75,-2316 101.75,-2316 101.75,-2032 101.75,-2032 101.75,-2026 107.75,-2020 113.75,-2020"/>
<text text-anchor="middle" x="291.56" y="-2315.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Shared</text>
</g>
<g id="clust72" class="cluster">
<title>cluster_src/server/Shared/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M365.38,-2028C365.38,-2028 461.38,-2028 461.38,-2028 467.38,-2028 473.38,-2034 473.38,-2040 473.38,-2040 473.38,-2289 473.38,-2289 473.38,-2295 467.38,-2301 461.38,-2301 461.38,-2301 365.38,-2301 365.38,-2301 359.38,-2301 353.38,-2295 353.38,-2289 353.38,-2289 353.38,-2040 353.38,-2040 353.38,-2034 359.38,-2028 365.38,-2028"/>
<text text-anchor="middle" x="413.38" y="-2288.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust73" class="cluster">
<title>cluster_src/server/Shared/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M373.38,-2036C373.38,-2036 453.38,-2036 453.38,-2036 459.38,-2036 465.38,-2042 465.38,-2048 465.38,-2048 465.38,-2170 465.38,-2170 465.38,-2176 459.38,-2182 453.38,-2182 453.38,-2182 373.38,-2182 373.38,-2182 367.38,-2182 361.38,-2176 361.38,-2170 361.38,-2170 361.38,-2048 361.38,-2048 361.38,-2042 367.38,-2036 373.38,-2036"/>
<text text-anchor="middle" x="413.38" y="-2169.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust74" class="cluster">
<title>cluster_src/server/Shared/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M374.5,-2190C374.5,-2190 452.25,-2190 452.25,-2190 458.25,-2190 464.25,-2196 464.25,-2202 464.25,-2202 464.25,-2262 464.25,-2262 464.25,-2268 458.25,-2274 452.25,-2274 452.25,-2274 374.5,-2274 374.5,-2274 368.5,-2274 362.5,-2268 362.5,-2262 362.5,-2262 362.5,-2202 362.5,-2202 362.5,-2196 368.5,-2190 374.5,-2190"/>
<text text-anchor="middle" x="413.38" y="-2261.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust75" class="cluster">
<title>cluster_src/server/Shared/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M121.75,-2028C121.75,-2028 241.25,-2028 241.25,-2028 247.25,-2028 253.25,-2034 253.25,-2040 253.25,-2040 253.25,-2261 253.25,-2261 253.25,-2267 247.25,-2273 241.25,-2273 241.25,-2273 121.75,-2273 121.75,-2273 115.75,-2273 109.75,-2267 109.75,-2261 109.75,-2261 109.75,-2040 109.75,-2040 109.75,-2034 115.75,-2028 121.75,-2028"/>
<text text-anchor="middle" x="181.5" y="-2260.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust76" class="cluster">
<title>cluster_src/server/Shared/infrastructure/IdentityProvider</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M131.75,-2036C131.75,-2036 231.25,-2036 231.25,-2036 237.25,-2036 243.25,-2042 243.25,-2048 243.25,-2048 243.25,-2077 243.25,-2077 243.25,-2083 237.25,-2089 231.25,-2089 231.25,-2089 131.75,-2089 131.75,-2089 125.75,-2089 119.75,-2083 119.75,-2077 119.75,-2077 119.75,-2048 119.75,-2048 119.75,-2042 125.75,-2036 131.75,-2036"/>
<text text-anchor="middle" x="181.5" y="-2076.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">IdentityProvider</text>
</g>
<g id="clust77" class="cluster">
<title>cluster_src/server/Shared/infrastructure/gateways</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M129.75,-2097C129.75,-2097 233.25,-2097 233.25,-2097 239.25,-2097 245.25,-2103 245.25,-2109 245.25,-2109 245.25,-2234 245.25,-2234 245.25,-2240 239.25,-2246 233.25,-2246 233.25,-2246 129.75,-2246 129.75,-2246 123.75,-2246 117.75,-2240 117.75,-2234 117.75,-2234 117.75,-2109 117.75,-2109 117.75,-2103 123.75,-2097 129.75,-2097"/>
<text text-anchor="middle" x="181.5" y="-2233.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">gateways</text>
</g>
<g id="clust78" class="cluster">
<title>cluster_src/server/Shared/infrastructure/gateways/Context</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M141.5,-2166C141.5,-2166 221.5,-2166 221.5,-2166 227.5,-2166 233.5,-2172 233.5,-2178 233.5,-2178 233.5,-2207 233.5,-2207 233.5,-2213 227.5,-2219 221.5,-2219 221.5,-2219 141.5,-2219 141.5,-2219 135.5,-2219 129.5,-2213 129.5,-2207 129.5,-2207 129.5,-2178 129.5,-2178 129.5,-2172 135.5,-2166 141.5,-2166"/>
<text text-anchor="middle" x="181.5" y="-2206.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Context</text>
</g>
<g id="clust79" class="cluster">
<title>cluster_src/server/Shared/infrastructure/gateways/Time</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M137.75,-2105C137.75,-2105 225.25,-2105 225.25,-2105 231.25,-2105 237.25,-2111 237.25,-2117 237.25,-2117 237.25,-2146 237.25,-2146 237.25,-2152 231.25,-2158 225.25,-2158 225.25,-2158 137.75,-2158 137.75,-2158 131.75,-2158 125.75,-2152 125.75,-2146 125.75,-2146 125.75,-2117 125.75,-2117 125.75,-2111 131.75,-2105 137.75,-2105"/>
<text text-anchor="middle" x="181.5" y="-2145.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Time</text>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx -->
<g id="node1" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx</title>
<g id="a_node1"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/loading.tsx" xlink:title="loading.tsx">
<path fill="#ccffcc" stroke="black" d="M205.46,-82.25C205.46,-82.25 157.54,-82.25 157.54,-82.25 154.46,-82.25 151.38,-79.17 151.38,-76.08 151.38,-76.08 151.38,-69.92 151.38,-69.92 151.38,-66.83 154.46,-63.75 157.54,-63.75 157.54,-63.75 205.46,-63.75 205.46,-63.75 208.54,-63.75 211.62,-66.83 211.62,-69.92 211.62,-69.92 211.62,-76.08 211.62,-76.08 211.62,-79.17 208.54,-82.25 205.46,-82.25"/>
<text text-anchor="start" x="159.38" y="-69.7" font-family="Helvetica,sans-Serif" font-size="9.00">loading.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx -->
<g id="node2" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx</title>
<g id="a_node2"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-113.25C202.33,-113.25 160.67,-113.25 160.67,-113.25 157.58,-113.25 154.5,-110.17 154.5,-107.08 154.5,-107.08 154.5,-100.92 154.5,-100.92 154.5,-97.83 157.58,-94.75 160.67,-94.75 160.67,-94.75 202.33,-94.75 202.33,-94.75 205.42,-94.75 208.5,-97.83 208.5,-100.92 208.5,-100.92 208.5,-107.08 208.5,-107.08 208.5,-110.17 205.42,-113.25 202.33,-113.25"/>
<text text-anchor="start" x="164.25" y="-100.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx -->
<g id="node3" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx</title>
<g id="a_node3"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/layout.tsx" xlink:title="layout.tsx">
<path fill="#ccffcc" stroke="black" d="M202.46,-166.25C202.46,-166.25 160.54,-166.25 160.54,-166.25 157.46,-166.25 154.38,-163.17 154.38,-160.08 154.38,-160.08 154.38,-153.92 154.38,-153.92 154.38,-150.83 157.46,-147.75 160.54,-147.75 160.54,-147.75 202.46,-147.75 202.46,-147.75 205.54,-147.75 208.62,-150.83 208.62,-153.92 208.62,-153.92 208.62,-160.08 208.62,-160.08 208.62,-163.17 205.54,-166.25 202.46,-166.25"/>
<text text-anchor="start" x="162.38" y="-153.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx -->
<g id="node4" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx</title>
<g id="a_node4"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-197.25C202.33,-197.25 160.67,-197.25 160.67,-197.25 157.58,-197.25 154.5,-194.17 154.5,-191.08 154.5,-191.08 154.5,-184.92 154.5,-184.92 154.5,-181.83 157.58,-178.75 160.67,-178.75 160.67,-178.75 202.33,-178.75 202.33,-178.75 205.42,-178.75 208.5,-181.83 208.5,-184.92 208.5,-184.92 208.5,-191.08 208.5,-191.08 208.5,-194.17 205.42,-197.25 202.33,-197.25"/>
<text text-anchor="start" x="164.25" y="-184.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx -->
<g id="node5" class="node">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx</title>
<g id="a_node5"><a xlink:href="app/[locale]/(connected)/games/[gameId]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-311.25C202.33,-311.25 160.67,-311.25 160.67,-311.25 157.58,-311.25 154.5,-308.17 154.5,-305.08 154.5,-305.08 154.5,-298.92 154.5,-298.92 154.5,-295.83 157.58,-292.75 160.67,-292.75 160.67,-292.75 202.33,-292.75 202.33,-292.75 205.42,-292.75 208.5,-295.83 208.5,-298.92 208.5,-298.92 208.5,-305.08 208.5,-305.08 208.5,-308.17 205.42,-311.25 202.33,-311.25"/>
<text text-anchor="start" x="164.25" y="-298.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx -->
<g id="node6" class="node">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx</title>
<g id="a_node6"><a xlink:href="app/[locale]/(connected)/games/[gameId]/play/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-258.25C202.33,-258.25 160.67,-258.25 160.67,-258.25 157.58,-258.25 154.5,-255.17 154.5,-252.08 154.5,-252.08 154.5,-245.92 154.5,-245.92 154.5,-242.83 157.58,-239.75 160.67,-239.75 160.67,-239.75 202.33,-239.75 202.33,-239.75 205.42,-239.75 208.5,-242.83 208.5,-245.92 208.5,-245.92 208.5,-252.08 208.5,-252.08 208.5,-255.17 205.42,-258.25 202.33,-258.25"/>
<text text-anchor="start" x="164.25" y="-245.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/page.tsx -->
<g id="node7" class="node">
<title>app/[locale]/(connected)/games/page.tsx</title>
<g id="a_node7"><a xlink:href="app/[locale]/(connected)/games/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-364.25C202.33,-364.25 160.67,-364.25 160.67,-364.25 157.58,-364.25 154.5,-361.17 154.5,-358.08 154.5,-358.08 154.5,-351.92 154.5,-351.92 154.5,-348.83 157.58,-345.75 160.67,-345.75 160.67,-345.75 202.33,-345.75 202.33,-345.75 205.42,-345.75 208.5,-348.83 208.5,-351.92 208.5,-351.92 208.5,-358.08 208.5,-358.08 208.5,-361.17 205.42,-364.25 202.33,-364.25"/>
<text text-anchor="start" x="164.25" y="-351.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx -->
<g id="node8" class="node">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx</title>
<g id="a_node8"><a xlink:href="app/[locale]/(connected)/matches/[matchId]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-433.25C202.33,-433.25 160.67,-433.25 160.67,-433.25 157.58,-433.25 154.5,-430.17 154.5,-427.08 154.5,-427.08 154.5,-420.92 154.5,-420.92 154.5,-417.83 157.58,-414.75 160.67,-414.75 160.67,-414.75 202.33,-414.75 202.33,-414.75 205.42,-414.75 208.5,-417.83 208.5,-420.92 208.5,-420.92 208.5,-427.08 208.5,-427.08 208.5,-430.17 205.42,-433.25 202.33,-433.25"/>
<text text-anchor="start" x="164.25" y="-420.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx -->
<g id="node9" class="node">
<title>app/[locale]/(not&#45;connected)/layout.tsx</title>
<g id="a_node9"><a xlink:href="app/[locale]/(not-connected)/layout.tsx" xlink:title="layout.tsx">
<path fill="#ccffcc" stroke="black" d="M202.46,-609.25C202.46,-609.25 160.54,-609.25 160.54,-609.25 157.46,-609.25 154.38,-606.17 154.38,-603.08 154.38,-603.08 154.38,-596.92 154.38,-596.92 154.38,-593.83 157.46,-590.75 160.54,-590.75 160.54,-590.75 202.46,-590.75 202.46,-590.75 205.54,-590.75 208.62,-593.83 208.62,-596.92 208.62,-596.92 208.62,-603.08 208.62,-603.08 208.62,-606.17 205.54,-609.25 202.46,-609.25"/>
<text text-anchor="start" x="162.38" y="-596.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx -->
<g id="node10" class="node">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx</title>
<g id="a_node10"><a xlink:href="app/[locale]/(not-connected)/signin/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-556.25C202.33,-556.25 160.67,-556.25 160.67,-556.25 157.58,-556.25 154.5,-553.17 154.5,-550.08 154.5,-550.08 154.5,-543.92 154.5,-543.92 154.5,-540.83 157.58,-537.75 160.67,-537.75 160.67,-537.75 202.33,-537.75 202.33,-537.75 205.42,-537.75 208.5,-540.83 208.5,-543.92 208.5,-543.92 208.5,-550.08 208.5,-550.08 208.5,-553.17 205.42,-556.25 202.33,-556.25"/>
<text text-anchor="start" x="164.25" y="-543.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx -->
<g id="node11" class="node">
<title>app/[locale]/access&#45;denied/page.tsx</title>
<g id="a_node11"><a xlink:href="app/[locale]/access-denied/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-670.25C202.33,-670.25 160.67,-670.25 160.67,-670.25 157.58,-670.25 154.5,-667.17 154.5,-664.08 154.5,-664.08 154.5,-657.92 154.5,-657.92 154.5,-654.83 157.58,-651.75 160.67,-651.75 160.67,-651.75 202.33,-651.75 202.33,-651.75 205.42,-651.75 208.5,-654.83 208.5,-657.92 208.5,-657.92 208.5,-664.08 208.5,-664.08 208.5,-667.17 205.42,-670.25 202.33,-670.25"/>
<text text-anchor="start" x="164.25" y="-657.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/page.tsx -->
<g id="node12" class="node">
<title>app/[locale]/page.tsx</title>
<g id="a_node12"><a xlink:href="app/[locale]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-723.25C202.33,-723.25 160.67,-723.25 160.67,-723.25 157.58,-723.25 154.5,-720.17 154.5,-717.08 154.5,-717.08 154.5,-710.92 154.5,-710.92 154.5,-707.83 157.58,-704.75 160.67,-704.75 160.67,-704.75 202.33,-704.75 202.33,-704.75 205.42,-704.75 208.5,-707.83 208.5,-710.92 208.5,-710.92 208.5,-717.08 208.5,-717.08 208.5,-720.17 205.42,-723.25 202.33,-723.25"/>
<text text-anchor="start" x="164.25" y="-710.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/globals.css -->
<g id="node13" class="node">
<title>app/globals.css</title>
<g id="a_node13"><a xlink:href="app/globals.css" xlink:title="globals.css">
<path fill="#ffffcc" stroke="black" d="M438.08,-776.25C438.08,-776.25 388.67,-776.25 388.67,-776.25 385.58,-776.25 382.5,-773.17 382.5,-770.08 382.5,-770.08 382.5,-763.92 382.5,-763.92 382.5,-760.83 385.58,-757.75 388.67,-757.75 388.67,-757.75 438.08,-757.75 438.08,-757.75 441.17,-757.75 444.25,-760.83 444.25,-763.92 444.25,-763.92 444.25,-770.08 444.25,-770.08 444.25,-773.17 441.17,-776.25 438.08,-776.25"/>
<text text-anchor="start" x="390.5" y="-763.7" font-family="Helvetica,sans-Serif" font-size="9.00">globals.css</text>
</a>
</g>
</g>
<!-- app/layout.tsx -->
<g id="node14" class="node">
<title>app/layout.tsx</title>
<g id="a_node14"><a xlink:href="app/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M202.46,-776.25C202.46,-776.25 160.54,-776.25 160.54,-776.25 157.46,-776.25 154.38,-773.17 154.38,-770.08 154.38,-770.08 154.38,-763.92 154.38,-763.92 154.38,-760.83 157.46,-757.75 160.54,-757.75 160.54,-757.75 202.46,-757.75 202.46,-757.75 205.54,-757.75 208.62,-760.83 208.62,-763.92 208.62,-763.92 208.62,-770.08 208.62,-770.08 208.62,-773.17 205.54,-776.25 202.46,-776.25"/>
<text text-anchor="start" x="162.38" y="-763.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;app/globals.css -->
<g id="edge1" class="edge">
<title>app/layout.tsx&#45;&gt;app/globals.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M208.87,-767C249.22,-767 326.54,-767 373.38,-767"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="373.3,-769.1 379.3,-767 373.3,-764.9 373.3,-769.1"/>
</g>
<!-- app/page.tsx -->
<g id="node15" class="node">
<title>app/page.tsx</title>
<g id="a_node15"><a xlink:href="app/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M202.33,-807.25C202.33,-807.25 160.67,-807.25 160.67,-807.25 157.58,-807.25 154.5,-804.17 154.5,-801.08 154.5,-801.08 154.5,-794.92 154.5,-794.92 154.5,-791.83 157.58,-788.75 160.67,-788.75 160.67,-788.75 202.33,-788.75 202.33,-788.75 205.42,-788.75 208.5,-791.83 208.5,-794.92 208.5,-794.92 208.5,-801.08 208.5,-801.08 208.5,-804.17 205.42,-807.25 202.33,-807.25"/>
<text text-anchor="start" x="164.25" y="-794.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/server/Authentication/application/ports/AppUserRepository.ts -->
<g id="node16" class="node">
<title>src/server/Authentication/application/ports/AppUserRepository.ts</title>
<g id="a_node16"><a xlink:href="src/server/Authentication/application/ports/AppUserRepository.ts" xlink:title="AppUserRepository.ts">
<path fill="#ddfeff" stroke="black" d="M459.46,-1512.25C459.46,-1512.25 367.29,-1512.25 367.29,-1512.25 364.21,-1512.25 361.12,-1509.17 361.12,-1506.08 361.12,-1506.08 361.12,-1499.92 361.12,-1499.92 361.12,-1496.83 364.21,-1493.75 367.29,-1493.75 367.29,-1493.75 459.46,-1493.75 459.46,-1493.75 462.54,-1493.75 465.62,-1496.83 465.62,-1499.92 465.62,-1499.92 465.62,-1506.08 465.62,-1506.08 465.62,-1509.17 462.54,-1512.25 459.46,-1512.25"/>
<text text-anchor="start" x="369.12" y="-1499.7" font-family="Helvetica,sans-Serif" font-size="9.00">AppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/domain/AppUser/AppUser.ts -->
<g id="node17" class="node">
<title>src/server/Authentication/domain/AppUser/AppUser.ts</title>
<g id="a_node17"><a xlink:href="src/server/Authentication/domain/AppUser/AppUser.ts" xlink:title="AppUser.ts">
<path fill="#ddfeff" stroke="black" d="M670.71,-1479.25C670.71,-1479.25 622.79,-1479.25 622.79,-1479.25 619.71,-1479.25 616.62,-1476.17 616.62,-1473.08 616.62,-1473.08 616.62,-1466.92 616.62,-1466.92 616.62,-1463.83 619.71,-1460.75 622.79,-1460.75 622.79,-1460.75 670.71,-1460.75 670.71,-1460.75 673.79,-1460.75 676.88,-1463.83 676.88,-1466.92 676.88,-1466.92 676.88,-1473.08 676.88,-1473.08 676.88,-1476.17 673.79,-1479.25 670.71,-1479.25"/>
<text text-anchor="start" x="624.62" y="-1466.7" font-family="Helvetica,sans-Serif" font-size="9.00">AppUser.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/application/ports/AppUserRepository.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts -->
<g id="edge2" class="edge">
<title>src/server/Authentication/application/ports/AppUserRepository.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M466,-1497.36C490.51,-1494.53 520.18,-1490.88 546.75,-1487 566.92,-1484.05 589.31,-1480.25 607.68,-1476.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="607.84,-1479.1 613.37,-1475.98 607.1,-1474.96 607.84,-1479.1"/>
</g>
<!-- src/server/Authentication/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="node19" class="node">
<title>src/server/Authentication/domain/AppUser/valueObjects/AppUserId.ts</title>
<g id="a_node19"><a xlink:href="src/server/Authentication/domain/AppUser/valueObjects/AppUserId.ts" xlink:title="AppUserId.ts">
<path fill="#ddfeff" stroke="black" d="M845.21,-1485.25C845.21,-1485.25 789.04,-1485.25 789.04,-1485.25 785.96,-1485.25 782.88,-1482.17 782.88,-1479.08 782.88,-1479.08 782.88,-1472.92 782.88,-1472.92 782.88,-1469.83 785.96,-1466.75 789.04,-1466.75 789.04,-1466.75 845.21,-1466.75 845.21,-1466.75 848.29,-1466.75 851.38,-1469.83 851.38,-1472.92 851.38,-1472.92 851.38,-1479.08 851.38,-1479.08 851.38,-1482.17 848.29,-1485.25 845.21,-1485.25"/>
<text text-anchor="start" x="790.88" y="-1472.7" font-family="Helvetica,sans-Serif" font-size="9.00">AppUserId.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/domain/AppUser/AppUser.ts&#45;&gt;src/server/Authentication/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="edge4" class="edge">
<title>src/server/Authentication/domain/AppUser/AppUser.ts&#45;&gt;src/server/Authentication/domain/AppUser/valueObjects/AppUserId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M677.08,-1471.05C703.83,-1472 743.68,-1473.42 773.87,-1474.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="773.41,-1476.58 779.48,-1474.69 773.56,-1472.38 773.41,-1476.58"/>
</g>
<!-- src/server/Authentication/application/ports/AuthenticationGateway.ts -->
<g id="node18" class="node">
<title>src/server/Authentication/application/ports/AuthenticationGateway.ts</title>
<g id="a_node18"><a xlink:href="src/server/Authentication/application/ports/AuthenticationGateway.ts" xlink:title="AuthenticationGateway.ts">
<path fill="#ddfeff" stroke="black" d="M466.58,-1481.25C466.58,-1481.25 360.17,-1481.25 360.17,-1481.25 357.08,-1481.25 354,-1478.17 354,-1475.08 354,-1475.08 354,-1468.92 354,-1468.92 354,-1465.83 357.08,-1462.75 360.17,-1462.75 360.17,-1462.75 466.58,-1462.75 466.58,-1462.75 469.67,-1462.75 472.75,-1465.83 472.75,-1468.92 472.75,-1468.92 472.75,-1475.08 472.75,-1475.08 472.75,-1478.17 469.67,-1481.25 466.58,-1481.25"/>
<text text-anchor="start" x="362" y="-1468.7" font-family="Helvetica,sans-Serif" font-size="9.00">AuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts -->
<g id="edge3" class="edge">
<title>src/server/Authentication/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M473.06,-1471.49C515.27,-1471.13 570.98,-1470.65 607.55,-1470.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="607.41,-1472.43 613.39,-1470.28 607.38,-1468.23 607.41,-1472.43"/>
</g>
<!-- src/server/Authentication/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="node20" class="node">
<title>src/server/Authentication/domain/User/errors/UserNotAuthenticatedError.ts</title>
<g id="a_node20"><a xlink:href="src/server/Authentication/domain/User/errors/UserNotAuthenticatedError.ts" xlink:title="UserNotAuthenticatedError.ts">
<path fill="#ddfeff" stroke="black" d="M707.83,-1358.25C707.83,-1358.25 585.67,-1358.25 585.67,-1358.25 582.58,-1358.25 579.5,-1355.17 579.5,-1352.08 579.5,-1352.08 579.5,-1345.92 579.5,-1345.92 579.5,-1342.83 582.58,-1339.75 585.67,-1339.75 585.67,-1339.75 707.83,-1339.75 707.83,-1339.75 710.92,-1339.75 714,-1342.83 714,-1345.92 714,-1345.92 714,-1352.08 714,-1352.08 714,-1355.17 710.92,-1358.25 707.83,-1358.25"/>
<text text-anchor="start" x="587.5" y="-1345.7" font-family="Helvetica,sans-Serif" font-size="9.00">UserNotAuthenticatedError.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/domain/User/errors/UserNotRegisteredError.ts -->
<g id="node21" class="node">
<title>src/server/Authentication/domain/User/errors/UserNotRegisteredError.ts</title>
<g id="a_node21"><a xlink:href="src/server/Authentication/domain/User/errors/UserNotRegisteredError.ts" xlink:title="UserNotRegisteredError.ts">
<path fill="#ddfeff" stroke="black" d="M702.21,-1389.25C702.21,-1389.25 591.29,-1389.25 591.29,-1389.25 588.21,-1389.25 585.12,-1386.17 585.12,-1383.08 585.12,-1383.08 585.12,-1376.92 585.12,-1376.92 585.12,-1373.83 588.21,-1370.75 591.29,-1370.75 591.29,-1370.75 702.21,-1370.75 702.21,-1370.75 705.29,-1370.75 708.38,-1373.83 708.38,-1376.92 708.38,-1376.92 708.38,-1383.08 708.38,-1383.08 708.38,-1386.17 705.29,-1389.25 702.21,-1389.25"/>
<text text-anchor="start" x="593.12" y="-1376.7" font-family="Helvetica,sans-Serif" font-size="9.00">UserNotRegisteredError.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts -->
<g id="node22" class="node">
<title>src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts</title>
<g id="a_node22"><a xlink:href="src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts" xlink:title="ConvexAuthenticationGateway.ts">
<path fill="#ddfeff" stroke="black" d="M250.46,-1481.25C250.46,-1481.25 112.54,-1481.25 112.54,-1481.25 109.46,-1481.25 106.38,-1478.17 106.38,-1475.08 106.38,-1475.08 106.38,-1468.92 106.38,-1468.92 106.38,-1465.83 109.46,-1462.75 112.54,-1462.75 112.54,-1462.75 250.46,-1462.75 250.46,-1462.75 253.54,-1462.75 256.62,-1465.83 256.62,-1468.92 256.62,-1468.92 256.62,-1475.08 256.62,-1475.08 256.62,-1478.17 253.54,-1481.25 250.46,-1481.25"/>
<text text-anchor="start" x="114.38" y="-1468.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexAuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts -->
<g id="edge6" class="edge">
<title>src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M233.65,-1462.25C257.38,-1458.2 286.03,-1453.99 312,-1452 416.03,-1444.01 442.84,-1442.63 546.75,-1452 567.08,-1453.83 589.49,-1457.74 607.83,-1461.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="607.22,-1463.47 613.52,-1462.63 608.07,-1459.36 607.22,-1463.47"/>
</g>
<!-- src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/application/ports/AuthenticationGateway.ts -->
<g id="edge5" class="edge">
<title>src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/application/ports/AuthenticationGateway.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M256.82,-1472C285.11,-1472 317.16,-1472 344.79,-1472"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="344.57,-1474.1 350.57,-1472 344.57,-1469.9 344.57,-1474.1"/>
</g>
<!-- src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="edge7" class="edge">
<title>src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/User/errors/UserNotAuthenticatedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M257,-1463.03C272.08,-1458.53 286.97,-1451.87 299,-1442 309.3,-1433.55 301.5,-1423.19 312,-1415 398.84,-1347.24 446.55,-1385.65 554.75,-1365 562.94,-1363.44 571.55,-1361.85 580.07,-1360.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="580.39,-1362.39 585.93,-1359.26 579.65,-1358.26 580.39,-1362.39"/>
</g>
<!-- src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/User/errors/UserNotRegisteredError.ts -->
<g id="edge8" class="edge">
<title>src/server/Authentication/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/Authentication/domain/User/errors/UserNotRegisteredError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M219.68,-1462.25C245.45,-1455.78 280.63,-1447.49 312,-1442 415.5,-1423.89 444.32,-1437.42 546.75,-1414 569.64,-1408.77 594.64,-1400.15 613.8,-1392.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="614.49,-1394.89 619.33,-1390.78 612.98,-1390.97 614.49,-1394.89"/>
</g>
<!-- src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts -->
<g id="node23" class="node">
<title>src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts</title>
<g id="a_node23"><a xlink:href="src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts" xlink:title="ConvexAppUserRepository.ts">
<path fill="#ddfeff" stroke="black" d="M243.33,-1359.25C243.33,-1359.25 119.67,-1359.25 119.67,-1359.25 116.58,-1359.25 113.5,-1356.17 113.5,-1353.08 113.5,-1353.08 113.5,-1346.92 113.5,-1346.92 113.5,-1343.83 116.58,-1340.75 119.67,-1340.75 119.67,-1340.75 243.33,-1340.75 243.33,-1340.75 246.42,-1340.75 249.5,-1343.83 249.5,-1346.92 249.5,-1346.92 249.5,-1353.08 249.5,-1353.08 249.5,-1356.17 246.42,-1359.25 243.33,-1359.25"/>
<text text-anchor="start" x="121.5" y="-1346.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts -->
<g id="edge9" class="edge">
<title>src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M249.95,-1342.95C268.06,-1344.6 286.16,-1350.08 299,-1363 318.53,-1382.66 292.94,-1466.89 312,-1487 322.4,-1497.97 337.08,-1503.39 352.02,-1505.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="351.6,-1507.81 357.8,-1506.44 352.09,-1503.64 351.6,-1507.81"/>
</g>
<!-- src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts -->
<g id="edge10" class="edge">
<title>src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M250,-1346.5C327.17,-1345.61 454.7,-1353.85 546.75,-1409 551.63,-1411.93 550.41,-1415.32 554.75,-1419 572.96,-1434.44 596.54,-1447.5 615.13,-1456.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="614.23,-1458.44 620.56,-1459.11 616.03,-1454.64 614.23,-1458.44"/>
</g>
<!-- src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts -->
<g id="node24" class="node">
<title>src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts</title>
<g id="a_node24"><a xlink:href="src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts" xlink:title="InMemoryAppUserRepository.ts">
<path fill="#ddfeff" stroke="black" d="M247.83,-1412.25C247.83,-1412.25 115.17,-1412.25 115.17,-1412.25 112.08,-1412.25 109,-1409.17 109,-1406.08 109,-1406.08 109,-1399.92 109,-1399.92 109,-1396.83 112.08,-1393.75 115.17,-1393.75 115.17,-1393.75 247.83,-1393.75 247.83,-1393.75 250.92,-1393.75 254,-1396.83 254,-1399.92 254,-1399.92 254,-1406.08 254,-1406.08 254,-1409.17 250.92,-1412.25 247.83,-1412.25"/>
<text text-anchor="start" x="117" y="-1399.7" font-family="Helvetica,sans-Serif" font-size="9.00">InMemoryAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts -->
<g id="edge11" class="edge">
<title>src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M227.6,-1412.72C251.58,-1419.95 279.95,-1432.12 299,-1452 310.48,-1463.98 299.17,-1476.48 312,-1487 323.19,-1496.17 337.51,-1501.11 351.8,-1503.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="351.44,-1505.65 357.67,-1504.4 352.02,-1501.49 351.44,-1505.65"/>
</g>
<!-- src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts -->
<g id="edge12" class="edge">
<title>src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository.ts&#45;&gt;src/server/Authentication/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M254.22,-1407.09C328.07,-1412.14 446.22,-1422.55 546.75,-1442 567.43,-1446 590.07,-1452.26 608.47,-1457.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="607.36,-1459.7 613.71,-1459.46 608.6,-1455.69 607.36,-1459.7"/>
</g>
<!-- src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="node25" class="node">
<title>src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<g id="a_node25"><a xlink:href="src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommand.ts" xlink:title="SaveDeckCommand.ts">
<path fill="#ddfeff" stroke="black" d="M462.08,-916.25C462.08,-916.25 364.67,-916.25 364.67,-916.25 361.58,-916.25 358.5,-913.17 358.5,-910.08 358.5,-910.08 358.5,-903.92 358.5,-903.92 358.5,-900.83 361.58,-897.75 364.67,-897.75 364.67,-897.75 462.08,-897.75 462.08,-897.75 465.17,-897.75 468.25,-900.83 468.25,-903.92 468.25,-903.92 468.25,-910.08 468.25,-910.08 468.25,-913.17 465.17,-916.25 462.08,-916.25"/>
<text text-anchor="start" x="366.5" y="-903.7" font-family="Helvetica,sans-Serif" font-size="9.00">SaveDeckCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts -->
<g id="node26" class="node">
<title>src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts</title>
<g id="a_node26"><a xlink:href="src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts" xlink:title="SaveDeckCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M246.33,-916.25C246.33,-916.25 116.67,-916.25 116.67,-916.25 113.58,-916.25 110.5,-913.17 110.5,-910.08 110.5,-910.08 110.5,-903.92 110.5,-903.92 110.5,-900.83 113.58,-897.75 116.67,-897.75 116.67,-897.75 246.33,-897.75 246.33,-897.75 249.42,-897.75 252.5,-900.83 252.5,-903.92 252.5,-903.92 252.5,-910.08 252.5,-910.08 252.5,-913.17 249.42,-916.25 246.33,-916.25"/>
<text text-anchor="start" x="118.5" y="-903.7" font-family="Helvetica,sans-Serif" font-size="9.00">SaveDeckCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="edge13" class="edge">
<title>src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M252.72,-907C283.6,-907 319.59,-907 349.57,-907"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="349.24,-909.1 355.24,-907 349.24,-904.9 349.24,-909.1"/>
</g>
<!-- src/server/DeckBuilding/application/ports/DeckRepository.ts -->
<g id="node27" class="node">
<title>src/server/DeckBuilding/application/ports/DeckRepository.ts</title>
<g id="a_node27"><a xlink:href="src/server/DeckBuilding/application/ports/DeckRepository.ts" xlink:title="DeckRepository.ts">
<path fill="#ddfeff" stroke="black" d="M451.96,-1062.25C451.96,-1062.25 374.79,-1062.25 374.79,-1062.25 371.71,-1062.25 368.62,-1059.17 368.62,-1056.08 368.62,-1056.08 368.62,-1049.92 368.62,-1049.92 368.62,-1046.83 371.71,-1043.75 374.79,-1043.75 374.79,-1043.75 451.96,-1043.75 451.96,-1043.75 455.04,-1043.75 458.12,-1046.83 458.12,-1049.92 458.12,-1049.92 458.12,-1056.08 458.12,-1056.08 458.12,-1059.17 455.04,-1062.25 451.96,-1062.25"/>
<text text-anchor="start" x="376.62" y="-1049.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/DeckBuilding/application/ports/DeckRepository.ts -->
<g id="edge14" class="edge">
<title>src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/DeckBuilding/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M197.58,-916.58C236.96,-941.59 343.02,-1008.95 389.75,-1038.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="388.49,-1040.32 394.68,-1041.76 390.74,-1036.77 388.49,-1040.32"/>
</g>
<!-- src/server/DeckBuilding/domain/Deck/Deck.ts -->
<g id="node28" class="node">
<title>src/server/DeckBuilding/domain/Deck/Deck.ts</title>
<g id="a_node28"><a xlink:href="src/server/DeckBuilding/domain/Deck/Deck.ts" xlink:title="Deck.ts">
<path fill="#ddfeff" stroke="black" d="M667.58,-1106.25C667.58,-1106.25 625.92,-1106.25 625.92,-1106.25 622.83,-1106.25 619.75,-1103.17 619.75,-1100.08 619.75,-1100.08 619.75,-1093.92 619.75,-1093.92 619.75,-1090.83 622.83,-1087.75 625.92,-1087.75 625.92,-1087.75 667.58,-1087.75 667.58,-1087.75 670.67,-1087.75 673.75,-1090.83 673.75,-1093.92 673.75,-1093.92 673.75,-1100.08 673.75,-1100.08 673.75,-1103.17 670.67,-1106.25 667.58,-1106.25"/>
<text text-anchor="start" x="631.75" y="-1093.7" font-family="Helvetica,sans-Serif" font-size="9.00">Deck.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts -->
<g id="edge15" class="edge">
<title>src/server/DeckBuilding/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M202.93,-897.4C264.54,-870.03 450.14,-799.35 546.75,-892 562.08,-906.7 540.59,-1067.17 554.75,-1083 568.37,-1098.22 591.06,-1101.73 610.38,-1101.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="610.33,-1103.54 616.23,-1101.17 610.14,-1099.35 610.33,-1103.54"/>
</g>
<!-- src/server/DeckBuilding/application/ports/DeckRepository.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts -->
<g id="edge16" class="edge">
<title>src/server/DeckBuilding/application/ports/DeckRepository.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M456.63,-1062.7C484.43,-1068.91 521.66,-1076.92 554.75,-1083 573.23,-1086.39 593.91,-1089.62 610.89,-1092.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="610.16,-1094.14 616.4,-1092.92 610.77,-1089.98 610.16,-1094.14"/>
</g>
<!-- src/server/DeckBuilding/application/queries/loadDeckBuilderSettingsByGameId.ts -->
<g id="node29" class="node">
<title>src/server/DeckBuilding/application/queries/loadDeckBuilderSettingsByGameId.ts</title>
<g id="a_node29"><a xlink:href="src/server/DeckBuilding/application/queries/loadDeckBuilderSettingsByGameId.ts" xlink:title="loadDeckBuilderSettingsByGameId.ts">
<path fill="#ccffcc" stroke="black" d="M259.83,-1031.25C259.83,-1031.25 103.17,-1031.25 103.17,-1031.25 100.08,-1031.25 97,-1028.17 97,-1025.08 97,-1025.08 97,-1018.92 97,-1018.92 97,-1015.83 100.08,-1012.75 103.17,-1012.75 103.17,-1012.75 259.83,-1012.75 259.83,-1012.75 262.92,-1012.75 266,-1015.83 266,-1018.92 266,-1018.92 266,-1025.08 266,-1025.08 266,-1028.17 262.92,-1031.25 259.83,-1031.25"/>
<text text-anchor="start" x="105" y="-1018.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckBuilderSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/application/queries/loadGameSettingsByGameId.ts -->
<g id="node30" class="node">
<title>src/server/DeckBuilding/application/queries/loadGameSettingsByGameId.ts</title>
<g id="a_node30"><a xlink:href="src/server/DeckBuilding/application/queries/loadGameSettingsByGameId.ts" xlink:title="loadGameSettingsByGameId.ts">
<path fill="#ccffcc" stroke="black" d="M247.08,-1062.25C247.08,-1062.25 115.92,-1062.25 115.92,-1062.25 112.83,-1062.25 109.75,-1059.17 109.75,-1056.08 109.75,-1056.08 109.75,-1049.92 109.75,-1049.92 109.75,-1046.83 112.83,-1043.75 115.92,-1043.75 115.92,-1043.75 247.08,-1043.75 247.08,-1043.75 250.17,-1043.75 253.25,-1046.83 253.25,-1049.92 253.25,-1049.92 253.25,-1056.08 253.25,-1056.08 253.25,-1059.17 250.17,-1062.25 247.08,-1062.25"/>
<text text-anchor="start" x="117.75" y="-1049.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError.ts -->
<g id="node31" class="node">
<title>src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError.ts</title>
<g id="a_node31"><a xlink:href="src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError.ts" xlink:title="DeckNotOwnedError.ts">
<path fill="#ccffcc" stroke="black" d="M695.08,-1053.25C695.08,-1053.25 598.42,-1053.25 598.42,-1053.25 595.33,-1053.25 592.25,-1050.17 592.25,-1047.08 592.25,-1047.08 592.25,-1040.92 592.25,-1040.92 592.25,-1037.83 595.33,-1034.75 598.42,-1034.75 598.42,-1034.75 695.08,-1034.75 695.08,-1034.75 698.17,-1034.75 701.25,-1037.83 701.25,-1040.92 701.25,-1040.92 701.25,-1047.08 701.25,-1047.08 701.25,-1050.17 698.17,-1053.25 695.08,-1053.25"/>
<text text-anchor="start" x="600.25" y="-1040.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckNotOwnedError.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts -->
<g id="node32" class="node">
<title>src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts</title>
<g id="a_node32"><a xlink:href="src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts" xlink:title="ConvexDeckRepository.ts">
<path fill="#ddfeff" stroke="black" d="M235.83,-1166.25C235.83,-1166.25 127.17,-1166.25 127.17,-1166.25 124.08,-1166.25 121,-1163.17 121,-1160.08 121,-1160.08 121,-1153.92 121,-1153.92 121,-1150.83 124.08,-1147.75 127.17,-1147.75 127.17,-1147.75 235.83,-1147.75 235.83,-1147.75 238.92,-1147.75 242,-1150.83 242,-1153.92 242,-1153.92 242,-1160.08 242,-1160.08 242,-1163.17 238.92,-1166.25 235.83,-1166.25"/>
<text text-anchor="start" x="129" y="-1153.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/DeckBuilding/application/ports/DeckRepository.ts -->
<g id="edge17" class="edge">
<title>src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/DeckBuilding/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M203.71,-1147.4C244.92,-1128.76 335.86,-1087.62 382.9,-1066.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="383.52,-1068.36 388.12,-1063.97 381.79,-1064.53 383.52,-1068.36"/>
</g>
<!-- src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts -->
<g id="edge18" class="edge">
<title>src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M242.11,-1149.28C340.12,-1136.59 532.24,-1111.7 610.9,-1101.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="610.8,-1103.64 616.48,-1100.79 610.26,-1099.48 610.8,-1103.64"/>
</g>
<!-- src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts -->
<g id="node33" class="node">
<title>src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts</title>
<g id="a_node33"><a xlink:href="src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts" xlink:title="InMemoryDeckRepository.ts">
<path fill="#ddfeff" stroke="black" d="M240.33,-1219.25C240.33,-1219.25 122.67,-1219.25 122.67,-1219.25 119.58,-1219.25 116.5,-1216.17 116.5,-1213.08 116.5,-1213.08 116.5,-1206.92 116.5,-1206.92 116.5,-1203.83 119.58,-1200.75 122.67,-1200.75 122.67,-1200.75 240.33,-1200.75 240.33,-1200.75 243.42,-1200.75 246.5,-1203.83 246.5,-1206.92 246.5,-1206.92 246.5,-1213.08 246.5,-1213.08 246.5,-1216.17 243.42,-1219.25 240.33,-1219.25"/>
<text text-anchor="start" x="124.5" y="-1206.7" font-family="Helvetica,sans-Serif" font-size="9.00">InMemoryDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts&#45;&gt;src/server/DeckBuilding/application/ports/DeckRepository.ts -->
<g id="edge19" class="edge">
<title>src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts&#45;&gt;src/server/DeckBuilding/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M246.91,-1211.62C264.75,-1209.65 283.47,-1205.15 299,-1196 307.5,-1190.99 306.02,-1185.85 312,-1178 342.13,-1138.44 379.1,-1093.22 398.68,-1069.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="400.26,-1070.89 402.47,-1064.92 397.02,-1068.21 400.26,-1070.89"/>
</g>
<!-- src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts -->
<g id="edge20" class="edge">
<title>src/server/DeckBuilding/infrastructure/repositories/InMemoryDeckRepository.ts&#45;&gt;src/server/DeckBuilding/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M246.85,-1203.95C263.88,-1201.89 282.2,-1199.26 299,-1196 415.51,-1173.37 549.62,-1129.96 611.37,-1108.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="611.73,-1111.06 616.73,-1107.13 610.37,-1107.09 611.73,-1111.06"/>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="node34" class="node">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<g id="a_node34"><a xlink:href="src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts" xlink:title="LeaveMatchCommand.ts">
<path fill="#ddfeff" stroke="black" d="M466.21,-1659.25C466.21,-1659.25 360.54,-1659.25 360.54,-1659.25 357.46,-1659.25 354.38,-1656.17 354.38,-1653.08 354.38,-1653.08 354.38,-1646.92 354.38,-1646.92 354.38,-1643.83 357.46,-1640.75 360.54,-1640.75 360.54,-1640.75 466.21,-1640.75 466.21,-1640.75 469.29,-1640.75 472.38,-1643.83 472.38,-1646.92 472.38,-1646.92 472.38,-1653.08 472.38,-1653.08 472.38,-1656.17 469.29,-1659.25 466.21,-1659.25"/>
<text text-anchor="start" x="362.38" y="-1646.7" font-family="Helvetica,sans-Serif" font-size="9.00">LeaveMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts -->
<g id="node35" class="node">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts</title>
<g id="a_node35"><a xlink:href="src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts" xlink:title="LeaveMatchCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M250.46,-1659.25C250.46,-1659.25 112.54,-1659.25 112.54,-1659.25 109.46,-1659.25 106.38,-1656.17 106.38,-1653.08 106.38,-1653.08 106.38,-1646.92 106.38,-1646.92 106.38,-1643.83 109.46,-1640.75 112.54,-1640.75 112.54,-1640.75 250.46,-1640.75 250.46,-1640.75 253.54,-1640.75 256.62,-1643.83 256.62,-1646.92 256.62,-1646.92 256.62,-1653.08 256.62,-1653.08 256.62,-1656.17 253.54,-1659.25 250.46,-1659.25"/>
<text text-anchor="start" x="114.38" y="-1646.7" font-family="Helvetica,sans-Serif" font-size="9.00">LeaveMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="edge21" class="edge">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M256.82,-1650C285.22,-1650 317.43,-1650 345.14,-1650"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="344.94,-1652.1 350.94,-1650 344.94,-1647.9 344.94,-1652.1"/>
</g>
<!-- src/server/Gaming/application/ports/MatchRepository.ts -->
<g id="node36" class="node">
<title>src/server/Gaming/application/ports/MatchRepository.ts</title>
<g id="a_node36"><a xlink:href="src/server/Gaming/application/ports/MatchRepository.ts" xlink:title="MatchRepository.ts">
<path fill="#ddfeff" stroke="black" d="M453.83,-1774.25C453.83,-1774.25 372.92,-1774.25 372.92,-1774.25 369.83,-1774.25 366.75,-1771.17 366.75,-1768.08 366.75,-1768.08 366.75,-1761.92 366.75,-1761.92 366.75,-1758.83 369.83,-1755.75 372.92,-1755.75 372.92,-1755.75 453.83,-1755.75 453.83,-1755.75 456.92,-1755.75 460,-1758.83 460,-1761.92 460,-1761.92 460,-1768.08 460,-1768.08 460,-1771.17 456.92,-1774.25 453.83,-1774.25"/>
<text text-anchor="start" x="374.75" y="-1761.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts -->
<g id="edge22" class="edge">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M201.7,-1659.61C242.52,-1680.03 338.03,-1727.81 385.13,-1751.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="384.05,-1753.18 390.36,-1753.99 385.93,-1749.42 384.05,-1753.18"/>
</g>
<!-- src/server/Gaming/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="node37" class="node">
<title>src/server/Gaming/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<g id="a_node37"><a xlink:href="src/server/Gaming/domain/Match/errors/MatchAlreadyFinishedError.ts" xlink:title="MatchAlreadyFinishedError.ts">
<path fill="#ddfeff" stroke="black" d="M708.58,-1734.25C708.58,-1734.25 584.92,-1734.25 584.92,-1734.25 581.83,-1734.25 578.75,-1731.17 578.75,-1728.08 578.75,-1728.08 578.75,-1721.92 578.75,-1721.92 578.75,-1718.83 581.83,-1715.75 584.92,-1715.75 584.92,-1715.75 708.58,-1715.75 708.58,-1715.75 711.67,-1715.75 714.75,-1718.83 714.75,-1721.92 714.75,-1721.92 714.75,-1728.08 714.75,-1728.08 714.75,-1731.17 711.67,-1734.25 708.58,-1734.25"/>
<text text-anchor="start" x="586.75" y="-1721.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchAlreadyFinishedError.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="edge23" class="edge">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M208.52,-1640.4C270.15,-1619.26 428.68,-1574.4 546.75,-1624 587.43,-1641.09 619.78,-1683.95 635.45,-1707.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="633.51,-1708.84 638.49,-1712.79 637.05,-1706.6 633.51,-1708.84"/>
</g>
<!-- src/server/Gaming/domain/Match/errors/MatchNotFoundError.ts -->
<g id="node38" class="node">
<title>src/server/Gaming/domain/Match/errors/MatchNotFoundError.ts</title>
<g id="a_node38"><a xlink:href="src/server/Gaming/domain/Match/errors/MatchNotFoundError.ts" xlink:title="MatchNotFoundError.ts">
<path fill="#ddfeff" stroke="black" d="M695.46,-1765.25C695.46,-1765.25 598.04,-1765.25 598.04,-1765.25 594.96,-1765.25 591.88,-1762.17 591.88,-1759.08 591.88,-1759.08 591.88,-1752.92 591.88,-1752.92 591.88,-1749.83 594.96,-1746.75 598.04,-1746.75 598.04,-1746.75 695.46,-1746.75 695.46,-1746.75 698.54,-1746.75 701.62,-1749.83 701.62,-1752.92 701.62,-1752.92 701.62,-1759.08 701.62,-1759.08 701.62,-1762.17 698.54,-1765.25 695.46,-1765.25"/>
<text text-anchor="start" x="599.88" y="-1752.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchNotFoundError.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/domain/Match/errors/MatchNotFoundError.ts -->
<g id="edge24" class="edge">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Gaming/domain/Match/errors/MatchNotFoundError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M253.62,-1640.27C272.55,-1638.09 293.02,-1636.1 312,-1635 364.08,-1631.99 508.49,-1599.54 546.75,-1635 563.91,-1650.91 539.03,-1722.66 554.75,-1740 562.15,-1748.16 572.12,-1753.09 582.76,-1755.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="582.12,-1757.96 588.43,-1757.17 583.01,-1753.85 582.12,-1757.96"/>
</g>
<!-- src/server/Shared/application/ports/Context.ts -->
<g id="node39" class="node">
<title>src/server/Shared/application/ports/Context.ts</title>
<g id="a_node39"><a xlink:href="src/server/Shared/application/ports/Context.ts" xlink:title="Context.ts">
<path fill="#ddfeff" stroke="black" d="M435.46,-2155.25C435.46,-2155.25 391.29,-2155.25 391.29,-2155.25 388.21,-2155.25 385.12,-2152.17 385.12,-2149.08 385.12,-2149.08 385.12,-2142.92 385.12,-2142.92 385.12,-2139.83 388.21,-2136.75 391.29,-2136.75 391.29,-2136.75 435.46,-2136.75 435.46,-2136.75 438.54,-2136.75 441.62,-2139.83 441.62,-2142.92 441.62,-2142.92 441.62,-2149.08 441.62,-2149.08 441.62,-2152.17 438.54,-2155.25 435.46,-2155.25"/>
<text text-anchor="start" x="393.12" y="-2142.7" font-family="Helvetica,sans-Serif" font-size="9.00">Context.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts -->
<g id="edge25" class="edge">
<title>src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M203.07,-1659.73C230.65,-1674.04 278.48,-1703.61 299,-1745 318.01,-1783.35 283.67,-2097.91 312,-2130 327.6,-2147.67 354.25,-2151.26 376.18,-2150.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="376.02,-2152.76 381.9,-2150.34 375.8,-2148.56 376.02,-2152.76"/>
</g>
<!-- src/server/Gaming/domain/Match/Match.ts -->
<g id="node40" class="node">
<title>src/server/Gaming/domain/Match/Match.ts</title>
<g id="a_node40"><a xlink:href="src/server/Gaming/domain/Match/Match.ts" xlink:title="Match.ts">
<path fill="#ddfeff" stroke="black" d="M667.58,-1818.25C667.58,-1818.25 625.92,-1818.25 625.92,-1818.25 622.83,-1818.25 619.75,-1815.17 619.75,-1812.08 619.75,-1812.08 619.75,-1805.92 619.75,-1805.92 619.75,-1802.83 622.83,-1799.75 625.92,-1799.75 625.92,-1799.75 667.58,-1799.75 667.58,-1799.75 670.67,-1799.75 673.75,-1802.83 673.75,-1805.92 673.75,-1805.92 673.75,-1812.08 673.75,-1812.08 673.75,-1815.17 670.67,-1818.25 667.58,-1818.25"/>
<text text-anchor="start" x="629.88" y="-1805.7" font-family="Helvetica,sans-Serif" font-size="9.00">Match.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/application/ports/MatchRepository.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts -->
<g id="edge26" class="edge">
<title>src/server/Gaming/application/ports/MatchRepository.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M456.63,-1774.7C484.43,-1780.91 521.66,-1788.92 554.75,-1795 573.23,-1798.39 593.91,-1801.62 610.89,-1804.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="610.16,-1806.14 616.4,-1804.92 610.77,-1801.98 610.16,-1806.14"/>
</g>
<!-- src/server/Gaming/application/queries/loadMatchById.ts -->
<g id="node41" class="node">
<title>src/server/Gaming/application/queries/loadMatchById.ts</title>
<g id="a_node41"><a xlink:href="src/server/Gaming/application/queries/loadMatchById.ts" xlink:title="loadMatchById.ts">
<path fill="#ccffcc" stroke="black" d="M218.21,-1774.25C218.21,-1774.25 144.79,-1774.25 144.79,-1774.25 141.71,-1774.25 138.62,-1771.17 138.62,-1768.08 138.62,-1768.08 138.62,-1761.92 138.62,-1761.92 138.62,-1758.83 141.71,-1755.75 144.79,-1755.75 144.79,-1755.75 218.21,-1755.75 218.21,-1755.75 221.29,-1755.75 224.38,-1758.83 224.38,-1761.92 224.38,-1761.92 224.38,-1768.08 224.38,-1768.08 224.38,-1771.17 221.29,-1774.25 218.21,-1774.25"/>
<text text-anchor="start" x="146.62" y="-1761.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadMatchById.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts -->
<g id="node42" class="node">
<title>src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts</title>
<g id="a_node42"><a xlink:href="src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts" xlink:title="InMemoryMatchRepository.ts">
<path fill="#ddfeff" stroke="black" d="M242.21,-1931.25C242.21,-1931.25 120.79,-1931.25 120.79,-1931.25 117.71,-1931.25 114.62,-1928.17 114.62,-1925.08 114.62,-1925.08 114.62,-1918.92 114.62,-1918.92 114.62,-1915.83 117.71,-1912.75 120.79,-1912.75 120.79,-1912.75 242.21,-1912.75 242.21,-1912.75 245.29,-1912.75 248.38,-1915.83 248.38,-1918.92 248.38,-1918.92 248.38,-1925.08 248.38,-1925.08 248.38,-1928.17 245.29,-1931.25 242.21,-1931.25"/>
<text text-anchor="start" x="122.62" y="-1918.7" font-family="Helvetica,sans-Serif" font-size="9.00">InMemoryMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts -->
<g id="edge27" class="edge">
<title>src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M248.73,-1923.41C266.02,-1921.34 283.99,-1916.84 299,-1908 307.5,-1902.99 306.02,-1897.85 312,-1890 342.13,-1850.44 379.1,-1805.22 398.68,-1781.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="400.26,-1782.89 402.47,-1776.92 397.02,-1780.21 400.26,-1782.89"/>
</g>
<!-- src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts -->
<g id="edge28" class="edge">
<title>src/server/Gaming/infrastructure/repositories/InMemoryMatchRepository.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M248.68,-1915.73C265.19,-1913.7 282.8,-1911.15 299,-1908 415.51,-1885.37 549.62,-1841.96 611.37,-1820.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="611.73,-1823.06 616.73,-1819.13 610.37,-1819.09 611.73,-1823.06"/>
</g>
<!-- src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts -->
<g id="node43" class="node">
<title>src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts</title>
<g id="a_node43"><a xlink:href="src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts" xlink:title="ConvexMatchRepository.ts">
<path fill="#ddfeff" stroke="black" d="M237.71,-1878.25C237.71,-1878.25 125.29,-1878.25 125.29,-1878.25 122.21,-1878.25 119.12,-1875.17 119.12,-1872.08 119.12,-1872.08 119.12,-1865.92 119.12,-1865.92 119.12,-1862.83 122.21,-1859.75 125.29,-1859.75 125.29,-1859.75 237.71,-1859.75 237.71,-1859.75 240.79,-1859.75 243.88,-1862.83 243.88,-1865.92 243.88,-1865.92 243.88,-1872.08 243.88,-1872.08 243.88,-1875.17 240.79,-1878.25 237.71,-1878.25"/>
<text text-anchor="start" x="127.12" y="-1865.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts -->
<g id="edge29" class="edge">
<title>src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M203.71,-1859.4C244.92,-1840.76 335.86,-1799.62 382.9,-1778.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="383.52,-1780.36 388.12,-1775.97 381.79,-1776.53 383.52,-1780.36"/>
</g>
<!-- src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts -->
<g id="edge30" class="edge">
<title>src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M244.02,-1861.03C342.6,-1848.26 532.82,-1823.63 610.95,-1813.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="610.79,-1815.65 616.47,-1812.79 610.25,-1811.48 610.79,-1815.65"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="node44" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<g id="a_node44"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts" xlink:title="AddPlayerToMatchMakingQueueCommand.ts">
<path fill="#ddfeff" stroke="black" d="M508.58,-2516.25C508.58,-2516.25 318.17,-2516.25 318.17,-2516.25 315.08,-2516.25 312,-2513.17 312,-2510.08 312,-2510.08 312,-2503.92 312,-2503.92 312,-2500.83 315.08,-2497.75 318.17,-2497.75 318.17,-2497.75 508.58,-2497.75 508.58,-2497.75 511.67,-2497.75 514.75,-2500.83 514.75,-2503.92 514.75,-2503.92 514.75,-2510.08 514.75,-2510.08 514.75,-2513.17 511.67,-2516.25 508.58,-2516.25"/>
<text text-anchor="start" x="320" y="-2503.7" font-family="Helvetica,sans-Serif" font-size="9.00">AddPlayerToMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts -->
<g id="node45" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts</title>
<g id="a_node45"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts" xlink:title="AddPlayerToMatchMakingQueueCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M292.83,-2516.25C292.83,-2516.25 70.17,-2516.25 70.17,-2516.25 67.08,-2516.25 64,-2513.17 64,-2510.08 64,-2510.08 64,-2503.92 64,-2503.92 64,-2500.83 67.08,-2497.75 70.17,-2497.75 70.17,-2497.75 292.83,-2497.75 292.83,-2497.75 295.92,-2497.75 299,-2500.83 299,-2503.92 299,-2503.92 299,-2510.08 299,-2510.08 299,-2513.17 295.92,-2516.25 292.83,-2516.25"/>
<text text-anchor="start" x="72" y="-2503.7" font-family="Helvetica,sans-Serif" font-size="9.00">AddPlayerToMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts -->
<g id="edge34" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.09,-2497.38C289.63,-2494.06 294.69,-2489.98 299,-2485 320.7,-2459.95 292.92,-2214.1 312,-2187 326.63,-2166.21 353.77,-2155.94 376.14,-2150.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="376.33,-2152.99 381.79,-2149.74 375.49,-2148.87 376.33,-2152.99"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="edge31" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M299.46,-2507C300.61,-2507 301.77,-2507 302.92,-2507"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="302.71,-2509.1 308.71,-2507 302.71,-2504.9 302.71,-2509.1"/>
</g>
<!-- src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="node46" class="node">
<title>src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<g id="a_node46"><a xlink:href="src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts" xlink:title="MatchmakingQueueRepository.ts">
<path fill="#ddfeff" stroke="black" d="M482.71,-2814.25C482.71,-2814.25 344.04,-2814.25 344.04,-2814.25 340.96,-2814.25 337.88,-2811.17 337.88,-2808.08 337.88,-2808.08 337.88,-2801.92 337.88,-2801.92 337.88,-2798.83 340.96,-2795.75 344.04,-2795.75 344.04,-2795.75 482.71,-2795.75 482.71,-2795.75 485.79,-2795.75 488.88,-2798.83 488.88,-2801.92 488.88,-2801.92 488.88,-2808.08 488.88,-2808.08 488.88,-2811.17 485.79,-2814.25 482.71,-2814.25"/>
<text text-anchor="start" x="345.88" y="-2801.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge32" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M250.24,-2516.71C268.82,-2522.72 287.09,-2532.41 299,-2548 323.47,-2580.05 292.77,-2693.56 312,-2729 327.57,-2757.7 359.15,-2778.78 382.75,-2791.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="381.74,-2793.21 388.04,-2794.08 383.66,-2789.47 381.74,-2793.21"/>
</g>
<!-- src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="node47" class="node">
<title>src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<g id="a_node47"><a xlink:href="src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts" xlink:title="MatchmakingQueueItem.ts">
<path fill="#ddfeff" stroke="black" d="M873.33,-2888.25C873.33,-2888.25 760.92,-2888.25 760.92,-2888.25 757.83,-2888.25 754.75,-2885.17 754.75,-2882.08 754.75,-2882.08 754.75,-2875.92 754.75,-2875.92 754.75,-2872.83 757.83,-2869.75 760.92,-2869.75 760.92,-2869.75 873.33,-2869.75 873.33,-2869.75 876.42,-2869.75 879.5,-2872.83 879.5,-2875.92 879.5,-2875.92 879.5,-2882.08 879.5,-2882.08 879.5,-2885.17 876.42,-2888.25 873.33,-2888.25"/>
<text text-anchor="start" x="762.75" y="-2875.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchmakingQueueItem.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge33" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M283.2,-2497.36C289.03,-2494.06 294.39,-2489.99 299,-2485 317.21,-2465.3 292.04,-2382.92 312,-2365 350.82,-2330.16 502.11,-2338 546.75,-2365 738.54,-2481.02 800.43,-2783.6 813.34,-2860.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="811.23,-2860.91 814.26,-2866.5 815.37,-2860.24 811.23,-2860.91"/>
</g>
<!-- src/server/Shared/application/ports/TimeService.ts -->
<g id="node48" class="node">
<title>src/server/Shared/application/ports/TimeService.ts</title>
<g id="a_node48"><a xlink:href="src/server/Shared/application/ports/TimeService.ts" xlink:title="TimeService.ts">
<path fill="#ddfeff" stroke="black" d="M445.21,-2124.25C445.21,-2124.25 381.54,-2124.25 381.54,-2124.25 378.46,-2124.25 375.38,-2121.17 375.38,-2118.08 375.38,-2118.08 375.38,-2111.92 375.38,-2111.92 375.38,-2108.83 378.46,-2105.75 381.54,-2105.75 381.54,-2105.75 445.21,-2105.75 445.21,-2105.75 448.29,-2105.75 451.38,-2108.83 451.38,-2111.92 451.38,-2111.92 451.38,-2118.08 451.38,-2118.08 451.38,-2121.17 448.29,-2124.25 445.21,-2124.25"/>
<text text-anchor="start" x="383.38" y="-2111.7" font-family="Helvetica,sans-Serif" font-size="9.00">TimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/TimeService.ts -->
<g id="edge35" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.39,-2497.26C289.82,-2493.95 294.77,-2489.92 299,-2485 324.67,-2455.16 285.91,-2160.47 312,-2131 325.29,-2115.99 346.55,-2111.15 366.09,-2110.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="365.9,-2112.51 371.89,-2110.36 365.87,-2108.31 365.9,-2112.51"/>
</g>
<!-- src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge53" class="edge">
<title>src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M473.04,-2814.73C539.54,-2825.93 651.15,-2845.19 746.75,-2864 752.36,-2865.1 758.21,-2866.3 764.03,-2867.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="763.24,-2869.5 769.55,-2868.68 764.11,-2865.39 763.24,-2869.5"/>
</g>
<!-- src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="node59" class="node">
<title>src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<g id="a_node59"><a xlink:href="src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts" xlink:title="MatchmakingQueue.ts">
<path fill="#ddfeff" stroke="black" d="M694.33,-2888.25C694.33,-2888.25 599.17,-2888.25 599.17,-2888.25 596.08,-2888.25 593,-2885.17 593,-2882.08 593,-2882.08 593,-2875.92 593,-2875.92 593,-2872.83 596.08,-2869.75 599.17,-2869.75 599.17,-2869.75 694.33,-2869.75 694.33,-2869.75 697.42,-2869.75 700.5,-2872.83 700.5,-2875.92 700.5,-2875.92 700.5,-2882.08 700.5,-2882.08 700.5,-2885.17 697.42,-2888.25 694.33,-2888.25"/>
<text text-anchor="start" x="601" y="-2875.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchmakingQueue.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge52" class="edge">
<title>src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M444.62,-2814.67C486.28,-2828 561.33,-2852 607.04,-2866.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="606.26,-2868.57 612.61,-2868.4 607.54,-2864.57 606.26,-2868.57"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="node49" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<g id="a_node49"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts" xlink:title="CancelMatchRegistrationCommand.ts">
<path fill="#ddfeff" stroke="black" d="M492.83,-2638.25C492.83,-2638.25 333.92,-2638.25 333.92,-2638.25 330.83,-2638.25 327.75,-2635.17 327.75,-2632.08 327.75,-2632.08 327.75,-2625.92 327.75,-2625.92 327.75,-2622.83 330.83,-2619.75 333.92,-2619.75 333.92,-2619.75 492.83,-2619.75 492.83,-2619.75 495.92,-2619.75 499,-2622.83 499,-2625.92 499,-2625.92 499,-2632.08 499,-2632.08 499,-2635.17 495.92,-2638.25 492.83,-2638.25"/>
<text text-anchor="start" x="335.75" y="-2625.7" font-family="Helvetica,sans-Serif" font-size="9.00">CancelMatchRegistrationCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts -->
<g id="node50" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts</title>
<g id="a_node50"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts" xlink:title="CancelMatchRegistrationCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M277.08,-2638.25C277.08,-2638.25 85.92,-2638.25 85.92,-2638.25 82.83,-2638.25 79.75,-2635.17 79.75,-2632.08 79.75,-2632.08 79.75,-2625.92 79.75,-2625.92 79.75,-2622.83 82.83,-2619.75 85.92,-2619.75 85.92,-2619.75 277.08,-2619.75 277.08,-2619.75 280.17,-2619.75 283.25,-2622.83 283.25,-2625.92 283.25,-2625.92 283.25,-2632.08 283.25,-2632.08 283.25,-2635.17 280.17,-2638.25 277.08,-2638.25"/>
<text text-anchor="start" x="87.75" y="-2625.7" font-family="Helvetica,sans-Serif" font-size="9.00">CancelMatchRegistrationCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts -->
<g id="edge38" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M283.64,-2619.75C289.37,-2616.36 294.59,-2612.16 299,-2607 329.35,-2571.52 285.37,-2225.35 312,-2187 326.5,-2166.12 353.64,-2155.86 376.05,-2150.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="376.25,-2152.93 381.71,-2149.68 375.42,-2148.81 376.25,-2152.93"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge37" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.24,-2638.69C264.94,-2644.67 285.14,-2654.35 299,-2670 316.8,-2690.1 297.51,-2706.39 312,-2729 329.83,-2756.82 361.6,-2778.29 384.64,-2791.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="383.45,-2792.95 389.72,-2793.97 385.45,-2789.26 383.45,-2792.95"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="edge36" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M283.66,-2629C295.28,-2629 307.08,-2629 318.59,-2629"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="318.3,-2631.1 324.3,-2629 318.3,-2626.9 318.3,-2631.1"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="node51" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<g id="a_node51"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts" xlink:title="CleanUpMatchMakingQueueCommand.ts">
<path fill="#ddfeff" stroke="black" d="M500.71,-2394.25C500.71,-2394.25 326.04,-2394.25 326.04,-2394.25 322.96,-2394.25 319.88,-2391.17 319.88,-2388.08 319.88,-2388.08 319.88,-2381.92 319.88,-2381.92 319.88,-2378.83 322.96,-2375.75 326.04,-2375.75 326.04,-2375.75 500.71,-2375.75 500.71,-2375.75 503.79,-2375.75 506.88,-2378.83 506.88,-2381.92 506.88,-2381.92 506.88,-2388.08 506.88,-2388.08 506.88,-2391.17 503.79,-2394.25 500.71,-2394.25"/>
<text text-anchor="start" x="327.88" y="-2381.7" font-family="Helvetica,sans-Serif" font-size="9.00">CleanUpMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts -->
<g id="node52" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts</title>
<g id="a_node52"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts" xlink:title="CleanUpMatchMakingQueueCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M284.96,-2394.25C284.96,-2394.25 78.04,-2394.25 78.04,-2394.25 74.96,-2394.25 71.88,-2391.17 71.88,-2388.08 71.88,-2388.08 71.88,-2381.92 71.88,-2381.92 71.88,-2378.83 74.96,-2375.75 78.04,-2375.75 78.04,-2375.75 284.96,-2375.75 284.96,-2375.75 288.04,-2375.75 291.12,-2378.83 291.12,-2381.92 291.12,-2381.92 291.12,-2388.08 291.12,-2388.08 291.12,-2391.17 288.04,-2394.25 284.96,-2394.25"/>
<text text-anchor="start" x="79.88" y="-2381.7" font-family="Helvetica,sans-Serif" font-size="9.00">CleanUpMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts -->
<g id="edge40" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M230.32,-2375.33C255.13,-2367.68 283.43,-2354.24 299,-2331 317.44,-2303.46 305.38,-2065.48 312,-2033 332.09,-1934.37 383.25,-1824.01 403.71,-1782.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="405.47,-1783.43 406.25,-1777.12 401.71,-1781.57 405.47,-1783.43"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts -->
<g id="edge42" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M229.02,-2375.32C253.9,-2367.62 282.7,-2354.15 299,-2331 335.99,-2278.45 273.8,-2238.68 312,-2187 327.11,-2166.55 354.22,-2156.27 376.46,-2151.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="376.61,-2153.23 382.05,-2149.94 375.75,-2149.12 376.61,-2153.23"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge41" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M251.13,-2394.7C269.5,-2400.72 287.45,-2410.41 299,-2426 319.06,-2453.08 296.36,-2699.15 312,-2729 327.07,-2757.75 358.43,-2778.72 382.11,-2791.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="381.13,-2793.11 387.43,-2793.97 383.04,-2789.37 381.13,-2793.11"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="edge39" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M291.38,-2385C297.84,-2385 304.33,-2385 310.76,-2385"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="310.46,-2387.1 316.46,-2385 310.46,-2382.9 310.46,-2387.1"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="node53" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<g id="a_node53"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts" xlink:title="MakeMatchCommand.ts">
<path fill="#ddfeff" stroke="black" d="M464.71,-2455.25C464.71,-2455.25 362.04,-2455.25 362.04,-2455.25 358.96,-2455.25 355.88,-2452.17 355.88,-2449.08 355.88,-2449.08 355.88,-2442.92 355.88,-2442.92 355.88,-2439.83 358.96,-2436.75 362.04,-2436.75 362.04,-2436.75 464.71,-2436.75 464.71,-2436.75 467.79,-2436.75 470.88,-2439.83 470.88,-2442.92 470.88,-2442.92 470.88,-2449.08 470.88,-2449.08 470.88,-2452.17 467.79,-2455.25 464.71,-2455.25"/>
<text text-anchor="start" x="363.88" y="-2442.7" font-family="Helvetica,sans-Serif" font-size="9.00">MakeMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts -->
<g id="node54" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts</title>
<g id="a_node54"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts" xlink:title="MakeMatchCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M248.96,-2455.25C248.96,-2455.25 114.04,-2455.25 114.04,-2455.25 110.96,-2455.25 107.88,-2452.17 107.88,-2449.08 107.88,-2449.08 107.88,-2442.92 107.88,-2442.92 107.88,-2439.83 110.96,-2436.75 114.04,-2436.75 114.04,-2436.75 248.96,-2436.75 248.96,-2436.75 252.04,-2436.75 255.12,-2439.83 255.12,-2442.92 255.12,-2442.92 255.12,-2449.08 255.12,-2449.08 255.12,-2452.17 252.04,-2455.25 248.96,-2455.25"/>
<text text-anchor="start" x="115.88" y="-2442.7" font-family="Helvetica,sans-Serif" font-size="9.00">MakeMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts -->
<g id="edge44" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/Gaming/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M255.57,-2447.09C272.11,-2443.68 288.01,-2436.81 299,-2424 313.15,-2407.5 307.77,-2054.32 312,-2033 331.58,-1934.27 383.03,-1823.96 403.65,-1782.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="405.41,-1783.42 406.21,-1777.11 401.65,-1781.54 405.41,-1783.42"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts -->
<g id="edge47" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/Shared/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M255.38,-2446.92C271.93,-2443.51 287.88,-2436.69 299,-2424 333.76,-2384.33 281.4,-2229.96 312,-2187 326.75,-2166.29 353.88,-2156.02 376.22,-2150.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="376.4,-2153.05 381.85,-2149.79 375.55,-2148.93 376.4,-2153.05"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts -->
<g id="edge45" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/Gaming/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M255.47,-2445.62C271.62,-2442.16 287.36,-2435.65 299,-2424 321.06,-2401.93 291.47,-2378.5 312,-2355 384.34,-2272.21 473.5,-2358.98 546.75,-2277 608.31,-2208.11 637.64,-1905.48 644.27,-1827.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="646.34,-1827.84 644.74,-1821.69 642.16,-1827.5 646.34,-1827.84"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge46" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M250.72,-2455.68C269.2,-2461.69 287.3,-2471.39 299,-2487 331.29,-2530.1 286.74,-2681.44 312,-2729 327.22,-2757.67 358.56,-2778.65 382.19,-2791.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="381.2,-2793.06 387.5,-2793.93 383.11,-2789.33 381.2,-2793.06"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="edge43" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M255.55,-2446C284.86,-2446 318.38,-2446 346.88,-2446"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="346.53,-2448.1 352.53,-2446 346.53,-2443.9 346.53,-2448.1"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="node55" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<g id="a_node55"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts" xlink:title="UpdatePlayersStatusCommand.ts">
<path fill="#ddfeff" stroke="black" d="M484.21,-2577.25C484.21,-2577.25 342.54,-2577.25 342.54,-2577.25 339.46,-2577.25 336.38,-2574.17 336.38,-2571.08 336.38,-2571.08 336.38,-2564.92 336.38,-2564.92 336.38,-2561.83 339.46,-2558.75 342.54,-2558.75 342.54,-2558.75 484.21,-2558.75 484.21,-2558.75 487.29,-2558.75 490.38,-2561.83 490.38,-2564.92 490.38,-2564.92 490.38,-2571.08 490.38,-2571.08 490.38,-2574.17 487.29,-2577.25 484.21,-2577.25"/>
<text text-anchor="start" x="344.38" y="-2564.7" font-family="Helvetica,sans-Serif" font-size="9.00">UpdatePlayersStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts -->
<g id="node56" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts</title>
<g id="a_node56"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts" xlink:title="UpdatePlayersStatusCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M268.46,-2577.25C268.46,-2577.25 94.54,-2577.25 94.54,-2577.25 91.46,-2577.25 88.38,-2574.17 88.38,-2571.08 88.38,-2571.08 88.38,-2564.92 88.38,-2564.92 88.38,-2561.83 91.46,-2558.75 94.54,-2558.75 94.54,-2558.75 268.46,-2558.75 268.46,-2558.75 271.54,-2558.75 274.62,-2561.83 274.62,-2564.92 274.62,-2564.92 274.62,-2571.08 274.62,-2571.08 274.62,-2574.17 271.54,-2577.25 268.46,-2577.25"/>
<text text-anchor="start" x="96.38" y="-2564.7" font-family="Helvetica,sans-Serif" font-size="9.00">UpdatePlayersStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts -->
<g id="edge49" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M275.1,-2563.25C284.23,-2559.29 292.51,-2553.72 299,-2546 315.38,-2526.52 303.48,-1653.98 312,-1630 328.62,-1583.24 370.06,-1540.57 394.18,-1518.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="395.44,-1520.28 398.52,-1514.72 392.64,-1517.15 395.44,-1520.28"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="edge48" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M275.01,-2568C292.22,-2568 310.13,-2568 327.11,-2568"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="326.87,-2570.1 332.87,-2568 326.87,-2565.9 326.87,-2570.1"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="node57" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<g id="a_node57"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts" xlink:title="UpdateSinglePlayerStatusCommand.ts">
<path fill="#ddfeff" stroke="black" d="M495.08,-2699.25C495.08,-2699.25 331.67,-2699.25 331.67,-2699.25 328.58,-2699.25 325.5,-2696.17 325.5,-2693.08 325.5,-2693.08 325.5,-2686.92 325.5,-2686.92 325.5,-2683.83 328.58,-2680.75 331.67,-2680.75 331.67,-2680.75 495.08,-2680.75 495.08,-2680.75 498.17,-2680.75 501.25,-2683.83 501.25,-2686.92 501.25,-2686.92 501.25,-2693.08 501.25,-2693.08 501.25,-2696.17 498.17,-2699.25 495.08,-2699.25"/>
<text text-anchor="start" x="333.5" y="-2686.7" font-family="Helvetica,sans-Serif" font-size="9.00">UpdateSinglePlayerStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts -->
<g id="node58" class="node">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts</title>
<g id="a_node58"><a xlink:href="src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts" xlink:title="UpdateSinglePlayerStatusCommandHandler.ts">
<path fill="#ddfeff" stroke="black" d="M279.33,-2699.25C279.33,-2699.25 83.67,-2699.25 83.67,-2699.25 80.58,-2699.25 77.5,-2696.17 77.5,-2693.08 77.5,-2693.08 77.5,-2686.92 77.5,-2686.92 77.5,-2683.83 80.58,-2680.75 83.67,-2680.75 83.67,-2680.75 279.33,-2680.75 279.33,-2680.75 282.42,-2680.75 285.5,-2683.83 285.5,-2686.92 285.5,-2686.92 285.5,-2693.08 285.5,-2693.08 285.5,-2696.17 282.42,-2699.25 279.33,-2699.25"/>
<text text-anchor="start" x="85.5" y="-2686.7" font-family="Helvetica,sans-Serif" font-size="9.00">UpdateSinglePlayerStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts -->
<g id="edge51" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/Authentication/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.52,-2680.37C289.92,-2677.04 294.83,-2672.97 299,-2668 317.54,-2645.91 302.37,-1657.18 312,-1630 328.58,-1583.23 370.04,-1540.56 394.17,-1518.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="395.43,-1520.27 398.51,-1514.71 392.64,-1517.14 395.43,-1520.27"/>
</g>
<!-- src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="edge50" class="edge">
<title>src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M285.67,-2690C295.92,-2690 306.29,-2690 316.46,-2690"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="316.31,-2692.1 322.31,-2690 316.31,-2687.9 316.31,-2692.1"/>
</g>
<!-- src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge54" class="edge">
<title>src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M700.56,-2879C714.83,-2879 730.51,-2879 745.58,-2879"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="745.34,-2881.1 751.34,-2879 745.34,-2876.9 745.34,-2881.1"/>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts -->
<g id="node60" class="node">
<title>src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts</title>
<g id="a_node60"><a xlink:href="src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts" xlink:title="InMemoryMatchmakingQueueRepository.ts">
<path fill="#ddfeff" stroke="black" d="M271.08,-2910.25C271.08,-2910.25 91.92,-2910.25 91.92,-2910.25 88.83,-2910.25 85.75,-2907.17 85.75,-2904.08 85.75,-2904.08 85.75,-2897.92 85.75,-2897.92 85.75,-2894.83 88.83,-2891.75 91.92,-2891.75 91.92,-2891.75 271.08,-2891.75 271.08,-2891.75 274.17,-2891.75 277.25,-2894.83 277.25,-2897.92 277.25,-2897.92 277.25,-2904.08 277.25,-2904.08 277.25,-2907.17 274.17,-2910.25 271.08,-2910.25"/>
<text text-anchor="start" x="93.75" y="-2897.7" font-family="Helvetica,sans-Serif" font-size="9.00">InMemoryMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge55" class="edge">
<title>src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M205.81,-2891.27C247.39,-2873.9 334.39,-2837.57 381.11,-2818.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="381.83,-2820.03 386.56,-2815.78 380.21,-2816.16 381.83,-2820.03"/>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge57" class="edge">
<title>src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M277.71,-2905.61C390.17,-2909.72 582.72,-2912.56 746.75,-2894 754.05,-2893.17 761.72,-2891.92 769.19,-2890.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="769.4,-2892.58 774.86,-2889.32 768.56,-2888.46 769.4,-2892.58"/>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge56" class="edge">
<title>src/server/MatchMaking/infrastructure/repositories/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M277.64,-2896.48C369.3,-2892.13 505.28,-2885.67 583.76,-2881.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="583.65,-2884.05 589.54,-2881.67 583.45,-2879.86 583.65,-2884.05"/>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts -->
<g id="node61" class="node">
<title>src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts</title>
<g id="a_node61"><a xlink:href="src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts" xlink:title="ConvexMatchmakingQueueRepository.ts">
<path fill="#ddfeff" stroke="black" d="M266.58,-2944.25C266.58,-2944.25 96.42,-2944.25 96.42,-2944.25 93.33,-2944.25 90.25,-2941.17 90.25,-2938.08 90.25,-2938.08 90.25,-2931.92 90.25,-2931.92 90.25,-2928.83 93.33,-2925.75 96.42,-2925.75 96.42,-2925.75 266.58,-2925.75 266.58,-2925.75 269.67,-2925.75 272.75,-2928.83 272.75,-2931.92 272.75,-2931.92 272.75,-2938.08 272.75,-2938.08 272.75,-2941.17 269.67,-2944.25 266.58,-2944.25"/>
<text text-anchor="start" x="98.25" y="-2931.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge58" class="edge">
<title>src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M273.01,-2931.21C282.05,-2928.98 290.87,-2925.99 299,-2922 323.35,-2910.06 374.83,-2850.36 399.14,-2821.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="400.56,-2822.72 402.77,-2816.76 397.32,-2820.05 400.56,-2822.72"/>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge60" class="edge">
<title>src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M272.94,-2938.05C385.24,-2940.01 582.1,-2937.44 746.75,-2904 759.98,-2901.31 774.07,-2896.6 786.08,-2891.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="786.8,-2893.96 791.6,-2889.79 785.25,-2890.06 786.8,-2893.96"/>
</g>
<!-- src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge59" class="edge">
<title>src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M272.77,-2925.29C346.53,-2917.18 453.47,-2905.05 546.75,-2893 558.73,-2891.45 571.52,-2889.71 583.78,-2888"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="583.96,-2890.09 589.61,-2887.18 583.37,-2885.93 583.96,-2890.09"/>
</g>
<!-- src/server/Shared/DependencyInjection.ts -->
<g id="node62" class="node">
<title>src/server/Shared/DependencyInjection.ts</title>
<g id="a_node62"><a xlink:href="src/server/Shared/DependencyInjection.ts" xlink:title="DependencyInjection.ts">
<path fill="#ccffcc" stroke="black" d="M231.33,-2300.25C231.33,-2300.25 131.67,-2300.25 131.67,-2300.25 128.58,-2300.25 125.5,-2297.17 125.5,-2294.08 125.5,-2294.08 125.5,-2287.92 125.5,-2287.92 125.5,-2284.83 128.58,-2281.75 131.67,-2281.75 131.67,-2281.75 231.33,-2281.75 231.33,-2281.75 234.42,-2281.75 237.5,-2284.83 237.5,-2287.92 237.5,-2287.92 237.5,-2294.08 237.5,-2294.08 237.5,-2297.17 234.42,-2300.25 231.33,-2300.25"/>
<text text-anchor="start" x="133.5" y="-2287.7" font-family="Helvetica,sans-Serif" font-size="9.00">DependencyInjection.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/application/ports/CryptoPort.ts -->
<g id="node63" class="node">
<title>src/server/Shared/application/ports/CryptoPort.ts</title>
<g id="a_node63"><a xlink:href="src/server/Shared/application/ports/CryptoPort.ts" xlink:title="CryptoPort.ts">
<path fill="#ddfeff" stroke="black" d="M441.46,-2062.25C441.46,-2062.25 385.29,-2062.25 385.29,-2062.25 382.21,-2062.25 379.12,-2059.17 379.12,-2056.08 379.12,-2056.08 379.12,-2049.92 379.12,-2049.92 379.12,-2046.83 382.21,-2043.75 385.29,-2043.75 385.29,-2043.75 441.46,-2043.75 441.46,-2043.75 444.54,-2043.75 447.62,-2046.83 447.62,-2049.92 447.62,-2049.92 447.62,-2056.08 447.62,-2056.08 447.62,-2059.17 444.54,-2062.25 441.46,-2062.25"/>
<text text-anchor="start" x="387.12" y="-2049.7" font-family="Helvetica,sans-Serif" font-size="9.00">CryptoPort.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/application/ports/IdentityProvider.ts -->
<g id="node64" class="node">
<title>src/server/Shared/application/ports/IdentityProvider.ts</title>
<g id="a_node64"><a xlink:href="src/server/Shared/application/ports/IdentityProvider.ts" xlink:title="IdentityProvider.ts">
<path fill="#ddfeff" stroke="black" d="M451.21,-2093.25C451.21,-2093.25 375.54,-2093.25 375.54,-2093.25 372.46,-2093.25 369.38,-2090.17 369.38,-2087.08 369.38,-2087.08 369.38,-2080.92 369.38,-2080.92 369.38,-2077.83 372.46,-2074.75 375.54,-2074.75 375.54,-2074.75 451.21,-2074.75 451.21,-2074.75 454.29,-2074.75 457.38,-2077.83 457.38,-2080.92 457.38,-2080.92 457.38,-2087.08 457.38,-2087.08 457.38,-2090.17 454.29,-2093.25 451.21,-2093.25"/>
<text text-anchor="start" x="377.38" y="-2080.7" font-family="Helvetica,sans-Serif" font-size="9.00">IdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/application/queries/loadGameById.ts -->
<g id="node65" class="node">
<title>src/server/Shared/application/queries/loadGameById.ts</title>
<g id="a_node65"><a xlink:href="src/server/Shared/application/queries/loadGameById.ts" xlink:title="loadGameById.ts">
<path fill="#ccffcc" stroke="black" d="M450.08,-2216.25C450.08,-2216.25 376.67,-2216.25 376.67,-2216.25 373.58,-2216.25 370.5,-2213.17 370.5,-2210.08 370.5,-2210.08 370.5,-2203.92 370.5,-2203.92 370.5,-2200.83 373.58,-2197.75 376.67,-2197.75 376.67,-2197.75 450.08,-2197.75 450.08,-2197.75 453.17,-2197.75 456.25,-2200.83 456.25,-2203.92 456.25,-2203.92 456.25,-2210.08 456.25,-2210.08 456.25,-2213.17 453.17,-2216.25 450.08,-2216.25"/>
<text text-anchor="start" x="378.5" y="-2203.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameById.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/application/queries/loadGameList.ts -->
<g id="node66" class="node">
<title>src/server/Shared/application/queries/loadGameList.ts</title>
<g id="a_node66"><a xlink:href="src/server/Shared/application/queries/loadGameList.ts" xlink:title="loadGameList.ts">
<path fill="#ccffcc" stroke="black" d="M448.21,-2247.25C448.21,-2247.25 378.54,-2247.25 378.54,-2247.25 375.46,-2247.25 372.38,-2244.17 372.38,-2241.08 372.38,-2241.08 372.38,-2234.92 372.38,-2234.92 372.38,-2231.83 375.46,-2228.75 378.54,-2228.75 378.54,-2228.75 448.21,-2228.75 448.21,-2228.75 451.29,-2228.75 454.38,-2231.83 454.38,-2234.92 454.38,-2234.92 454.38,-2241.08 454.38,-2241.08 454.38,-2244.17 451.29,-2247.25 448.21,-2247.25"/>
<text text-anchor="start" x="380.38" y="-2234.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameList.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts -->
<g id="node67" class="node">
<title>src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts</title>
<g id="a_node67"><a xlink:href="src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts" xlink:title="UuidIdentityProvider.ts">
<path fill="#ddfeff" stroke="black" d="M229.08,-2062.25C229.08,-2062.25 133.92,-2062.25 133.92,-2062.25 130.83,-2062.25 127.75,-2059.17 127.75,-2056.08 127.75,-2056.08 127.75,-2049.92 127.75,-2049.92 127.75,-2046.83 130.83,-2043.75 133.92,-2043.75 133.92,-2043.75 229.08,-2043.75 229.08,-2043.75 232.17,-2043.75 235.25,-2046.83 235.25,-2049.92 235.25,-2049.92 235.25,-2056.08 235.25,-2056.08 235.25,-2059.17 232.17,-2062.25 229.08,-2062.25"/>
<text text-anchor="start" x="135.75" y="-2049.7" font-family="Helvetica,sans-Serif" font-size="9.00">UuidIdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/Shared/application/ports/CryptoPort.ts -->
<g id="edge61" class="edge">
<title>src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/Shared/application/ports/CryptoPort.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M235.53,-2053C276.4,-2053 332.17,-2053 370.08,-2053"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="369.81,-2055.1 375.81,-2053 369.81,-2050.9 369.81,-2055.1"/>
</g>
<!-- src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/Shared/application/ports/IdentityProvider.ts -->
<g id="edge62" class="edge">
<title>src/server/Shared/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/Shared/application/ports/IdentityProvider.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M235.53,-2060.15C273.09,-2065.22 323.23,-2071.98 360.52,-2077.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="360,-2079.06 366.23,-2077.78 360.57,-2074.89 360,-2079.06"/>
</g>
<!-- src/server/Shared/infrastructure/gateways/Context/ConvexContext.ts -->
<g id="node68" class="node">
<title>src/server/Shared/infrastructure/gateways/Context/ConvexContext.ts</title>
<g id="a_node68"><a xlink:href="src/server/Shared/infrastructure/gateways/Context/ConvexContext.ts" xlink:title="ConvexContext.ts">
<path fill="#ddfeff" stroke="black" d="M219.33,-2192.25C219.33,-2192.25 143.67,-2192.25 143.67,-2192.25 140.58,-2192.25 137.5,-2189.17 137.5,-2186.08 137.5,-2186.08 137.5,-2179.92 137.5,-2179.92 137.5,-2176.83 140.58,-2173.75 143.67,-2173.75 143.67,-2173.75 219.33,-2173.75 219.33,-2173.75 222.42,-2173.75 225.5,-2176.83 225.5,-2179.92 225.5,-2179.92 225.5,-2186.08 225.5,-2186.08 225.5,-2189.17 222.42,-2192.25 219.33,-2192.25"/>
<text text-anchor="start" x="145.5" y="-2179.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexContext.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/Shared/application/ports/Context.ts -->
<g id="edge63" class="edge">
<title>src/server/Shared/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/Shared/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M225.74,-2176.04C269.15,-2169.05 335.27,-2158.41 376.05,-2151.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="376.27,-2153.94 381.86,-2150.91 375.6,-2149.79 376.27,-2153.94"/>
</g>
<!-- src/server/Shared/infrastructure/gateways/Time/RealTimeService.ts -->
<g id="node69" class="node">
<title>src/server/Shared/infrastructure/gateways/Time/RealTimeService.ts</title>
<g id="a_node69"><a xlink:href="src/server/Shared/infrastructure/gateways/Time/RealTimeService.ts" xlink:title="RealTimeService.ts">
<path fill="#ddfeff" stroke="black" d="M223.08,-2131.25C223.08,-2131.25 139.92,-2131.25 139.92,-2131.25 136.83,-2131.25 133.75,-2128.17 133.75,-2125.08 133.75,-2125.08 133.75,-2118.92 133.75,-2118.92 133.75,-2115.83 136.83,-2112.75 139.92,-2112.75 139.92,-2112.75 223.08,-2112.75 223.08,-2112.75 226.17,-2112.75 229.25,-2115.83 229.25,-2118.92 229.25,-2118.92 229.25,-2125.08 229.25,-2125.08 229.25,-2128.17 226.17,-2131.25 223.08,-2131.25"/>
<text text-anchor="start" x="141.75" y="-2118.7" font-family="Helvetica,sans-Serif" font-size="9.00">RealTimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/Shared/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/Shared/application/ports/TimeService.ts -->
<g id="edge64" class="edge">
<title>src/server/Shared/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/Shared/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M229.57,-2120.57C269.54,-2119.35 326.57,-2117.61 366.27,-2116.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="366.25,-2118.51 372.18,-2116.22 366.12,-2114.31 366.25,-2118.51"/>
</g>
</g>
</svg>
    <script>
      var gMode = new Mode();

var title2ElementMap = (function makeElementMap() {
  /** @type {NodeListOf<SVGGElement>} */
  var nodes = document.querySelectorAll(".node");
  /** @type {NodeListOf<SVGGElement>} */
  var edges = document.querySelectorAll(".edge");
  return new Title2ElementMap(edges, nodes);
})();

function getHoverHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function hoverHighlightHandler(pMouseEvent) {
    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (
      currentHighlightedTitle !== closestTitleText &&
      gMode.get() === gMode.HOVER
    ) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}

function getSelectHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function selectHighlightHandler(pMouseEvent) {
    pMouseEvent.preventDefault();

    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (closestNodeOrEdge) {
      gMode.setToSelect();
    } else {
      gMode.setToHover();
    }
    if (currentHighlightedTitle !== closestTitleText) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}
function Mode() {
  var HOVER = 1;
  var SELECT = 2;

  function setToHover() {
    this._mode = HOVER;
  }
  function setToSelect() {
    this._mode = SELECT;
  }

  /**
   * @returns {number}
   */
  function get() {
    return this._mode || HOVER;
  }

  return {
    HOVER: HOVER,
    SELECT: SELECT,
    setToHover: setToHover,
    setToSelect: setToSelect,
    get: get,
  };
}

/**
 *
 * @param {SVGGelement[]} pEdges
 * @param {SVGGElement[]} pNodes
 * @return {{get: (pTitleText:string) => SVGGElement[]}}
 */
function Title2ElementMap(pEdges, pNodes) {
  /* {{[key: string]: SVGGElement[]}} */
  var elementMap = buildMap(pEdges, pNodes);

  /**
   * @param {NodeListOf<SVGGElement>} pEdges
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement[]}}
   */
  function buildMap(pEdges, pNodes) {
    var title2NodeMap = buildTitle2NodeMap(pNodes);

    return nodeListToArray(pEdges).reduce(addEdgeToMap(title2NodeMap), {});
  }
  /**
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement}}
   */
  function buildTitle2NodeMap(pNodes) {
    return nodeListToArray(pNodes).reduce(addNodeToMap, {});
  }

  function addNodeToMap(pMap, pNode) {
    var titleText = getTitleText(pNode);

    if (titleText) {
      pMap[titleText] = pNode;
    }
    return pMap;
  }

  function addEdgeToMap(pNodeMap) {
    return function (pEdgeMap, pEdge) {
      /** @type {string} */
      var titleText = getTitleText(pEdge);

      if (titleText) {
        var edge = pryEdgeFromTitle(titleText);

        pEdgeMap[titleText] = [pNodeMap[edge.from], pNodeMap[edge.to]];
        (pEdgeMap[edge.from] || (pEdgeMap[edge.from] = [])).push(pEdge);
        (pEdgeMap[edge.to] || (pEdgeMap[edge.to] = [])).push(pEdge);
      }
      return pEdgeMap;
    };
  }

  /**
   *
   * @param {string} pString
   * @return {{from?: string; to?:string;}}
   */
  function pryEdgeFromTitle(pString) {
    var nodeNames = pString.split(/\s*->\s*/);

    return {
      from: nodeNames.shift(),
      to: nodeNames.shift(),
    };
  }
  /**
   *
   * @param {string} pTitleText
   * @return {SVGGElement[]}
   */
  function get(pTitleText) {
    return (pTitleText && elementMap[pTitleText]) || [];
  }
  return {
    get: get,
  };
}

/**
 * @param {SVGGElement} pGElement
 * @return {string?}
 */
function getTitleText(pGElement) {
  /** @type {SVGTitleElement} */
  var title = pGElement && pGElement.querySelector("title");
  /** @type {string} */
  var titleText = title && title.textContent;

  if (titleText) {
    titleText = titleText.trim();
  }
  return titleText;
}

/**
 * @param {NodeListOf<Element>} pNodeList
 * @return {Element[]}
 */
function nodeListToArray(pNodeList) {
  var lReturnValue = [];

  pNodeList.forEach(function (pElement) {
    lReturnValue.push(pElement);
  });

  return lReturnValue;
}

function resetNodesAndEdges() {
  nodeListToArray(document.querySelectorAll(".current")).forEach(
    removeHighlight,
  );
}

/**
 * @param {SVGGElement} pGElement
 */
function removeHighlight(pGElement) {
  if (pGElement && pGElement.classList) {
    pGElement.classList.remove("current");
  }
}

/**
 * @param {SVGGElement} pGroup
 */
function addHighlight(pGroup) {
  if (pGroup && pGroup.classList) {
    pGroup.classList.add("current");
  }
}

var gHints = {
  HIDDEN: 1,
  SHOWN: 2,
  state: 1, // === HIDDEN
  show: function () {
    document.getElementById("hints").removeAttribute("style");
    gHints.state = gHints.SHOWN;
  },
  hide: function () {
    document.getElementById("hints").style = "display:none";
    gHints.state = gHints.HIDDEN;
  },
  toggle: function () {
    if ((gHints.state || gHints.HIDDEN) === gHints.HIDDEN) {
      gHints.show();
    } else {
      gHints.hide();
    }
  },
};

/** @param {KeyboardEvent} pKeyboardEvent */
function keyboardEventHandler(pKeyboardEvent) {
  if (pKeyboardEvent.key === "Escape") {
    resetNodesAndEdges();
    gMode.setToHover();
    gHints.hide();
  }
  if (pKeyboardEvent.key === "F1") {
    pKeyboardEvent.preventDefault();
    gHints.toggle();
  }
}

document.addEventListener("contextmenu", getSelectHandler(title2ElementMap));
document.addEventListener("mouseover", getHoverHandler(title2ElementMap));
document.addEventListener("keydown", keyboardEventHandler);
document.getElementById("close-hints").addEventListener("click", gHints.hide);
document.getElementById("button_help").addEventListener("click", gHints.toggle);
document.querySelector("svg").insertAdjacentHTML(
  "afterbegin",
  `<linearGradient id="edgeGradient">
      <stop offset="0%" stop-color="fuchsia"/>
      <stop offset="100%" stop-color="purple"/>
   </linearGradient>
  `,
);

// Add a small increment to the last value of the path to make gradients on
// horizontal paths work. Without them all browsers I tested with (firefox,
// chrome) do not render the gradient, but instead make the line transparent
// (or the color of the background, I haven't looked into it that deeply,
// but for the hack it doesn't matter which).
function skewLineABit(lDrawingInstructions) {
  var lLastValue = lDrawingInstructions.match(/(\d+\.?\d*)$/)[0];
  // Smaller values than .001 _should_ work as well, but don't in all
  // cases. Even this value is so small that it is not visible to the
  // human eye (tested with the two I have at my disposal).
  var lIncrement = 0.001;
  var lNewLastValue = parseFloat(lLastValue) + lIncrement;

  return lDrawingInstructions.replace(lLastValue, lNewLastValue);
}

nodeListToArray(document.querySelectorAll("path"))
  .filter(function (pElement) {
    return pElement.parentElement.classList.contains("edge");
  })
  .forEach(function (pElement) {
    pElement.attributes.d.value = skewLineABit(pElement.attributes.d.value);
  });

    </script>
  </body>
</html>
