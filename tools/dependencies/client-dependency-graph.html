<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <title>dependency graph</title>
    <style>
      .node:active path,
.node:hover path,
.node.current path,
.node:active polygon,
.node:hover polygon,
.node.current polygon {
  stroke: fuchsia;
  stroke-width: 2;
}

.edge:active path,
.edge:hover path,
.edge.current path,
.edge:active ellipse,
.edge:hover ellipse,
.edge.current ellipse {
  stroke: url(#edgeGradient);
  stroke-width: 3;
  stroke-opacity: 1;
}

.edge:active polygon,
.edge:hover polygon,
.edge.current polygon {
  stroke: fuchsia;
  stroke-width: 3;
  fill: fuchsia;
  stroke-opacity: 1;
  fill-opacity: 1;
}

.edge:active text,
.edge:hover text {
  fill: fuchsia;
}

.cluster path {
  stroke-width: 3;
}

.cluster:active path,
.cluster:hover path {
  fill: #ffff0011;
}

div.hint {
  background-color: #000000aa;
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  border-radius: 1rem;
  position: fixed;
  top: calc(50% - 4em);
  right: calc(50% - 10em);
  border: none;
  padding: 1em 3em 1em 1em;
}

.hint button {
  position: absolute;
  font-weight: bolder;
  right: 0.6em;
  top: 0.6em;
  color: inherit;
  background-color: inherit;
  border: 1px solid currentColor;
  border-radius: 1em;
  margin-left: 0.6em;
}

.hint a {
  color: inherit;
}

#button_help {
  color: white;
  background-color: #00000011;
  border-radius: 1em;
  position: fixed;
  top: 1em;
  right: 1em;
  font-size: 24pt;
  font-weight: bolder;
  width: 2em;
  height: 2em;
  border: none;
}

#button_help:hover {
  cursor: pointer;
  background-color: #00000077;
}

@media print {
  #button_help {
    display: none;
  }

  div.hint {
    display: none;
  }
}

    </style>
  </head>
  <body>
    <button id="button_help">?</button>
    <div id="hints" class="hint" style="display: none">
      <button id="close-hints">x</button>
      <span id="hint-text"></span>
      <ul>
        <li><b>Hover</b> - highlight</li>
        <li><b>Right-click</b> - pin highlight</li>
        <li><b>ESC</b> - clear</li>
      </ul>
    </div>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.0 (20241103.1931)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="1801pt" height="6956pt"
 viewBox="0.00 0.00 1801.25 6956.10" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 6952.1)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-6952.1 1797.25,-6952.1 1797.25,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-6119.1C20,-6119.1 341.38,-6119.1 341.38,-6119.1 347.38,-6119.1 353.38,-6125.1 353.38,-6131.1 353.38,-6131.1 353.38,-6928.1 353.38,-6928.1 353.38,-6934.1 347.38,-6940.1 341.38,-6940.1 341.38,-6940.1 20,-6940.1 20,-6940.1 14,-6940.1 8,-6934.1 8,-6928.1 8,-6928.1 8,-6131.1 8,-6131.1 8,-6125.1 14,-6119.1 20,-6119.1"/>
<text text-anchor="middle" x="180.69" y="-6927.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_app/[locale]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M28,-6153.1C28,-6153.1 160.25,-6153.1 160.25,-6153.1 166.25,-6153.1 172.25,-6159.1 172.25,-6165.1 172.25,-6165.1 172.25,-6875.1 172.25,-6875.1 172.25,-6881.1 166.25,-6887.1 160.25,-6887.1 160.25,-6887.1 28,-6887.1 28,-6887.1 22,-6887.1 16,-6881.1 16,-6875.1 16,-6875.1 16,-6165.1 16,-6165.1 16,-6159.1 22,-6153.1 28,-6153.1"/>
<text text-anchor="middle" x="94.12" y="-6874.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[locale]</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_app/[locale]/(connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M36,-6187.1C36,-6187.1 152.25,-6187.1 152.25,-6187.1 158.25,-6187.1 164.25,-6193.1 164.25,-6199.1 164.25,-6199.1 164.25,-6665.1 164.25,-6665.1 164.25,-6671.1 158.25,-6677.1 152.25,-6677.1 152.25,-6677.1 36,-6677.1 36,-6677.1 30,-6677.1 24,-6671.1 24,-6665.1 24,-6665.1 24,-6199.1 24,-6199.1 24,-6193.1 30,-6187.1 36,-6187.1"/>
<text text-anchor="middle" x="94.12" y="-6664.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(connected)</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_app/[locale]/(connected)/games</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M44,-6195.1C44,-6195.1 144.25,-6195.1 144.25,-6195.1 150.25,-6195.1 156.25,-6201.1 156.25,-6207.1 156.25,-6207.1 156.25,-6542.1 156.25,-6542.1 156.25,-6548.1 150.25,-6554.1 144.25,-6554.1 144.25,-6554.1 44,-6554.1 44,-6554.1 38,-6554.1 32,-6548.1 32,-6542.1 32,-6542.1 32,-6207.1 32,-6207.1 32,-6201.1 38,-6195.1 44,-6195.1"/>
<text text-anchor="middle" x="94.12" y="-6541.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">games</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M52,-6203.1C52,-6203.1 136.25,-6203.1 136.25,-6203.1 142.25,-6203.1 148.25,-6209.1 148.25,-6215.1 148.25,-6215.1 148.25,-6489.1 148.25,-6489.1 148.25,-6495.1 142.25,-6501.1 136.25,-6501.1 136.25,-6501.1 52,-6501.1 52,-6501.1 46,-6501.1 40,-6495.1 40,-6489.1 40,-6489.1 40,-6215.1 40,-6215.1 40,-6209.1 46,-6203.1 52,-6203.1"/>
<text text-anchor="middle" x="94.12" y="-6488.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[gameId]</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M60,-6298.1C60,-6298.1 128.25,-6298.1 128.25,-6298.1 134.25,-6298.1 140.25,-6304.1 140.25,-6310.1 140.25,-6310.1 140.25,-6462.1 140.25,-6462.1 140.25,-6468.1 134.25,-6474.1 128.25,-6474.1 128.25,-6474.1 60,-6474.1 60,-6474.1 54,-6474.1 48,-6468.1 48,-6462.1 48,-6462.1 48,-6310.1 48,-6310.1 48,-6304.1 54,-6298.1 60,-6298.1"/>
<text text-anchor="middle" x="94.12" y="-6461.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deck&#45;builder</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M68,-6363.1C68,-6363.1 120.25,-6363.1 120.25,-6363.1 126.25,-6363.1 132.25,-6369.1 132.25,-6375.1 132.25,-6375.1 132.25,-6435.1 132.25,-6435.1 132.25,-6441.1 126.25,-6447.1 120.25,-6447.1 120.25,-6447.1 68,-6447.1 68,-6447.1 62,-6447.1 56,-6441.1 56,-6435.1 56,-6435.1 56,-6375.1 56,-6375.1 56,-6369.1 62,-6363.1 68,-6363.1"/>
<text text-anchor="middle" x="94.12" y="-6434.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[deckId]</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/play</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-6211.1C71.12,-6211.1 117.12,-6211.1 117.12,-6211.1 123.12,-6211.1 129.12,-6217.1 129.12,-6223.1 129.12,-6223.1 129.12,-6252.1 129.12,-6252.1 129.12,-6258.1 123.12,-6264.1 117.12,-6264.1 117.12,-6264.1 71.12,-6264.1 71.12,-6264.1 65.12,-6264.1 59.12,-6258.1 59.12,-6252.1 59.12,-6252.1 59.12,-6223.1 59.12,-6223.1 59.12,-6217.1 65.12,-6211.1 71.12,-6211.1"/>
<text text-anchor="middle" x="94.12" y="-6251.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">play</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_app/[locale]/(connected)/matches</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-6562.1C63.12,-6562.1 125.12,-6562.1 125.12,-6562.1 131.12,-6562.1 137.12,-6568.1 137.12,-6574.1 137.12,-6574.1 137.12,-6638.1 137.12,-6638.1 137.12,-6644.1 131.12,-6650.1 125.12,-6650.1 125.12,-6650.1 63.12,-6650.1 63.12,-6650.1 57.12,-6650.1 51.12,-6644.1 51.12,-6638.1 51.12,-6638.1 51.12,-6574.1 51.12,-6574.1 51.12,-6568.1 57.12,-6562.1 63.12,-6562.1"/>
<text text-anchor="middle" x="94.12" y="-6637.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">matches</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_app/[locale]/(connected)/matches/[matchId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-6570.1C71.12,-6570.1 117.12,-6570.1 117.12,-6570.1 123.12,-6570.1 129.12,-6576.1 129.12,-6582.1 129.12,-6582.1 129.12,-6611.1 129.12,-6611.1 129.12,-6617.1 123.12,-6623.1 117.12,-6623.1 117.12,-6623.1 71.12,-6623.1 71.12,-6623.1 65.12,-6623.1 59.12,-6617.1 59.12,-6611.1 59.12,-6611.1 59.12,-6582.1 59.12,-6582.1 59.12,-6576.1 65.12,-6570.1 71.12,-6570.1"/>
<text text-anchor="middle" x="94.12" y="-6610.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[matchId]</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-6685.1C63.12,-6685.1 125.12,-6685.1 125.12,-6685.1 131.12,-6685.1 137.12,-6691.1 137.12,-6697.1 137.12,-6697.1 137.12,-6787.1 137.12,-6787.1 137.12,-6793.1 131.12,-6799.1 125.12,-6799.1 125.12,-6799.1 63.12,-6799.1 63.12,-6799.1 57.12,-6799.1 51.12,-6793.1 51.12,-6787.1 51.12,-6787.1 51.12,-6697.1 51.12,-6697.1 51.12,-6691.1 57.12,-6685.1 63.12,-6685.1"/>
<text text-anchor="middle" x="94.12" y="-6786.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(not&#45;connected)</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)/signin</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-6693.1C71.12,-6693.1 117.12,-6693.1 117.12,-6693.1 123.12,-6693.1 129.12,-6699.1 129.12,-6705.1 129.12,-6705.1 129.12,-6734.1 129.12,-6734.1 129.12,-6740.1 123.12,-6746.1 117.12,-6746.1 117.12,-6746.1 71.12,-6746.1 71.12,-6746.1 65.12,-6746.1 59.12,-6740.1 59.12,-6734.1 59.12,-6734.1 59.12,-6705.1 59.12,-6705.1 59.12,-6699.1 65.12,-6693.1 71.12,-6693.1"/>
<text text-anchor="middle" x="94.12" y="-6733.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">signin</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_app/[locale]/access&#45;denied</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M67.12,-6807.1C67.12,-6807.1 122.12,-6807.1 122.12,-6807.1 128.12,-6807.1 134.12,-6813.1 134.12,-6819.1 134.12,-6819.1 134.12,-6848.1 134.12,-6848.1 134.12,-6854.1 128.12,-6860.1 122.12,-6860.1 122.12,-6860.1 67.12,-6860.1 67.12,-6860.1 61.12,-6860.1 55.12,-6854.1 55.12,-6848.1 55.12,-6848.1 55.12,-6819.1 55.12,-6819.1 55.12,-6813.1 61.12,-6807.1 67.12,-6807.1"/>
<text text-anchor="middle" x="94.62" y="-6847.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">access&#45;denied</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M192.25,-265.1C192.25,-265.1 1773.25,-265.1 1773.25,-265.1 1779.25,-265.1 1785.25,-271.1 1785.25,-277.1 1785.25,-277.1 1785.25,-6099.1 1785.25,-6099.1 1785.25,-6105.1 1779.25,-6111.1 1773.25,-6111.1 1773.25,-6111.1 192.25,-6111.1 192.25,-6111.1 186.25,-6111.1 180.25,-6105.1 180.25,-6099.1 180.25,-6099.1 180.25,-277.1 180.25,-277.1 180.25,-271.1 186.25,-265.1 192.25,-265.1"/>
<text text-anchor="middle" x="982.75" y="-6098.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src/client</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M200.25,-273.1C200.25,-273.1 1765.25,-273.1 1765.25,-273.1 1771.25,-273.1 1777.25,-279.1 1777.25,-285.1 1777.25,-285.1 1777.25,-6072.1 1777.25,-6072.1 1777.25,-6078.1 1771.25,-6084.1 1765.25,-6084.1 1765.25,-6084.1 200.25,-6084.1 200.25,-6084.1 194.25,-6084.1 188.25,-6078.1 188.25,-6072.1 188.25,-6072.1 188.25,-285.1 188.25,-285.1 188.25,-279.1 194.25,-273.1 200.25,-273.1"/>
<text text-anchor="middle" x="982.75" y="-6071.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">client</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/client/Authentication</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M415.12,-281.1C415.12,-281.1 804.75,-281.1 804.75,-281.1 810.75,-281.1 816.75,-287.1 816.75,-293.1 816.75,-293.1 816.75,-755.1 816.75,-755.1 816.75,-761.1 810.75,-767.1 804.75,-767.1 804.75,-767.1 415.12,-767.1 415.12,-767.1 409.12,-767.1 403.12,-761.1 403.12,-755.1 403.12,-755.1 403.12,-293.1 403.12,-293.1 403.12,-287.1 409.12,-281.1 415.12,-281.1"/>
<text text-anchor="middle" x="609.94" y="-754.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Authentication</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/client/Authentication/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M423.12,-289.1C423.12,-289.1 796.75,-289.1 796.75,-289.1 802.75,-289.1 808.75,-295.1 808.75,-301.1 808.75,-301.1 808.75,-728.1 808.75,-728.1 808.75,-734.1 802.75,-740.1 796.75,-740.1 796.75,-740.1 423.12,-740.1 423.12,-740.1 417.12,-740.1 411.12,-734.1 411.12,-728.1 411.12,-728.1 411.12,-301.1 411.12,-301.1 411.12,-295.1 417.12,-289.1 423.12,-289.1"/>
<text text-anchor="middle" x="609.94" y="-727.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M431.12,-297.1C431.12,-297.1 788.75,-297.1 788.75,-297.1 794.75,-297.1 800.75,-303.1 800.75,-309.1 800.75,-309.1 800.75,-509.1 800.75,-509.1 800.75,-515.1 794.75,-521.1 788.75,-521.1 788.75,-521.1 431.12,-521.1 431.12,-521.1 425.12,-521.1 419.12,-515.1 419.12,-509.1 419.12,-509.1 419.12,-309.1 419.12,-309.1 419.12,-303.1 425.12,-297.1 431.12,-297.1"/>
<text text-anchor="middle" x="609.94" y="-508.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M439.12,-305.1C439.12,-305.1 780.75,-305.1 780.75,-305.1 786.75,-305.1 792.75,-311.1 792.75,-317.1 792.75,-317.1 792.75,-482.1 792.75,-482.1 792.75,-488.1 786.75,-494.1 780.75,-494.1 780.75,-494.1 439.12,-494.1 439.12,-494.1 433.12,-494.1 427.12,-488.1 427.12,-482.1 427.12,-482.1 427.12,-317.1 427.12,-317.1 427.12,-311.1 433.12,-305.1 439.12,-305.1"/>
<text text-anchor="middle" x="609.94" y="-481.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/components/app/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M447.12,-313.1C447.12,-313.1 772.75,-313.1 772.75,-313.1 778.75,-313.1 784.75,-319.1 784.75,-325.1 784.75,-325.1 784.75,-455.1 784.75,-455.1 784.75,-461.1 778.75,-467.1 772.75,-467.1 772.75,-467.1 447.12,-467.1 447.12,-467.1 441.12,-467.1 435.12,-461.1 435.12,-455.1 435.12,-455.1 435.12,-325.1 435.12,-325.1 435.12,-319.1 441.12,-313.1 447.12,-313.1"/>
<text text-anchor="middle" x="609.94" y="-454.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/components/app/Auth/SignIn</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M455.12,-321.1C455.12,-321.1 764.75,-321.1 764.75,-321.1 770.75,-321.1 776.75,-327.1 776.75,-333.1 776.75,-333.1 776.75,-428.1 776.75,-428.1 776.75,-434.1 770.75,-440.1 764.75,-440.1 764.75,-440.1 455.12,-440.1 455.12,-440.1 449.12,-440.1 443.12,-434.1 443.12,-428.1 443.12,-428.1 443.12,-333.1 443.12,-333.1 443.12,-327.1 449.12,-321.1 455.12,-321.1"/>
<text text-anchor="middle" x="609.94" y="-427.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignIn</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.12,-360.1C463.12,-360.1 537.12,-360.1 537.12,-360.1 543.12,-360.1 549.12,-366.1 549.12,-372.1 549.12,-372.1 549.12,-401.1 549.12,-401.1 549.12,-407.1 543.12,-413.1 537.12,-413.1 537.12,-413.1 463.12,-413.1 463.12,-413.1 457.12,-413.1 451.12,-407.1 451.12,-401.1 451.12,-401.1 451.12,-372.1 451.12,-372.1 451.12,-366.1 457.12,-360.1 463.12,-360.1"/>
<text text-anchor="middle" x="500.12" y="-400.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInButton</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M673,-329.1C673,-329.1 756.75,-329.1 756.75,-329.1 762.75,-329.1 768.75,-335.1 768.75,-341.1 768.75,-341.1 768.75,-401.1 768.75,-401.1 768.75,-407.1 762.75,-413.1 756.75,-413.1 756.75,-413.1 673,-413.1 673,-413.1 667,-413.1 661,-407.1 661,-401.1 661,-401.1 661,-341.1 661,-341.1 661,-335.1 667,-329.1 673,-329.1"/>
<text text-anchor="middle" x="714.88" y="-400.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInForm</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/pages</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M432.88,-529.1C432.88,-529.1 567.38,-529.1 567.38,-529.1 573.38,-529.1 579.38,-535.1 579.38,-541.1 579.38,-541.1 579.38,-701.1 579.38,-701.1 579.38,-707.1 573.38,-713.1 567.38,-713.1 567.38,-713.1 432.88,-713.1 432.88,-713.1 426.88,-713.1 420.88,-707.1 420.88,-701.1 420.88,-701.1 420.88,-541.1 420.88,-541.1 420.88,-535.1 426.88,-529.1 432.88,-529.1"/>
<text text-anchor="middle" x="500.12" y="-700.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">pages</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/pages/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M440.88,-537.1C440.88,-537.1 559.38,-537.1 559.38,-537.1 565.38,-537.1 571.38,-543.1 571.38,-549.1 571.38,-549.1 571.38,-674.1 571.38,-674.1 571.38,-680.1 565.38,-686.1 559.38,-686.1 559.38,-686.1 440.88,-686.1 440.88,-686.1 434.88,-686.1 428.88,-680.1 428.88,-674.1 428.88,-674.1 428.88,-549.1 428.88,-549.1 428.88,-543.1 434.88,-537.1 440.88,-537.1"/>
<text text-anchor="middle" x="500.12" y="-673.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/pages/Auth/AccessDeniedPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M448.88,-606.1C448.88,-606.1 551.38,-606.1 551.38,-606.1 557.38,-606.1 563.38,-612.1 563.38,-618.1 563.38,-618.1 563.38,-647.1 563.38,-647.1 563.38,-653.1 557.38,-659.1 551.38,-659.1 551.38,-659.1 448.88,-659.1 448.88,-659.1 442.88,-659.1 436.88,-653.1 436.88,-647.1 436.88,-647.1 436.88,-618.1 436.88,-618.1 436.88,-612.1 442.88,-606.1 448.88,-606.1"/>
<text text-anchor="middle" x="500.12" y="-646.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AccessDeniedPage</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/client/Authentication/infrastructure/pages/Auth/SignInPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M465.38,-545.1C465.38,-545.1 534.88,-545.1 534.88,-545.1 540.88,-545.1 546.88,-551.1 546.88,-557.1 546.88,-557.1 546.88,-586.1 546.88,-586.1 546.88,-592.1 540.88,-598.1 534.88,-598.1 534.88,-598.1 465.38,-598.1 465.38,-598.1 459.38,-598.1 453.38,-592.1 453.38,-586.1 453.38,-586.1 453.38,-557.1 453.38,-557.1 453.38,-551.1 459.38,-545.1 465.38,-545.1"/>
<text text-anchor="middle" x="500.12" y="-585.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInPage</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/client/DeckBuilding</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M208.25,-2451.1C208.25,-2451.1 1757.25,-2451.1 1757.25,-2451.1 1763.25,-2451.1 1769.25,-2457.1 1769.25,-2463.1 1769.25,-2463.1 1769.25,-5460.1 1769.25,-5460.1 1769.25,-5466.1 1763.25,-5472.1 1757.25,-5472.1 1757.25,-5472.1 208.25,-5472.1 208.25,-5472.1 202.25,-5472.1 196.25,-5466.1 196.25,-5460.1 196.25,-5460.1 196.25,-2463.1 196.25,-2463.1 196.25,-2457.1 202.25,-2451.1 208.25,-2451.1"/>
<text text-anchor="middle" x="982.75" y="-5459.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilding</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/client/DeckBuilding/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M653.38,-2459.1C653.38,-2459.1 1392.88,-2459.1 1392.88,-2459.1 1398.88,-2459.1 1404.88,-2465.1 1404.88,-2471.1 1404.88,-2471.1 1404.88,-4592.1 1404.88,-4592.1 1404.88,-4598.1 1398.88,-4604.1 1392.88,-4604.1 1392.88,-4604.1 653.38,-4604.1 653.38,-4604.1 647.38,-4604.1 641.38,-4598.1 641.38,-4592.1 641.38,-4592.1 641.38,-2471.1 641.38,-2471.1 641.38,-2465.1 647.38,-2459.1 653.38,-2459.1"/>
<text text-anchor="middle" x="1023.12" y="-4591.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M863.62,-2467.1C863.62,-2467.1 1384.88,-2467.1 1384.88,-2467.1 1390.88,-2467.1 1396.88,-2473.1 1396.88,-2479.1 1396.88,-2479.1 1396.88,-3275.1 1396.88,-3275.1 1396.88,-3281.1 1390.88,-3287.1 1384.88,-3287.1 1384.88,-3287.1 863.62,-3287.1 863.62,-3287.1 857.62,-3287.1 851.62,-3281.1 851.62,-3275.1 851.62,-3275.1 851.62,-2479.1 851.62,-2479.1 851.62,-2473.1 857.62,-2467.1 863.62,-2467.1"/>
<text text-anchor="middle" x="1124.25" y="-3274.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/addCardToDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M908.38,-2902.1C908.38,-2902.1 1213.75,-2902.1 1213.75,-2902.1 1219.75,-2902.1 1225.75,-2908.1 1225.75,-2914.1 1225.75,-2914.1 1225.75,-2943.1 1225.75,-2943.1 1225.75,-2949.1 1219.75,-2955.1 1213.75,-2955.1 1213.75,-2955.1 908.38,-2955.1 908.38,-2955.1 902.38,-2955.1 896.38,-2949.1 896.38,-2943.1 896.38,-2943.1 896.38,-2914.1 896.38,-2914.1 896.38,-2908.1 902.38,-2902.1 908.38,-2902.1"/>
<text text-anchor="middle" x="1061.06" y="-2942.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">addCardToDeck</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/clearDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M911.38,-2536.1C911.38,-2536.1 989.12,-2536.1 989.12,-2536.1 995.12,-2536.1 1001.12,-2542.1 1001.12,-2548.1 1001.12,-2548.1 1001.12,-2577.1 1001.12,-2577.1 1001.12,-2583.1 995.12,-2589.1 989.12,-2589.1 989.12,-2589.1 911.38,-2589.1 911.38,-2589.1 905.38,-2589.1 899.38,-2583.1 899.38,-2577.1 899.38,-2577.1 899.38,-2548.1 899.38,-2548.1 899.38,-2542.1 905.38,-2536.1 911.38,-2536.1"/>
<text text-anchor="middle" x="950.25" y="-2576.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">clearDeckDraft</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/filterCatalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1121.38,-3115.1C1121.38,-3115.1 1376.88,-3115.1 1376.88,-3115.1 1382.88,-3115.1 1388.88,-3121.1 1388.88,-3127.1 1388.88,-3127.1 1388.88,-3156.1 1388.88,-3156.1 1388.88,-3162.1 1382.88,-3168.1 1376.88,-3168.1 1376.88,-3168.1 1121.38,-3168.1 1121.38,-3168.1 1115.38,-3168.1 1109.38,-3162.1 1109.38,-3156.1 1109.38,-3156.1 1109.38,-3127.1 1109.38,-3127.1 1109.38,-3121.1 1115.38,-3115.1 1121.38,-3115.1"/>
<text text-anchor="middle" x="1249.12" y="-3155.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">filterCatalog</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/hideCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M908.38,-3146.1C908.38,-3146.1 992.12,-3146.1 992.12,-3146.1 998.12,-3146.1 1004.12,-3152.1 1004.12,-3158.1 1004.12,-3158.1 1004.12,-3187.1 1004.12,-3187.1 1004.12,-3193.1 998.12,-3199.1 992.12,-3199.1 992.12,-3199.1 908.38,-3199.1 908.38,-3199.1 902.38,-3199.1 896.38,-3193.1 896.38,-3187.1 896.38,-3187.1 896.38,-3158.1 896.38,-3158.1 896.38,-3152.1 902.38,-3146.1 908.38,-3146.1"/>
<text text-anchor="middle" x="950.25" y="-3186.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hideCardDetails</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M871.62,-3085.1C871.62,-3085.1 1028.88,-3085.1 1028.88,-3085.1 1034.88,-3085.1 1040.88,-3091.1 1040.88,-3097.1 1040.88,-3097.1 1040.88,-3126.1 1040.88,-3126.1 1040.88,-3132.1 1034.88,-3138.1 1028.88,-3138.1 1028.88,-3138.1 871.62,-3138.1 871.62,-3138.1 865.62,-3138.1 859.62,-3132.1 859.62,-3126.1 859.62,-3126.1 859.62,-3097.1 859.62,-3097.1 859.62,-3091.1 865.62,-3085.1 871.62,-3085.1"/>
<text text-anchor="middle" x="950.25" y="-3125.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">initializeDeckBuilderFromLocation</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/loadCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M904.25,-2597.1C904.25,-2597.1 1217.88,-2597.1 1217.88,-2597.1 1223.88,-2597.1 1229.88,-2603.1 1229.88,-2609.1 1229.88,-2609.1 1229.88,-2638.1 1229.88,-2638.1 1229.88,-2644.1 1223.88,-2650.1 1217.88,-2650.1 1217.88,-2650.1 904.25,-2650.1 904.25,-2650.1 898.25,-2650.1 892.25,-2644.1 892.25,-2638.1 892.25,-2638.1 892.25,-2609.1 892.25,-2609.1 892.25,-2603.1 898.25,-2597.1 904.25,-2597.1"/>
<text text-anchor="middle" x="1061.06" y="-2637.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadCatalogCards</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/loadDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M912.5,-3207.1C912.5,-3207.1 988,-3207.1 988,-3207.1 994,-3207.1 1000,-3213.1 1000,-3219.1 1000,-3219.1 1000,-3248.1 1000,-3248.1 1000,-3254.1 994,-3260.1 988,-3260.1 988,-3260.1 912.5,-3260.1 912.5,-3260.1 906.5,-3260.1 900.5,-3254.1 900.5,-3248.1 900.5,-3248.1 900.5,-3219.1 900.5,-3219.1 900.5,-3213.1 906.5,-3207.1 912.5,-3207.1"/>
<text text-anchor="middle" x="950.25" y="-3247.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckDraft</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/loadDeckIntoBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M900.5,-2963.1C900.5,-2963.1 1222,-2963.1 1222,-2963.1 1228,-2963.1 1234,-2969.1 1234,-2975.1 1234,-2975.1 1234,-3004.1 1234,-3004.1 1234,-3010.1 1228,-3016.1 1222,-3016.1 1222,-3016.1 900.5,-3016.1 900.5,-3016.1 894.5,-3016.1 888.5,-3010.1 888.5,-3004.1 888.5,-3004.1 888.5,-2975.1 888.5,-2975.1 888.5,-2969.1 894.5,-2963.1 900.5,-2963.1"/>
<text text-anchor="middle" x="1061.25" y="-3003.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckIntoBuilder</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/loadGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M903.88,-2719.1C903.88,-2719.1 1218.25,-2719.1 1218.25,-2719.1 1224.25,-2719.1 1230.25,-2725.1 1230.25,-2731.1 1230.25,-2731.1 1230.25,-2760.1 1230.25,-2760.1 1230.25,-2766.1 1224.25,-2772.1 1218.25,-2772.1 1218.25,-2772.1 903.88,-2772.1 903.88,-2772.1 897.88,-2772.1 891.88,-2766.1 891.88,-2760.1 891.88,-2760.1 891.88,-2731.1 891.88,-2731.1 891.88,-2725.1 897.88,-2719.1 903.88,-2719.1"/>
<text text-anchor="middle" x="1061.06" y="-2759.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadGameSettings</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/removeCardFromDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M895.25,-3024.1C895.25,-3024.1 1226.88,-3024.1 1226.88,-3024.1 1232.88,-3024.1 1238.88,-3030.1 1238.88,-3036.1 1238.88,-3036.1 1238.88,-3065.1 1238.88,-3065.1 1238.88,-3071.1 1232.88,-3077.1 1226.88,-3077.1 1226.88,-3077.1 895.25,-3077.1 895.25,-3077.1 889.25,-3077.1 883.25,-3071.1 883.25,-3065.1 883.25,-3065.1 883.25,-3036.1 883.25,-3036.1 883.25,-3030.1 889.25,-3024.1 895.25,-3024.1"/>
<text text-anchor="middle" x="1061.06" y="-3064.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">removeCardFromDeck</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/saveDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M911.75,-2475.1C911.75,-2475.1 988.75,-2475.1 988.75,-2475.1 994.75,-2475.1 1000.75,-2481.1 1000.75,-2487.1 1000.75,-2487.1 1000.75,-2516.1 1000.75,-2516.1 1000.75,-2522.1 994.75,-2528.1 988.75,-2528.1 988.75,-2528.1 911.75,-2528.1 911.75,-2528.1 905.75,-2528.1 899.75,-2522.1 899.75,-2516.1 899.75,-2516.1 899.75,-2487.1 899.75,-2487.1 899.75,-2481.1 905.75,-2475.1 911.75,-2475.1"/>
<text text-anchor="middle" x="950.25" y="-2515.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">saveDeckDraft</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/search</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1131.62,-3191.1C1131.62,-3191.1 1366,-3191.1 1366,-3191.1 1372,-3191.1 1378,-3197.1 1378,-3203.1 1378,-3203.1 1378,-3232.1 1378,-3232.1 1378,-3238.1 1372,-3244.1 1366,-3244.1 1366,-3244.1 1131.62,-3244.1 1131.62,-3244.1 1125.62,-3244.1 1119.62,-3238.1 1119.62,-3232.1 1119.62,-3232.1 1119.62,-3203.1 1119.62,-3203.1 1119.62,-3197.1 1125.62,-3191.1 1131.62,-3191.1"/>
<text text-anchor="middle" x="1248.81" y="-3231.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">search</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/showCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M906.5,-2841.1C906.5,-2841.1 1215.62,-2841.1 1215.62,-2841.1 1221.62,-2841.1 1227.62,-2847.1 1227.62,-2853.1 1227.62,-2853.1 1227.62,-2882.1 1227.62,-2882.1 1227.62,-2888.1 1221.62,-2894.1 1215.62,-2894.1 1215.62,-2894.1 906.5,-2894.1 906.5,-2894.1 900.5,-2894.1 894.5,-2888.1 894.5,-2882.1 894.5,-2882.1 894.5,-2853.1 894.5,-2853.1 894.5,-2847.1 900.5,-2841.1 906.5,-2841.1"/>
<text text-anchor="middle" x="1061.06" y="-2881.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">showCardDetails</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/switchDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M894.12,-2780.1C894.12,-2780.1 1228.38,-2780.1 1228.38,-2780.1 1234.38,-2780.1 1240.38,-2786.1 1240.38,-2792.1 1240.38,-2792.1 1240.38,-2821.1 1240.38,-2821.1 1240.38,-2827.1 1234.38,-2833.1 1228.38,-2833.1 1228.38,-2833.1 894.12,-2833.1 894.12,-2833.1 888.12,-2833.1 882.12,-2827.1 882.12,-2821.1 882.12,-2821.1 882.12,-2792.1 882.12,-2792.1 882.12,-2786.1 888.12,-2780.1 894.12,-2780.1"/>
<text text-anchor="middle" x="1061.25" y="-2820.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">switchDeckBuilderView</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/client/DeckBuilding/application/commands/updateAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M896,-2658.1C896,-2658.1 1226.12,-2658.1 1226.12,-2658.1 1232.12,-2658.1 1238.12,-2664.1 1238.12,-2670.1 1238.12,-2670.1 1238.12,-2699.1 1238.12,-2699.1 1238.12,-2705.1 1232.12,-2711.1 1226.12,-2711.1 1226.12,-2711.1 896,-2711.1 896,-2711.1 890,-2711.1 884,-2705.1 884,-2699.1 884,-2699.1 884,-2670.1 884,-2670.1 884,-2664.1 890,-2658.1 896,-2658.1"/>
<text text-anchor="middle" x="1061.06" y="-2698.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">updateAvailableFilters</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1089,-3295.1C1089,-3295.1 1379.25,-3295.1 1379.25,-3295.1 1385.25,-3295.1 1391.25,-3301.1 1391.25,-3307.1 1391.25,-3307.1 1391.25,-4469.1 1391.25,-4469.1 1391.25,-4475.1 1385.25,-4481.1 1379.25,-4481.1 1379.25,-4481.1 1089,-4481.1 1089,-4481.1 1083,-4481.1 1077,-4475.1 1077,-4469.1 1077,-4469.1 1077,-3307.1 1077,-3307.1 1077,-3301.1 1083,-3295.1 1089,-3295.1"/>
<text text-anchor="middle" x="1234.12" y="-4468.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/applyFilterToCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1281.5,-4401.1C1281.5,-4401.1 1371.25,-4401.1 1371.25,-4401.1 1377.25,-4401.1 1383.25,-4407.1 1383.25,-4413.1 1383.25,-4413.1 1383.25,-4442.1 1383.25,-4442.1 1383.25,-4448.1 1377.25,-4454.1 1371.25,-4454.1 1371.25,-4454.1 1281.5,-4454.1 1281.5,-4454.1 1275.5,-4454.1 1269.5,-4448.1 1269.5,-4442.1 1269.5,-4442.1 1269.5,-4413.1 1269.5,-4413.1 1269.5,-4407.1 1275.5,-4401.1 1281.5,-4401.1"/>
<text text-anchor="middle" x="1326.38" y="-4441.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">applyFilterToCard</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/findDeckCardById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1109,-3303.1C1109,-3303.1 1200.25,-3303.1 1200.25,-3303.1 1206.25,-3303.1 1212.25,-3309.1 1212.25,-3315.1 1212.25,-3315.1 1212.25,-3344.1 1212.25,-3344.1 1212.25,-3350.1 1206.25,-3356.1 1200.25,-3356.1 1200.25,-3356.1 1109,-3356.1 1109,-3356.1 1103,-3356.1 1097,-3350.1 1097,-3344.1 1097,-3344.1 1097,-3315.1 1097,-3315.1 1097,-3309.1 1103,-3303.1 1109,-3303.1"/>
<text text-anchor="middle" x="1154.62" y="-3343.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">findDeckCardById</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getActiveFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1115,-3425.1C1115,-3425.1 1194.25,-3425.1 1194.25,-3425.1 1200.25,-3425.1 1206.25,-3431.1 1206.25,-3437.1 1206.25,-3437.1 1206.25,-3466.1 1206.25,-3466.1 1206.25,-3472.1 1200.25,-3478.1 1194.25,-3478.1 1194.25,-3478.1 1115,-3478.1 1115,-3478.1 1109,-3478.1 1103,-3472.1 1103,-3466.1 1103,-3466.1 1103,-3437.1 1103,-3437.1 1103,-3431.1 1109,-3425.1 1115,-3425.1"/>
<text text-anchor="middle" x="1154.62" y="-3465.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getActiveFilters</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1108.25,-3608.1C1108.25,-3608.1 1201,-3608.1 1201,-3608.1 1207,-3608.1 1213,-3614.1 1213,-3620.1 1213,-3620.1 1213,-3649.1 1213,-3649.1 1213,-3655.1 1207,-3661.1 1201,-3661.1 1201,-3661.1 1108.25,-3661.1 1108.25,-3661.1 1102.25,-3661.1 1096.25,-3655.1 1096.25,-3649.1 1096.25,-3649.1 1096.25,-3620.1 1096.25,-3620.1 1096.25,-3614.1 1102.25,-3608.1 1108.25,-3608.1"/>
<text text-anchor="middle" x="1154.62" y="-3648.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getAvailableFilters</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1115.38,-3486.1C1115.38,-3486.1 1193.88,-3486.1 1193.88,-3486.1 1199.88,-3486.1 1205.88,-3492.1 1205.88,-3498.1 1205.88,-3498.1 1205.88,-3527.1 1205.88,-3527.1 1205.88,-3533.1 1199.88,-3539.1 1193.88,-3539.1 1193.88,-3539.1 1115.38,-3539.1 1115.38,-3539.1 1109.38,-3539.1 1103.38,-3533.1 1103.38,-3527.1 1103.38,-3527.1 1103.38,-3498.1 1103.38,-3498.1 1103.38,-3492.1 1109.38,-3486.1 1115.38,-3486.1"/>
<text text-anchor="middle" x="1154.62" y="-3526.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCardDetails</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1113.12,-3547.1C1113.12,-3547.1 1196.12,-3547.1 1196.12,-3547.1 1202.12,-3547.1 1208.12,-3553.1 1208.12,-3559.1 1208.12,-3559.1 1208.12,-3588.1 1208.12,-3588.1 1208.12,-3594.1 1202.12,-3600.1 1196.12,-3600.1 1196.12,-3600.1 1113.12,-3600.1 1113.12,-3600.1 1107.12,-3600.1 1101.12,-3594.1 1101.12,-3588.1 1101.12,-3588.1 1101.12,-3559.1 1101.12,-3559.1 1101.12,-3553.1 1107.12,-3547.1 1113.12,-3547.1"/>
<text text-anchor="middle" x="1154.62" y="-3587.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCardsInDeck</text>
</g>
<g id="clust53" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getCatalogCardById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1104.5,-3364.1C1104.5,-3364.1 1204.75,-3364.1 1204.75,-3364.1 1210.75,-3364.1 1216.75,-3370.1 1216.75,-3376.1 1216.75,-3376.1 1216.75,-3405.1 1216.75,-3405.1 1216.75,-3411.1 1210.75,-3417.1 1204.75,-3417.1 1204.75,-3417.1 1104.5,-3417.1 1104.5,-3417.1 1098.5,-3417.1 1092.5,-3411.1 1092.5,-3405.1 1092.5,-3405.1 1092.5,-3376.1 1092.5,-3376.1 1092.5,-3370.1 1098.5,-3364.1 1104.5,-3364.1"/>
<text text-anchor="middle" x="1154.62" y="-3404.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogCardById</text>
</g>
<g id="clust54" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1111.25,-3669.1C1111.25,-3669.1 1198,-3669.1 1198,-3669.1 1204,-3669.1 1210,-3675.1 1210,-3681.1 1210,-3681.1 1210,-3710.1 1210,-3710.1 1210,-3716.1 1204,-3722.1 1198,-3722.1 1198,-3722.1 1111.25,-3722.1 1111.25,-3722.1 1105.25,-3722.1 1099.25,-3716.1 1099.25,-3710.1 1099.25,-3710.1 1099.25,-3681.1 1099.25,-3681.1 1099.25,-3675.1 1105.25,-3669.1 1111.25,-3669.1"/>
<text text-anchor="middle" x="1154.62" y="-3709.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogCards</text>
</g>
<g id="clust55" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getCatalogError</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1113.88,-3730.1C1113.88,-3730.1 1195.38,-3730.1 1195.38,-3730.1 1201.38,-3730.1 1207.38,-3736.1 1207.38,-3742.1 1207.38,-3742.1 1207.38,-3771.1 1207.38,-3771.1 1207.38,-3777.1 1201.38,-3783.1 1195.38,-3783.1 1195.38,-3783.1 1113.88,-3783.1 1113.88,-3783.1 1107.88,-3783.1 1101.88,-3777.1 1101.88,-3771.1 1101.88,-3771.1 1101.88,-3742.1 1101.88,-3742.1 1101.88,-3736.1 1107.88,-3730.1 1113.88,-3730.1"/>
<text text-anchor="middle" x="1154.62" y="-3770.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogError</text>
</g>
<g id="clust56" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1104.88,-3791.1C1104.88,-3791.1 1204.38,-3791.1 1204.38,-3791.1 1210.38,-3791.1 1216.38,-3797.1 1216.38,-3803.1 1216.38,-3803.1 1216.38,-3832.1 1216.38,-3832.1 1216.38,-3838.1 1210.38,-3844.1 1204.38,-3844.1 1204.38,-3844.1 1104.88,-3844.1 1104.88,-3844.1 1098.88,-3844.1 1092.88,-3838.1 1092.88,-3832.1 1092.88,-3832.1 1092.88,-3803.1 1092.88,-3803.1 1092.88,-3797.1 1098.88,-3791.1 1104.88,-3791.1"/>
<text text-anchor="middle" x="1154.62" y="-3831.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getDeckBuilderView</text>
</g>
<g id="clust57" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getDeckName</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1116.88,-3852.1C1116.88,-3852.1 1192.38,-3852.1 1192.38,-3852.1 1198.38,-3852.1 1204.38,-3858.1 1204.38,-3864.1 1204.38,-3864.1 1204.38,-3893.1 1204.38,-3893.1 1204.38,-3899.1 1198.38,-3905.1 1192.38,-3905.1 1192.38,-3905.1 1116.88,-3905.1 1116.88,-3905.1 1110.88,-3905.1 1104.88,-3899.1 1104.88,-3893.1 1104.88,-3893.1 1104.88,-3864.1 1104.88,-3864.1 1104.88,-3858.1 1110.88,-3852.1 1116.88,-3852.1"/>
<text text-anchor="middle" x="1154.62" y="-3892.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getDeckName</text>
</g>
<g id="clust58" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getFilteredCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1112,-3913.1C1112,-3913.1 1197.25,-3913.1 1197.25,-3913.1 1203.25,-3913.1 1209.25,-3919.1 1209.25,-3925.1 1209.25,-3925.1 1209.25,-3954.1 1209.25,-3954.1 1209.25,-3960.1 1203.25,-3966.1 1197.25,-3966.1 1197.25,-3966.1 1112,-3966.1 1112,-3966.1 1106,-3966.1 1100,-3960.1 1100,-3954.1 1100,-3954.1 1100,-3925.1 1100,-3925.1 1100,-3919.1 1106,-3913.1 1112,-3913.1"/>
<text text-anchor="middle" x="1154.62" y="-3953.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getFilteredCards</text>
</g>
<g id="clust59" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1110.88,-3974.1C1110.88,-3974.1 1198.38,-3974.1 1198.38,-3974.1 1204.38,-3974.1 1210.38,-3980.1 1210.38,-3986.1 1210.38,-3986.1 1210.38,-4015.1 1210.38,-4015.1 1210.38,-4021.1 1204.38,-4027.1 1198.38,-4027.1 1198.38,-4027.1 1110.88,-4027.1 1110.88,-4027.1 1104.88,-4027.1 1098.88,-4021.1 1098.88,-4015.1 1098.88,-4015.1 1098.88,-3986.1 1098.88,-3986.1 1098.88,-3980.1 1104.88,-3974.1 1110.88,-3974.1"/>
<text text-anchor="middle" x="1154.62" y="-4014.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getGameSettings</text>
</g>
<g id="clust60" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getGameSettingsError</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1101.12,-4035.1C1101.12,-4035.1 1208.12,-4035.1 1208.12,-4035.1 1214.12,-4035.1 1220.12,-4041.1 1220.12,-4047.1 1220.12,-4047.1 1220.12,-4076.1 1220.12,-4076.1 1220.12,-4082.1 1214.12,-4088.1 1208.12,-4088.1 1208.12,-4088.1 1101.12,-4088.1 1101.12,-4088.1 1095.12,-4088.1 1089.12,-4082.1 1089.12,-4076.1 1089.12,-4076.1 1089.12,-4047.1 1089.12,-4047.1 1089.12,-4041.1 1095.12,-4035.1 1101.12,-4035.1"/>
<text text-anchor="middle" x="1154.62" y="-4075.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getGameSettingsError</text>
</g>
<g id="clust61" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getMaxCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1104.5,-4096.1C1104.5,-4096.1 1204.75,-4096.1 1204.75,-4096.1 1210.75,-4096.1 1216.75,-4102.1 1216.75,-4108.1 1216.75,-4108.1 1216.75,-4137.1 1216.75,-4137.1 1216.75,-4143.1 1210.75,-4149.1 1204.75,-4149.1 1204.75,-4149.1 1104.5,-4149.1 1104.5,-4149.1 1098.5,-4149.1 1092.5,-4143.1 1092.5,-4137.1 1092.5,-4137.1 1092.5,-4108.1 1092.5,-4108.1 1092.5,-4102.1 1098.5,-4096.1 1104.5,-4096.1"/>
<text text-anchor="middle" x="1154.62" y="-4136.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getMaxCardsInDeck</text>
</g>
<g id="clust62" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getSearchTerm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1115,-4157.1C1115,-4157.1 1194.25,-4157.1 1194.25,-4157.1 1200.25,-4157.1 1206.25,-4163.1 1206.25,-4169.1 1206.25,-4169.1 1206.25,-4198.1 1206.25,-4198.1 1206.25,-4204.1 1200.25,-4210.1 1194.25,-4210.1 1194.25,-4210.1 1115,-4210.1 1115,-4210.1 1109,-4210.1 1103,-4204.1 1103,-4198.1 1103,-4198.1 1103,-4169.1 1103,-4169.1 1103,-4163.1 1109,-4157.1 1115,-4157.1"/>
<text text-anchor="middle" x="1154.62" y="-4197.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getSearchTerm</text>
</g>
<g id="clust63" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/getTotalCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1103.38,-4218.1C1103.38,-4218.1 1205.88,-4218.1 1205.88,-4218.1 1211.88,-4218.1 1217.88,-4224.1 1217.88,-4230.1 1217.88,-4230.1 1217.88,-4259.1 1217.88,-4259.1 1217.88,-4265.1 1211.88,-4271.1 1205.88,-4271.1 1205.88,-4271.1 1103.38,-4271.1 1103.38,-4271.1 1097.38,-4271.1 1091.38,-4265.1 1091.38,-4259.1 1091.38,-4259.1 1091.38,-4230.1 1091.38,-4230.1 1091.38,-4224.1 1097.38,-4218.1 1103.38,-4218.1"/>
<text text-anchor="middle" x="1154.62" y="-4258.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getTotalCardsInDeck</text>
</g>
<g id="clust64" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/hasDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1118.38,-4401.1C1118.38,-4401.1 1190.88,-4401.1 1190.88,-4401.1 1196.88,-4401.1 1202.88,-4407.1 1202.88,-4413.1 1202.88,-4413.1 1202.88,-4442.1 1202.88,-4442.1 1202.88,-4448.1 1196.88,-4454.1 1190.88,-4454.1 1190.88,-4454.1 1118.38,-4454.1 1118.38,-4454.1 1112.38,-4454.1 1106.38,-4448.1 1106.38,-4442.1 1106.38,-4442.1 1106.38,-4413.1 1106.38,-4413.1 1106.38,-4407.1 1112.38,-4401.1 1118.38,-4401.1"/>
<text text-anchor="middle" x="1154.62" y="-4441.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hasDeckDraft</text>
</g>
<g id="clust65" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/isCatalogLoading</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1109.75,-4340.1C1109.75,-4340.1 1199.5,-4340.1 1199.5,-4340.1 1205.5,-4340.1 1211.5,-4346.1 1211.5,-4352.1 1211.5,-4352.1 1211.5,-4381.1 1211.5,-4381.1 1211.5,-4387.1 1205.5,-4393.1 1199.5,-4393.1 1199.5,-4393.1 1109.75,-4393.1 1109.75,-4393.1 1103.75,-4393.1 1097.75,-4387.1 1097.75,-4381.1 1097.75,-4381.1 1097.75,-4352.1 1097.75,-4352.1 1097.75,-4346.1 1103.75,-4340.1 1109.75,-4340.1"/>
<text text-anchor="middle" x="1154.62" y="-4380.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">isCatalogLoading</text>
</g>
<g id="clust66" class="cluster">
<title>cluster_src/client/DeckBuilding/application/queries/isGameSettingsLoading</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1097,-4279.1C1097,-4279.1 1212.25,-4279.1 1212.25,-4279.1 1218.25,-4279.1 1224.25,-4285.1 1224.25,-4291.1 1224.25,-4291.1 1224.25,-4320.1 1224.25,-4320.1 1224.25,-4326.1 1218.25,-4332.1 1212.25,-4332.1 1212.25,-4332.1 1097,-4332.1 1097,-4332.1 1091,-4332.1 1085,-4326.1 1085,-4320.1 1085,-4320.1 1085,-4291.1 1085,-4291.1 1085,-4285.1 1091,-4279.1 1097,-4279.1"/>
<text text-anchor="middle" x="1154.62" y="-4319.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">isGameSettingsLoading</text>
</g>
<g id="clust67" class="cluster">
<title>cluster_src/client/DeckBuilding/application/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1274.25,-4489.1C1274.25,-4489.1 1378.5,-4489.1 1378.5,-4489.1 1384.5,-4489.1 1390.5,-4495.1 1390.5,-4501.1 1390.5,-4501.1 1390.5,-4565.1 1390.5,-4565.1 1390.5,-4571.1 1384.5,-4577.1 1378.5,-4577.1 1378.5,-4577.1 1274.25,-4577.1 1274.25,-4577.1 1268.25,-4577.1 1262.25,-4571.1 1262.25,-4565.1 1262.25,-4565.1 1262.25,-4501.1 1262.25,-4501.1 1262.25,-4495.1 1268.25,-4489.1 1274.25,-4489.1"/>
<text text-anchor="middle" x="1326.38" y="-4564.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust68" class="cluster">
<title>cluster_src/client/DeckBuilding/application/services/DeckDraftService</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1282.25,-4497.1C1282.25,-4497.1 1370.5,-4497.1 1370.5,-4497.1 1376.5,-4497.1 1382.5,-4503.1 1382.5,-4509.1 1382.5,-4509.1 1382.5,-4538.1 1382.5,-4538.1 1382.5,-4544.1 1376.5,-4550.1 1370.5,-4550.1 1370.5,-4550.1 1282.25,-4550.1 1282.25,-4550.1 1276.25,-4550.1 1270.25,-4544.1 1270.25,-4538.1 1270.25,-4538.1 1270.25,-4509.1 1270.25,-4509.1 1270.25,-4503.1 1276.25,-4497.1 1282.25,-4497.1"/>
<text text-anchor="middle" x="1326.38" y="-4537.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftService</text>
</g>
<g id="clust69" class="cluster">
<title>cluster_src/client/DeckBuilding/application/subscribers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M661.38,-3499.1C661.38,-3499.1 768.38,-3499.1 768.38,-3499.1 774.38,-3499.1 780.38,-3505.1 780.38,-3511.1 780.38,-3511.1 780.38,-3540.1 780.38,-3540.1 780.38,-3546.1 774.38,-3552.1 768.38,-3552.1 768.38,-3552.1 661.38,-3552.1 661.38,-3552.1 655.38,-3552.1 649.38,-3546.1 649.38,-3540.1 649.38,-3540.1 649.38,-3511.1 649.38,-3511.1 649.38,-3505.1 655.38,-3499.1 661.38,-3499.1"/>
<text text-anchor="middle" x="714.88" y="-3539.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">subscribers</text>
</g>
<g id="clust70" class="cluster">
<title>cluster_src/client/DeckBuilding/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1092.75,-4612.1C1092.75,-4612.1 1749.25,-4612.1 1749.25,-4612.1 1755.25,-4612.1 1761.25,-4618.1 1761.25,-4624.1 1761.25,-4624.1 1761.25,-4958.1 1761.25,-4958.1 1761.25,-4964.1 1755.25,-4970.1 1749.25,-4970.1 1749.25,-4970.1 1092.75,-4970.1 1092.75,-4970.1 1086.75,-4970.1 1080.75,-4964.1 1080.75,-4958.1 1080.75,-4958.1 1080.75,-4624.1 1080.75,-4624.1 1080.75,-4618.1 1086.75,-4612.1 1092.75,-4612.1"/>
<text text-anchor="middle" x="1421" y="-4957.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust71" class="cluster">
<title>cluster_src/client/DeckBuilding/domain/CardData</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1682.25,-4890.1C1682.25,-4890.1 1741.25,-4890.1 1741.25,-4890.1 1747.25,-4890.1 1753.25,-4896.1 1753.25,-4902.1 1753.25,-4902.1 1753.25,-4931.1 1753.25,-4931.1 1753.25,-4937.1 1747.25,-4943.1 1741.25,-4943.1 1741.25,-4943.1 1682.25,-4943.1 1682.25,-4943.1 1676.25,-4943.1 1670.25,-4937.1 1670.25,-4931.1 1670.25,-4931.1 1670.25,-4902.1 1670.25,-4902.1 1670.25,-4896.1 1676.25,-4890.1 1682.25,-4890.1"/>
<text text-anchor="middle" x="1711.75" y="-4930.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CardData</text>
</g>
<g id="clust72" class="cluster">
<title>cluster_src/client/DeckBuilding/domain/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1271,-4620.1C1271,-4620.1 1734.75,-4620.1 1734.75,-4620.1 1740.75,-4620.1 1746.75,-4626.1 1746.75,-4632.1 1746.75,-4632.1 1746.75,-4764.1 1746.75,-4764.1 1746.75,-4770.1 1740.75,-4776.1 1734.75,-4776.1 1734.75,-4776.1 1271,-4776.1 1271,-4776.1 1265,-4776.1 1259,-4770.1 1259,-4764.1 1259,-4764.1 1259,-4632.1 1259,-4632.1 1259,-4626.1 1265,-4620.1 1271,-4620.1"/>
<text text-anchor="middle" x="1502.88" y="-4763.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust73" class="cluster">
<title>cluster_src/client/DeckBuilding/domain/DeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1121.38,-4859.1C1121.38,-4859.1 1650.25,-4859.1 1650.25,-4859.1 1656.25,-4859.1 1662.25,-4865.1 1662.25,-4871.1 1662.25,-4871.1 1662.25,-4931.1 1662.25,-4931.1 1662.25,-4937.1 1656.25,-4943.1 1650.25,-4943.1 1650.25,-4943.1 1121.38,-4943.1 1121.38,-4943.1 1115.38,-4943.1 1109.38,-4937.1 1109.38,-4931.1 1109.38,-4931.1 1109.38,-4871.1 1109.38,-4871.1 1109.38,-4865.1 1115.38,-4859.1 1121.38,-4859.1"/>
<text text-anchor="middle" x="1385.81" y="-4930.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilder</text>
</g>
<g id="clust74" class="cluster">
<title>cluster_src/client/DeckBuilding/domain/GameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1100.75,-4784.1C1100.75,-4784.1 1514.88,-4784.1 1514.88,-4784.1 1520.88,-4784.1 1526.88,-4790.1 1526.88,-4796.1 1526.88,-4796.1 1526.88,-4839.1 1526.88,-4839.1 1526.88,-4845.1 1520.88,-4851.1 1514.88,-4851.1 1514.88,-4851.1 1100.75,-4851.1 1100.75,-4851.1 1094.75,-4851.1 1088.75,-4845.1 1088.75,-4839.1 1088.75,-4839.1 1088.75,-4796.1 1088.75,-4796.1 1088.75,-4790.1 1094.75,-4784.1 1100.75,-4784.1"/>
<text text-anchor="middle" x="1307.81" y="-4838.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameSettings</text>
</g>
<g id="clust75" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M216.25,-4612.1C216.25,-4612.1 817,-4612.1 817,-4612.1 823,-4612.1 829,-4618.1 829,-4624.1 829,-4624.1 829,-5433.1 829,-5433.1 829,-5439.1 823,-5445.1 817,-5445.1 817,-5445.1 216.25,-5445.1 216.25,-5445.1 210.25,-5445.1 204.25,-5439.1 204.25,-5433.1 204.25,-5433.1 204.25,-4624.1 204.25,-4624.1 204.25,-4618.1 210.25,-4612.1 216.25,-4612.1"/>
<text text-anchor="middle" x="516.62" y="-5432.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust76" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M224.25,-4676.1C224.25,-4676.1 587.38,-4676.1 587.38,-4676.1 593.38,-4676.1 599.38,-4682.1 599.38,-4688.1 599.38,-4688.1 599.38,-5310.1 599.38,-5310.1 599.38,-5316.1 593.38,-5322.1 587.38,-5322.1 587.38,-5322.1 224.25,-5322.1 224.25,-5322.1 218.25,-5322.1 212.25,-5316.1 212.25,-5310.1 212.25,-5310.1 212.25,-4688.1 212.25,-4688.1 212.25,-4682.1 218.25,-4676.1 224.25,-4676.1"/>
<text text-anchor="middle" x="405.81" y="-5309.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust77" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M232.25,-4684.1C232.25,-4684.1 579.38,-4684.1 579.38,-4684.1 585.38,-4684.1 591.38,-4690.1 591.38,-4696.1 591.38,-4696.1 591.38,-5283.1 591.38,-5283.1 591.38,-5289.1 585.38,-5295.1 579.38,-5295.1 579.38,-5295.1 232.25,-5295.1 232.25,-5295.1 226.25,-5295.1 220.25,-5289.1 220.25,-5283.1 220.25,-5283.1 220.25,-4696.1 220.25,-4696.1 220.25,-4690.1 226.25,-4684.1 232.25,-4684.1"/>
<text text-anchor="middle" x="405.81" y="-5282.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust78" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M240.25,-4692.1C240.25,-4692.1 571.38,-4692.1 571.38,-4692.1 577.38,-4692.1 583.38,-4698.1 583.38,-4704.1 583.38,-4704.1 583.38,-5256.1 583.38,-5256.1 583.38,-5262.1 577.38,-5268.1 571.38,-5268.1 571.38,-5268.1 240.25,-5268.1 240.25,-5268.1 234.25,-5268.1 228.25,-5262.1 228.25,-5256.1 228.25,-5256.1 228.25,-4704.1 228.25,-4704.1 228.25,-4698.1 234.25,-4692.1 240.25,-4692.1"/>
<text text-anchor="middle" x="405.81" y="-5255.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilding</text>
</g>
<g id="clust79" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M257.62,-5005.1C257.62,-5005.1 371.38,-5005.1 371.38,-5005.1 377.38,-5005.1 383.38,-5011.1 383.38,-5017.1 383.38,-5017.1 383.38,-5046.1 383.38,-5046.1 383.38,-5052.1 377.38,-5058.1 371.38,-5058.1 371.38,-5058.1 257.62,-5058.1 257.62,-5058.1 251.62,-5058.1 245.62,-5052.1 245.62,-5046.1 245.62,-5046.1 245.62,-5017.1 245.62,-5017.1 245.62,-5011.1 251.62,-5005.1 257.62,-5005.1"/>
<text text-anchor="middle" x="314.5" y="-5045.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderCardsGrid</text>
</g>
<g id="clust80" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M262.88,-4730.1C262.88,-4730.1 366.12,-4730.1 366.12,-4730.1 372.12,-4730.1 378.12,-4736.1 378.12,-4742.1 378.12,-4742.1 378.12,-4771.1 378.12,-4771.1 378.12,-4777.1 372.12,-4783.1 366.12,-4783.1 366.12,-4783.1 262.88,-4783.1 262.88,-4783.1 256.88,-4783.1 250.88,-4777.1 250.88,-4771.1 250.88,-4771.1 250.88,-4742.1 250.88,-4742.1 250.88,-4736.1 256.88,-4730.1 262.88,-4730.1"/>
<text text-anchor="middle" x="314.5" y="-4770.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderInitializer</text>
</g>
<g id="clust81" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M266.62,-4822.1C266.62,-4822.1 540.5,-4822.1 540.5,-4822.1 546.5,-4822.1 552.5,-4828.1 552.5,-4834.1 552.5,-4834.1 552.5,-4863.1 552.5,-4863.1 552.5,-4869.1 546.5,-4875.1 540.5,-4875.1 540.5,-4875.1 266.62,-4875.1 266.62,-4875.1 260.62,-4875.1 254.62,-4869.1 254.62,-4863.1 254.62,-4863.1 254.62,-4834.1 254.62,-4834.1 254.62,-4828.1 260.62,-4822.1 266.62,-4822.1"/>
<text text-anchor="middle" x="403.56" y="-4862.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderPanel</text>
</g>
<g id="clust82" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M451.88,-5005.1C451.88,-5005.1 548.38,-5005.1 548.38,-5005.1 554.38,-5005.1 560.38,-5011.1 560.38,-5017.1 560.38,-5017.1 560.38,-5046.1 560.38,-5046.1 560.38,-5052.1 554.38,-5058.1 548.38,-5058.1 548.38,-5058.1 451.88,-5058.1 451.88,-5058.1 445.88,-5058.1 439.88,-5052.1 439.88,-5046.1 439.88,-5046.1 439.88,-5017.1 439.88,-5017.1 439.88,-5011.1 445.88,-5005.1 451.88,-5005.1"/>
<text text-anchor="middle" x="500.12" y="-5045.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingCard</text>
</g>
<g id="clust83" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M264,-4944.1C264,-4944.1 529.62,-4944.1 529.62,-4944.1 535.62,-4944.1 541.62,-4950.1 541.62,-4956.1 541.62,-4956.1 541.62,-4985.1 541.62,-4985.1 541.62,-4991.1 535.62,-4997.1 529.62,-4997.1 529.62,-4997.1 264,-4997.1 264,-4997.1 258,-4997.1 252,-4991.1 252,-4985.1 252,-4985.1 252,-4956.1 252,-4956.1 252,-4950.1 258,-4944.1 264,-4944.1"/>
<text text-anchor="middle" x="396.81" y="-4984.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingFilters</text>
</g>
<g id="clust84" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M248.25,-5188.1C248.25,-5188.1 380.75,-5188.1 380.75,-5188.1 386.75,-5188.1 392.75,-5194.1 392.75,-5200.1 392.75,-5200.1 392.75,-5229.1 392.75,-5229.1 392.75,-5235.1 386.75,-5241.1 380.75,-5241.1 380.75,-5241.1 248.25,-5241.1 248.25,-5241.1 242.25,-5241.1 236.25,-5235.1 236.25,-5229.1 236.25,-5229.1 236.25,-5200.1 236.25,-5200.1 236.25,-5194.1 242.25,-5188.1 248.25,-5188.1"/>
<text text-anchor="middle" x="314.5" y="-5228.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingSkeletonCard</text>
</g>
<g id="clust85" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M441,-4700.1C441,-4700.1 559.25,-4700.1 559.25,-4700.1 565.25,-4700.1 571.25,-4706.1 571.25,-4712.1 571.25,-4712.1 571.25,-4741.1 571.25,-4741.1 571.25,-4747.1 565.25,-4753.1 559.25,-4753.1 559.25,-4753.1 441,-4753.1 441,-4753.1 435,-4753.1 429,-4747.1 429,-4741.1 429,-4741.1 429,-4712.1 429,-4712.1 429,-4706.1 435,-4700.1 441,-4700.1"/>
<text text-anchor="middle" x="500.12" y="-4740.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckCardDetailsDialog</text>
</g>
<g id="clust86" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M265.5,-5066.1C265.5,-5066.1 363.5,-5066.1 363.5,-5066.1 369.5,-5066.1 375.5,-5072.1 375.5,-5078.1 375.5,-5078.1 375.5,-5107.1 375.5,-5107.1 375.5,-5113.1 369.5,-5119.1 363.5,-5119.1 363.5,-5119.1 265.5,-5119.1 265.5,-5119.1 259.5,-5119.1 253.5,-5113.1 253.5,-5107.1 253.5,-5107.1 253.5,-5078.1 253.5,-5078.1 253.5,-5072.1 259.5,-5066.1 265.5,-5066.1"/>
<text text-anchor="middle" x="314.5" y="-5106.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftInitializer</text>
</g>
<g id="clust87" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M267.38,-5127.1C267.38,-5127.1 361.62,-5127.1 361.62,-5127.1 367.62,-5127.1 373.62,-5133.1 373.62,-5139.1 373.62,-5139.1 373.62,-5168.1 373.62,-5168.1 373.62,-5174.1 367.62,-5180.1 361.62,-5180.1 361.62,-5180.1 267.38,-5180.1 267.38,-5180.1 261.38,-5180.1 255.38,-5174.1 255.38,-5168.1 255.38,-5168.1 255.38,-5139.1 255.38,-5139.1 255.38,-5133.1 261.38,-5127.1 267.38,-5127.1"/>
<text text-anchor="middle" x="314.5" y="-5167.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">EditDeckInitializer</text>
</g>
<g id="clust88" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/SaveDeckDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M454.88,-4761.1C454.88,-4761.1 545.38,-4761.1 545.38,-4761.1 551.38,-4761.1 557.38,-4767.1 557.38,-4773.1 557.38,-4773.1 557.38,-4802.1 557.38,-4802.1 557.38,-4808.1 551.38,-4814.1 545.38,-4814.1 545.38,-4814.1 454.88,-4814.1 454.88,-4814.1 448.88,-4814.1 442.88,-4808.1 442.88,-4802.1 442.88,-4802.1 442.88,-4773.1 442.88,-4773.1 442.88,-4767.1 448.88,-4761.1 454.88,-4761.1"/>
<text text-anchor="middle" x="500.12" y="-4801.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeckDialog</text>
</g>
<g id="clust89" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M436.88,-4883.1C436.88,-4883.1 563.38,-4883.1 563.38,-4883.1 569.38,-4883.1 575.38,-4889.1 575.38,-4895.1 575.38,-4895.1 575.38,-4924.1 575.38,-4924.1 575.38,-4930.1 569.38,-4936.1 563.38,-4936.1 563.38,-4936.1 436.88,-4936.1 436.88,-4936.1 430.88,-4936.1 424.88,-4930.1 424.88,-4924.1 424.88,-4924.1 424.88,-4895.1 424.88,-4895.1 424.88,-4889.1 430.88,-4883.1 436.88,-4883.1"/>
<text text-anchor="middle" x="500.12" y="-4923.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">TotalCardsInDeckCounter</text>
</g>
<g id="clust90" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M620.75,-4620.1C620.75,-4620.1 809,-4620.1 809,-4620.1 815,-4620.1 821,-4626.1 821,-4632.1 821,-4632.1 821,-5001.1 821,-5001.1 821,-5007.1 815,-5013.1 809,-5013.1 809,-5013.1 620.75,-5013.1 620.75,-5013.1 614.75,-5013.1 608.75,-5007.1 608.75,-5001.1 608.75,-5001.1 608.75,-4632.1 608.75,-4632.1 608.75,-4626.1 614.75,-4620.1 620.75,-4620.1"/>
<text text-anchor="middle" x="714.88" y="-5000.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust91" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M659.38,-4628.1C659.38,-4628.1 771.38,-4628.1 771.38,-4628.1 777.38,-4628.1 783.38,-4634.1 783.38,-4640.1 783.38,-4640.1 783.38,-4669.1 783.38,-4669.1 783.38,-4675.1 777.38,-4681.1 771.38,-4681.1 771.38,-4681.1 659.38,-4681.1 659.38,-4681.1 653.38,-4681.1 647.38,-4675.1 647.38,-4669.1 647.38,-4669.1 647.38,-4640.1 647.38,-4640.1 647.38,-4634.1 653.38,-4628.1 659.38,-4628.1"/>
<text text-anchor="middle" x="715.38" y="-4668.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useCatalogCardsByGameId</text>
</g>
<g id="clust92" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M674.12,-4689.1C674.12,-4689.1 755.62,-4689.1 755.62,-4689.1 761.62,-4689.1 767.62,-4695.1 767.62,-4701.1 767.62,-4701.1 767.62,-4730.1 767.62,-4730.1 767.62,-4736.1 761.62,-4742.1 755.62,-4742.1 755.62,-4742.1 674.12,-4742.1 674.12,-4742.1 668.12,-4742.1 662.12,-4736.1 662.12,-4730.1 662.12,-4730.1 662.12,-4701.1 662.12,-4701.1 662.12,-4695.1 668.12,-4689.1 674.12,-4689.1"/>
<text text-anchor="middle" x="714.88" y="-4729.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckBuilder</text>
</g>
<g id="clust93" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks/useDeckById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M677.12,-4750.1C677.12,-4750.1 752.62,-4750.1 752.62,-4750.1 758.62,-4750.1 764.62,-4756.1 764.62,-4762.1 764.62,-4762.1 764.62,-4791.1 764.62,-4791.1 764.62,-4797.1 758.62,-4803.1 752.62,-4803.1 752.62,-4803.1 677.12,-4803.1 677.12,-4803.1 671.12,-4803.1 665.12,-4797.1 665.12,-4791.1 665.12,-4791.1 665.12,-4762.1 665.12,-4762.1 665.12,-4756.1 671.12,-4750.1 677.12,-4750.1"/>
<text text-anchor="middle" x="714.88" y="-4790.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckById</text>
</g>
<g id="clust94" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks/useDeckId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M684.62,-4811.1C684.62,-4811.1 745.12,-4811.1 745.12,-4811.1 751.12,-4811.1 757.12,-4817.1 757.12,-4823.1 757.12,-4823.1 757.12,-4852.1 757.12,-4852.1 757.12,-4858.1 751.12,-4864.1 745.12,-4864.1 745.12,-4864.1 684.62,-4864.1 684.62,-4864.1 678.62,-4864.1 672.62,-4858.1 672.62,-4852.1 672.62,-4852.1 672.62,-4823.1 672.62,-4823.1 672.62,-4817.1 678.62,-4811.1 684.62,-4811.1"/>
<text text-anchor="middle" x="714.88" y="-4851.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckId</text>
</g>
<g id="clust95" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M646.38,-4933.1C646.38,-4933.1 783.38,-4933.1 783.38,-4933.1 789.38,-4933.1 795.38,-4939.1 795.38,-4945.1 795.38,-4945.1 795.38,-4974.1 795.38,-4974.1 795.38,-4980.1 789.38,-4986.1 783.38,-4986.1 783.38,-4986.1 646.38,-4986.1 646.38,-4986.1 640.38,-4986.1 634.38,-4980.1 634.38,-4974.1 634.38,-4974.1 634.38,-4945.1 634.38,-4945.1 634.38,-4939.1 640.38,-4933.1 646.38,-4933.1"/>
<text text-anchor="middle" x="714.88" y="-4973.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameSettingsByGameId</text>
</g>
<g id="clust96" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M628.75,-4872.1C628.75,-4872.1 801,-4872.1 801,-4872.1 807,-4872.1 813,-4878.1 813,-4884.1 813,-4884.1 813,-4913.1 813,-4913.1 813,-4919.1 807,-4925.1 801,-4925.1 801,-4925.1 628.75,-4925.1 628.75,-4925.1 622.75,-4925.1 616.75,-4919.1 616.75,-4913.1 616.75,-4913.1 616.75,-4884.1 616.75,-4884.1 616.75,-4878.1 622.75,-4872.1 628.75,-4872.1"/>
<text text-anchor="middle" x="714.88" y="-4912.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useInitializeDeckBuilderFromLocation</text>
</g>
<g id="clust97" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M245.88,-5330.1C245.88,-5330.1 383.12,-5330.1 383.12,-5330.1 389.12,-5330.1 395.12,-5336.1 395.12,-5342.1 395.12,-5342.1 395.12,-5406.1 395.12,-5406.1 395.12,-5412.1 389.12,-5418.1 383.12,-5418.1 383.12,-5418.1 245.88,-5418.1 245.88,-5418.1 239.88,-5418.1 233.88,-5412.1 233.88,-5406.1 233.88,-5406.1 233.88,-5342.1 233.88,-5342.1 233.88,-5336.1 239.88,-5330.1 245.88,-5330.1"/>
<text text-anchor="middle" x="314.5" y="-5405.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust98" class="cluster">
<title>cluster_src/client/DeckBuilding/infrastructure/services/deckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M253.88,-5338.1C253.88,-5338.1 375.12,-5338.1 375.12,-5338.1 381.12,-5338.1 387.12,-5344.1 387.12,-5350.1 387.12,-5350.1 387.12,-5379.1 387.12,-5379.1 387.12,-5385.1 381.12,-5391.1 375.12,-5391.1 375.12,-5391.1 253.88,-5391.1 253.88,-5391.1 247.88,-5391.1 241.88,-5385.1 241.88,-5379.1 241.88,-5379.1 241.88,-5350.1 241.88,-5350.1 241.88,-5344.1 247.88,-5338.1 253.88,-5338.1"/>
<text text-anchor="middle" x="314.5" y="-5378.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deckDraft</text>
</g>
<g id="clust99" class="cluster">
<title>cluster_src/client/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M648.88,-5480.1C648.88,-5480.1 1043.25,-5480.1 1043.25,-5480.1 1049.25,-5480.1 1055.25,-5486.1 1055.25,-5492.1 1055.25,-5492.1 1055.25,-6045.1 1055.25,-6045.1 1055.25,-6051.1 1049.25,-6057.1 1043.25,-6057.1 1043.25,-6057.1 648.88,-6057.1 648.88,-6057.1 642.88,-6057.1 636.88,-6051.1 636.88,-6045.1 636.88,-6045.1 636.88,-5492.1 636.88,-5492.1 636.88,-5486.1 642.88,-5480.1 648.88,-5480.1"/>
<text text-anchor="middle" x="846.06" y="-6044.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust100" class="cluster">
<title>cluster_src/client/Gaming/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M656.88,-5488.1C656.88,-5488.1 1035.25,-5488.1 1035.25,-5488.1 1041.25,-5488.1 1047.25,-5494.1 1047.25,-5500.1 1047.25,-5500.1 1047.25,-6018.1 1047.25,-6018.1 1047.25,-6024.1 1041.25,-6030.1 1035.25,-6030.1 1035.25,-6030.1 656.88,-6030.1 656.88,-6030.1 650.88,-6030.1 644.88,-6024.1 644.88,-6018.1 644.88,-6018.1 644.88,-5500.1 644.88,-5500.1 644.88,-5494.1 650.88,-5488.1 656.88,-5488.1"/>
<text text-anchor="middle" x="846.06" y="-6017.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust101" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M877.25,-5688.1C877.25,-5688.1 1023.25,-5688.1 1023.25,-5688.1 1029.25,-5688.1 1035.25,-5694.1 1035.25,-5700.1 1035.25,-5700.1 1035.25,-5991.1 1035.25,-5991.1 1035.25,-5997.1 1029.25,-6003.1 1023.25,-6003.1 1023.25,-6003.1 877.25,-6003.1 877.25,-6003.1 871.25,-6003.1 865.25,-5997.1 865.25,-5991.1 865.25,-5991.1 865.25,-5700.1 865.25,-5700.1 865.25,-5694.1 871.25,-5688.1 877.25,-5688.1"/>
<text text-anchor="middle" x="950.25" y="-5990.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust102" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M885.25,-5696.1C885.25,-5696.1 1015.25,-5696.1 1015.25,-5696.1 1021.25,-5696.1 1027.25,-5702.1 1027.25,-5708.1 1027.25,-5708.1 1027.25,-5868.1 1027.25,-5868.1 1027.25,-5874.1 1021.25,-5880.1 1015.25,-5880.1 1015.25,-5880.1 885.25,-5880.1 885.25,-5880.1 879.25,-5880.1 873.25,-5874.1 873.25,-5868.1 873.25,-5868.1 873.25,-5708.1 873.25,-5708.1 873.25,-5702.1 879.25,-5696.1 885.25,-5696.1"/>
<text text-anchor="middle" x="950.25" y="-5867.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust103" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components/app/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M893.25,-5704.1C893.25,-5704.1 1007.25,-5704.1 1007.25,-5704.1 1013.25,-5704.1 1019.25,-5710.1 1019.25,-5716.1 1019.25,-5716.1 1019.25,-5841.1 1019.25,-5841.1 1019.25,-5847.1 1013.25,-5853.1 1007.25,-5853.1 1007.25,-5853.1 893.25,-5853.1 893.25,-5853.1 887.25,-5853.1 881.25,-5847.1 881.25,-5841.1 881.25,-5841.1 881.25,-5716.1 881.25,-5716.1 881.25,-5710.1 887.25,-5704.1 893.25,-5704.1"/>
<text text-anchor="middle" x="950.25" y="-5840.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust104" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M901.25,-5712.1C901.25,-5712.1 999.25,-5712.1 999.25,-5712.1 1005.25,-5712.1 1011.25,-5718.1 1011.25,-5724.1 1011.25,-5724.1 1011.25,-5753.1 1011.25,-5753.1 1011.25,-5759.1 1005.25,-5765.1 999.25,-5765.1 999.25,-5765.1 901.25,-5765.1 901.25,-5765.1 895.25,-5765.1 889.25,-5759.1 889.25,-5753.1 889.25,-5753.1 889.25,-5724.1 889.25,-5724.1 889.25,-5718.1 895.25,-5712.1 901.25,-5712.1"/>
<text text-anchor="middle" x="950.25" y="-5752.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatchButton</text>
</g>
<g id="clust105" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components/app/Gaming/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M927.12,-5773.1C927.12,-5773.1 973.38,-5773.1 973.38,-5773.1 979.38,-5773.1 985.38,-5779.1 985.38,-5785.1 985.38,-5785.1 985.38,-5814.1 985.38,-5814.1 985.38,-5820.1 979.38,-5826.1 973.38,-5826.1 973.38,-5826.1 927.12,-5826.1 927.12,-5826.1 921.12,-5826.1 915.12,-5820.1 915.12,-5814.1 915.12,-5814.1 915.12,-5785.1 915.12,-5785.1 915.12,-5779.1 921.12,-5773.1 927.12,-5773.1"/>
<text text-anchor="middle" x="950.25" y="-5813.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust106" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components/debug</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M888,-5888.1C888,-5888.1 1012.5,-5888.1 1012.5,-5888.1 1018.5,-5888.1 1024.5,-5894.1 1024.5,-5900.1 1024.5,-5900.1 1024.5,-5964.1 1024.5,-5964.1 1024.5,-5970.1 1018.5,-5976.1 1012.5,-5976.1 1012.5,-5976.1 888,-5976.1 888,-5976.1 882,-5976.1 876,-5970.1 876,-5964.1 876,-5964.1 876,-5900.1 876,-5900.1 876,-5894.1 882,-5888.1 888,-5888.1"/>
<text text-anchor="middle" x="950.25" y="-5963.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">debug</text>
</g>
<g id="clust107" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M896,-5896.1C896,-5896.1 1004.5,-5896.1 1004.5,-5896.1 1010.5,-5896.1 1016.5,-5902.1 1016.5,-5908.1 1016.5,-5908.1 1016.5,-5937.1 1016.5,-5937.1 1016.5,-5943.1 1010.5,-5949.1 1004.5,-5949.1 1004.5,-5949.1 896,-5949.1 896,-5949.1 890,-5949.1 884,-5943.1 884,-5937.1 884,-5937.1 884,-5908.1 884,-5908.1 884,-5902.1 890,-5896.1 896,-5896.1"/>
<text text-anchor="middle" x="950.25" y="-5936.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchConsoleEvents</text>
</g>
<g id="clust108" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/pages</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M664.88,-5496.1C664.88,-5496.1 1027.25,-5496.1 1027.25,-5496.1 1033.25,-5496.1 1039.25,-5502.1 1039.25,-5508.1 1039.25,-5508.1 1039.25,-5668.1 1039.25,-5668.1 1039.25,-5674.1 1033.25,-5680.1 1027.25,-5680.1 1027.25,-5680.1 664.88,-5680.1 664.88,-5680.1 658.88,-5680.1 652.88,-5674.1 652.88,-5668.1 652.88,-5668.1 652.88,-5508.1 652.88,-5508.1 652.88,-5502.1 658.88,-5496.1 664.88,-5496.1"/>
<text text-anchor="middle" x="846.06" y="-5667.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">pages</text>
</g>
<g id="clust109" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/pages/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M672.88,-5504.1C672.88,-5504.1 1019.25,-5504.1 1019.25,-5504.1 1025.25,-5504.1 1031.25,-5510.1 1031.25,-5516.1 1031.25,-5516.1 1031.25,-5641.1 1031.25,-5641.1 1031.25,-5647.1 1025.25,-5653.1 1019.25,-5653.1 1019.25,-5653.1 672.88,-5653.1 672.88,-5653.1 666.88,-5653.1 660.88,-5647.1 660.88,-5641.1 660.88,-5641.1 660.88,-5516.1 660.88,-5516.1 660.88,-5510.1 666.88,-5504.1 672.88,-5504.1"/>
<text text-anchor="middle" x="846.06" y="-5640.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust110" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M889.25,-5512.1C889.25,-5512.1 1011.25,-5512.1 1011.25,-5512.1 1017.25,-5512.1 1023.25,-5518.1 1023.25,-5524.1 1023.25,-5524.1 1023.25,-5553.1 1023.25,-5553.1 1023.25,-5559.1 1017.25,-5565.1 1011.25,-5565.1 1011.25,-5565.1 889.25,-5565.1 889.25,-5565.1 883.25,-5565.1 877.25,-5559.1 877.25,-5553.1 877.25,-5553.1 877.25,-5524.1 877.25,-5524.1 877.25,-5518.1 883.25,-5512.1 889.25,-5512.1"/>
<text text-anchor="middle" x="950.25" y="-5552.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ErrorLoadingMatchPage</text>
</g>
<g id="clust111" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M898.62,-5573.1C898.62,-5573.1 1001.88,-5573.1 1001.88,-5573.1 1007.88,-5573.1 1013.88,-5579.1 1013.88,-5585.1 1013.88,-5585.1 1013.88,-5614.1 1013.88,-5614.1 1013.88,-5620.1 1007.88,-5626.1 1001.88,-5626.1 1001.88,-5626.1 898.62,-5626.1 898.62,-5626.1 892.62,-5626.1 886.62,-5620.1 886.62,-5614.1 886.62,-5614.1 886.62,-5585.1 886.62,-5585.1 886.62,-5579.1 892.62,-5573.1 898.62,-5573.1"/>
<text text-anchor="middle" x="950.25" y="-5613.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FinishedMatchPage</text>
</g>
<g id="clust112" class="cluster">
<title>cluster_src/client/Gaming/infrastructure/pages/Gaming/MatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M680.88,-5573.1C680.88,-5573.1 748.88,-5573.1 748.88,-5573.1 754.88,-5573.1 760.88,-5579.1 760.88,-5585.1 760.88,-5585.1 760.88,-5614.1 760.88,-5614.1 760.88,-5620.1 754.88,-5626.1 748.88,-5626.1 748.88,-5626.1 680.88,-5626.1 680.88,-5626.1 674.88,-5626.1 668.88,-5620.1 668.88,-5614.1 668.88,-5614.1 668.88,-5585.1 668.88,-5585.1 668.88,-5579.1 674.88,-5573.1 680.88,-5573.1"/>
<text text-anchor="middle" x="714.88" y="-5613.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchPage</text>
</g>
<g id="clust113" class="cluster">
<title>cluster_src/client/MatchMaking</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M849,-2119.1C849,-2119.1 1051.5,-2119.1 1051.5,-2119.1 1057.5,-2119.1 1063.5,-2125.1 1063.5,-2131.1 1063.5,-2131.1 1063.5,-2431.1 1063.5,-2431.1 1063.5,-2437.1 1057.5,-2443.1 1051.5,-2443.1 1051.5,-2443.1 849,-2443.1 849,-2443.1 843,-2443.1 837,-2437.1 837,-2431.1 837,-2431.1 837,-2131.1 837,-2131.1 837,-2125.1 843,-2119.1 849,-2119.1"/>
<text text-anchor="middle" x="950.25" y="-2430.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMaking</text>
</g>
<g id="clust114" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M857,-2127.1C857,-2127.1 1043.5,-2127.1 1043.5,-2127.1 1049.5,-2127.1 1055.5,-2133.1 1055.5,-2139.1 1055.5,-2139.1 1055.5,-2404.1 1055.5,-2404.1 1055.5,-2410.1 1049.5,-2416.1 1043.5,-2416.1 1043.5,-2416.1 857,-2416.1 857,-2416.1 851,-2416.1 845,-2410.1 845,-2404.1 845,-2404.1 845,-2139.1 845,-2139.1 845,-2133.1 851,-2127.1 857,-2127.1"/>
<text text-anchor="middle" x="950.25" y="-2403.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust115" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M865,-2135.1C865,-2135.1 1035.5,-2135.1 1035.5,-2135.1 1041.5,-2135.1 1047.5,-2141.1 1047.5,-2147.1 1047.5,-2147.1 1047.5,-2377.1 1047.5,-2377.1 1047.5,-2383.1 1041.5,-2389.1 1035.5,-2389.1 1035.5,-2389.1 865,-2389.1 865,-2389.1 859,-2389.1 853,-2383.1 853,-2377.1 853,-2377.1 853,-2147.1 853,-2147.1 853,-2141.1 859,-2135.1 865,-2135.1"/>
<text text-anchor="middle" x="950.25" y="-2376.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust116" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M888.62,-2143.1C888.62,-2143.1 1011.88,-2143.1 1011.88,-2143.1 1017.88,-2143.1 1023.88,-2149.1 1023.88,-2155.1 1023.88,-2155.1 1023.88,-2254.1 1023.88,-2254.1 1023.88,-2260.1 1017.88,-2266.1 1011.88,-2266.1 1011.88,-2266.1 888.62,-2266.1 888.62,-2266.1 882.62,-2266.1 876.62,-2260.1 876.62,-2254.1 876.62,-2254.1 876.62,-2155.1 876.62,-2155.1 876.62,-2149.1 882.62,-2143.1 888.62,-2143.1"/>
<text text-anchor="middle" x="950.25" y="-2253.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust117" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure/components/app/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M896.62,-2151.1C896.62,-2151.1 1003.88,-2151.1 1003.88,-2151.1 1009.88,-2151.1 1015.88,-2157.1 1015.88,-2163.1 1015.88,-2163.1 1015.88,-2227.1 1015.88,-2227.1 1015.88,-2233.1 1009.88,-2239.1 1003.88,-2239.1 1003.88,-2239.1 896.62,-2239.1 896.62,-2239.1 890.62,-2239.1 884.62,-2233.1 884.62,-2227.1 884.62,-2227.1 884.62,-2163.1 884.62,-2163.1 884.62,-2157.1 890.62,-2151.1 896.62,-2151.1"/>
<text text-anchor="middle" x="950.25" y="-2226.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust118" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M904.62,-2159.1C904.62,-2159.1 995.88,-2159.1 995.88,-2159.1 1001.88,-2159.1 1007.88,-2165.1 1007.88,-2171.1 1007.88,-2171.1 1007.88,-2200.1 1007.88,-2200.1 1007.88,-2206.1 1001.88,-2212.1 995.88,-2212.1 995.88,-2212.1 904.62,-2212.1 904.62,-2212.1 898.62,-2212.1 892.62,-2206.1 892.62,-2200.1 892.62,-2200.1 892.62,-2171.1 892.62,-2171.1 892.62,-2165.1 898.62,-2159.1 904.62,-2159.1"/>
<text text-anchor="middle" x="950.25" y="-2199.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">StartGameButton</text>
</g>
<g id="clust119" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure/components/debug</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M873,-2274.1C873,-2274.1 1027.5,-2274.1 1027.5,-2274.1 1033.5,-2274.1 1039.5,-2280.1 1039.5,-2286.1 1039.5,-2286.1 1039.5,-2350.1 1039.5,-2350.1 1039.5,-2356.1 1033.5,-2362.1 1027.5,-2362.1 1027.5,-2362.1 873,-2362.1 873,-2362.1 867,-2362.1 861,-2356.1 861,-2350.1 861,-2350.1 861,-2286.1 861,-2286.1 861,-2280.1 867,-2274.1 873,-2274.1"/>
<text text-anchor="middle" x="950.25" y="-2349.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">debug</text>
</g>
<g id="clust120" class="cluster">
<title>cluster_src/client/MatchMaking/infrastructure/components/debug/MatchMakingConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M881,-2282.1C881,-2282.1 1019.5,-2282.1 1019.5,-2282.1 1025.5,-2282.1 1031.5,-2288.1 1031.5,-2294.1 1031.5,-2294.1 1031.5,-2323.1 1031.5,-2323.1 1031.5,-2329.1 1025.5,-2335.1 1019.5,-2335.1 1019.5,-2335.1 881,-2335.1 881,-2335.1 875,-2335.1 869,-2329.1 869,-2323.1 869,-2323.1 869,-2294.1 869,-2294.1 869,-2288.1 875,-2282.1 881,-2282.1"/>
<text text-anchor="middle" x="950.25" y="-2322.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMakingConsoleEvents</text>
</g>
<g id="clust121" class="cluster">
<title>cluster_src/client/Shared</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M634.62,-775.1C634.62,-775.1 1535,-775.1 1535,-775.1 1541,-775.1 1547,-781.1 1547,-787.1 1547,-787.1 1547,-2099.1 1547,-2099.1 1547,-2105.1 1541,-2111.1 1535,-2111.1 1535,-2111.1 634.62,-2111.1 634.62,-2111.1 628.62,-2111.1 622.62,-2105.1 622.62,-2099.1 622.62,-2099.1 622.62,-787.1 622.62,-787.1 622.62,-781.1 628.62,-775.1 634.62,-775.1"/>
<text text-anchor="middle" x="1084.81" y="-2098.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Shared</text>
</g>
<g id="clust122" class="cluster">
<title>cluster_src/client/Shared/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1291.12,-1996.1C1291.12,-1996.1 1527,-1996.1 1527,-1996.1 1533,-1996.1 1539,-2002.1 1539,-2008.1 1539,-2008.1 1539,-2072.1 1539,-2072.1 1539,-2078.1 1533,-2084.1 1527,-2084.1 1527,-2084.1 1291.12,-2084.1 1291.12,-2084.1 1285.12,-2084.1 1279.12,-2078.1 1279.12,-2072.1 1279.12,-2072.1 1279.12,-2008.1 1279.12,-2008.1 1279.12,-2002.1 1285.12,-1996.1 1291.12,-1996.1"/>
<text text-anchor="middle" x="1409.06" y="-2071.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust123" class="cluster">
<title>cluster_src/client/Shared/application/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1436,-2004.1C1436,-2004.1 1519,-2004.1 1519,-2004.1 1525,-2004.1 1531,-2010.1 1531,-2016.1 1531,-2016.1 1531,-2045.1 1531,-2045.1 1531,-2051.1 1525,-2057.1 1519,-2057.1 1519,-2057.1 1436,-2057.1 1436,-2057.1 1430,-2057.1 1424,-2051.1 1424,-2045.1 1424,-2045.1 1424,-2016.1 1424,-2016.1 1424,-2010.1 1430,-2004.1 1436,-2004.1"/>
<text text-anchor="middle" x="1477.5" y="-2044.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust124" class="cluster">
<title>cluster_src/client/Shared/application/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1299.12,-2004.1C1299.12,-2004.1 1353.62,-2004.1 1353.62,-2004.1 1359.62,-2004.1 1365.62,-2010.1 1365.62,-2016.1 1365.62,-2016.1 1365.62,-2045.1 1365.62,-2045.1 1365.62,-2051.1 1359.62,-2057.1 1353.62,-2057.1 1353.62,-2057.1 1299.12,-2057.1 1299.12,-2057.1 1293.12,-2057.1 1287.12,-2051.1 1287.12,-2045.1 1287.12,-2045.1 1287.12,-2016.1 1287.12,-2016.1 1287.12,-2010.1 1293.12,-2004.1 1299.12,-2004.1"/>
<text text-anchor="middle" x="1326.38" y="-2044.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<g id="clust125" class="cluster">
<title>cluster_src/client/Shared/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M642.62,-783.1C642.62,-783.1 1400.38,-783.1 1400.38,-783.1 1406.38,-783.1 1412.38,-789.1 1412.38,-795.1 1412.38,-795.1 1412.38,-1976.1 1412.38,-1976.1 1412.38,-1982.1 1406.38,-1988.1 1400.38,-1988.1 1400.38,-1988.1 642.62,-1988.1 642.62,-1988.1 636.62,-1988.1 630.62,-1982.1 630.62,-1976.1 630.62,-1976.1 630.62,-795.1 630.62,-795.1 630.62,-789.1 636.62,-783.1 642.62,-783.1"/>
<text text-anchor="middle" x="1021.5" y="-1975.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust126" class="cluster">
<title>cluster_src/client/Shared/infrastructure/builders</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1126.62,-1568.1C1126.62,-1568.1 1182.62,-1568.1 1182.62,-1568.1 1188.62,-1568.1 1194.62,-1574.1 1194.62,-1580.1 1194.62,-1580.1 1194.62,-1609.1 1194.62,-1609.1 1194.62,-1615.1 1188.62,-1621.1 1182.62,-1621.1 1182.62,-1621.1 1126.62,-1621.1 1126.62,-1621.1 1120.62,-1621.1 1114.62,-1615.1 1114.62,-1609.1 1114.62,-1609.1 1114.62,-1580.1 1114.62,-1580.1 1114.62,-1574.1 1120.62,-1568.1 1126.62,-1568.1"/>
<text text-anchor="middle" x="1154.62" y="-1608.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">builders</text>
</g>
<g id="clust127" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M881.88,-791.1C881.88,-791.1 1203.38,-791.1 1203.38,-791.1 1209.38,-791.1 1215.38,-797.1 1215.38,-803.1 1215.38,-803.1 1215.38,-1425.1 1215.38,-1425.1 1215.38,-1431.1 1209.38,-1437.1 1203.38,-1437.1 1203.38,-1437.1 881.88,-1437.1 881.88,-1437.1 875.88,-1437.1 869.88,-1431.1 869.88,-1425.1 869.88,-1425.1 869.88,-803.1 869.88,-803.1 869.88,-797.1 875.88,-791.1 881.88,-791.1"/>
<text text-anchor="middle" x="1042.62" y="-1424.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust128" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/Background</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M914.75,-799.1C914.75,-799.1 985.75,-799.1 985.75,-799.1 991.75,-799.1 997.75,-805.1 997.75,-811.1 997.75,-811.1 997.75,-840.1 997.75,-840.1 997.75,-846.1 991.75,-852.1 985.75,-852.1 985.75,-852.1 914.75,-852.1 914.75,-852.1 908.75,-852.1 902.75,-846.1 902.75,-840.1 902.75,-840.1 902.75,-811.1 902.75,-811.1 902.75,-805.1 908.75,-799.1 914.75,-799.1"/>
<text text-anchor="middle" x="950.25" y="-839.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Background</text>
</g>
<g id="clust129" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M891.75,-860.1C891.75,-860.1 1008.75,-860.1 1008.75,-860.1 1014.75,-860.1 1020.75,-866.1 1020.75,-872.1 1020.75,-872.1 1020.75,-997.1 1020.75,-997.1 1020.75,-1003.1 1014.75,-1009.1 1008.75,-1009.1 1008.75,-1009.1 891.75,-1009.1 891.75,-1009.1 885.75,-1009.1 879.75,-1003.1 879.75,-997.1 879.75,-997.1 879.75,-872.1 879.75,-872.1 879.75,-866.1 885.75,-860.1 891.75,-860.1"/>
<text text-anchor="middle" x="950.25" y="-996.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust130" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/Catalog/GameDetailsButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M899.75,-868.1C899.75,-868.1 1000.75,-868.1 1000.75,-868.1 1006.75,-868.1 1012.75,-874.1 1012.75,-880.1 1012.75,-880.1 1012.75,-909.1 1012.75,-909.1 1012.75,-915.1 1006.75,-921.1 1000.75,-921.1 1000.75,-921.1 899.75,-921.1 899.75,-921.1 893.75,-921.1 887.75,-915.1 887.75,-909.1 887.75,-909.1 887.75,-880.1 887.75,-880.1 887.75,-874.1 893.75,-868.1 899.75,-868.1"/>
<text text-anchor="middle" x="950.25" y="-908.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsButton</text>
</g>
<g id="clust131" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/Catalog/PlayGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M905,-929.1C905,-929.1 995.5,-929.1 995.5,-929.1 1001.5,-929.1 1007.5,-935.1 1007.5,-941.1 1007.5,-941.1 1007.5,-970.1 1007.5,-970.1 1007.5,-976.1 1001.5,-982.1 995.5,-982.1 995.5,-982.1 905,-982.1 905,-982.1 899,-982.1 893,-976.1 893,-970.1 893,-970.1 893,-941.1 893,-941.1 893,-935.1 899,-929.1 905,-929.1"/>
<text text-anchor="middle" x="950.25" y="-969.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGameButton</text>
</g>
<g id="clust132" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/JsonObjectViewer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M902.75,-1017.1C902.75,-1017.1 997.75,-1017.1 997.75,-1017.1 1003.75,-1017.1 1009.75,-1023.1 1009.75,-1029.1 1009.75,-1029.1 1009.75,-1058.1 1009.75,-1058.1 1009.75,-1064.1 1003.75,-1070.1 997.75,-1070.1 997.75,-1070.1 902.75,-1070.1 902.75,-1070.1 896.75,-1070.1 890.75,-1064.1 890.75,-1058.1 890.75,-1058.1 890.75,-1029.1 890.75,-1029.1 890.75,-1023.1 896.75,-1017.1 902.75,-1017.1"/>
<text text-anchor="middle" x="950.25" y="-1057.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">JsonObjectViewer</text>
</g>
<g id="clust133" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/ShiningButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M910.62,-1078.1C910.62,-1078.1 1195.38,-1078.1 1195.38,-1078.1 1201.38,-1078.1 1207.38,-1084.1 1207.38,-1090.1 1207.38,-1090.1 1207.38,-1119.1 1207.38,-1119.1 1207.38,-1125.1 1201.38,-1131.1 1195.38,-1131.1 1195.38,-1131.1 910.62,-1131.1 910.62,-1131.1 904.62,-1131.1 898.62,-1125.1 898.62,-1119.1 898.62,-1119.1 898.62,-1090.1 898.62,-1090.1 898.62,-1084.1 904.62,-1078.1 910.62,-1078.1"/>
<text text-anchor="middle" x="1053" y="-1118.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningButton</text>
</g>
<g id="clust134" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/ShiningCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M913.62,-1139.1C913.62,-1139.1 1192.38,-1139.1 1192.38,-1139.1 1198.38,-1139.1 1204.38,-1145.1 1204.38,-1151.1 1204.38,-1151.1 1204.38,-1180.1 1204.38,-1180.1 1204.38,-1186.1 1198.38,-1192.1 1192.38,-1192.1 1192.38,-1192.1 913.62,-1192.1 913.62,-1192.1 907.62,-1192.1 901.62,-1186.1 901.62,-1180.1 901.62,-1180.1 901.62,-1151.1 901.62,-1151.1 901.62,-1145.1 907.62,-1139.1 913.62,-1139.1"/>
<text text-anchor="middle" x="1053" y="-1179.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningCard</text>
</g>
<g id="clust135" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/SkeletonHelper</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M908,-1200.1C908,-1200.1 992.5,-1200.1 992.5,-1200.1 998.5,-1200.1 1004.5,-1206.1 1004.5,-1212.1 1004.5,-1212.1 1004.5,-1241.1 1004.5,-1241.1 1004.5,-1247.1 998.5,-1253.1 992.5,-1253.1 992.5,-1253.1 908,-1253.1 908,-1253.1 902,-1253.1 896,-1247.1 896,-1241.1 896,-1241.1 896,-1212.1 896,-1212.1 896,-1206.1 902,-1200.1 908,-1200.1"/>
<text text-anchor="middle" x="950.25" y="-1240.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SkeletonHelper</text>
</g>
<g id="clust136" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/Sparkles</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M921.5,-1357.1C921.5,-1357.1 979,-1357.1 979,-1357.1 985,-1357.1 991,-1363.1 991,-1369.1 991,-1369.1 991,-1398.1 991,-1398.1 991,-1404.1 985,-1410.1 979,-1410.1 979,-1410.1 921.5,-1410.1 921.5,-1410.1 915.5,-1410.1 909.5,-1404.1 909.5,-1398.1 909.5,-1398.1 909.5,-1369.1 909.5,-1369.1 909.5,-1363.1 915.5,-1357.1 921.5,-1357.1"/>
<text text-anchor="middle" x="950.25" y="-1397.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Sparkles</text>
</g>
<g id="clust137" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/redirections</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M889.88,-1261.1C889.88,-1261.1 1010.62,-1261.1 1010.62,-1261.1 1016.62,-1261.1 1022.62,-1267.1 1022.62,-1273.1 1022.62,-1273.1 1022.62,-1337.1 1022.62,-1337.1 1022.62,-1343.1 1016.62,-1349.1 1010.62,-1349.1 1010.62,-1349.1 889.88,-1349.1 889.88,-1349.1 883.88,-1349.1 877.88,-1343.1 877.88,-1337.1 877.88,-1337.1 877.88,-1273.1 877.88,-1273.1 877.88,-1267.1 883.88,-1261.1 889.88,-1261.1"/>
<text text-anchor="middle" x="950.25" y="-1336.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">redirections</text>
</g>
<g id="clust138" class="cluster">
<title>cluster_src/client/Shared/infrastructure/components/redirections/RedirectToGameList</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M897.88,-1269.1C897.88,-1269.1 1002.62,-1269.1 1002.62,-1269.1 1008.62,-1269.1 1014.62,-1275.1 1014.62,-1281.1 1014.62,-1281.1 1014.62,-1310.1 1014.62,-1310.1 1014.62,-1316.1 1008.62,-1322.1 1002.62,-1322.1 1002.62,-1322.1 897.88,-1322.1 897.88,-1322.1 891.88,-1322.1 885.88,-1316.1 885.88,-1310.1 885.88,-1310.1 885.88,-1281.1 885.88,-1281.1 885.88,-1275.1 891.88,-1269.1 897.88,-1269.1"/>
<text text-anchor="middle" x="950.25" y="-1309.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RedirectToGameList</text>
</g>
<g id="clust139" class="cluster">
<title>cluster_src/client/Shared/infrastructure/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1083.5,-1629.1C1083.5,-1629.1 1371.75,-1629.1 1371.75,-1629.1 1377.75,-1629.1 1383.75,-1635.1 1383.75,-1641.1 1383.75,-1641.1 1383.75,-1853.1 1383.75,-1853.1 1383.75,-1859.1 1377.75,-1865.1 1371.75,-1865.1 1371.75,-1865.1 1083.5,-1865.1 1083.5,-1865.1 1077.5,-1865.1 1071.5,-1859.1 1071.5,-1853.1 1071.5,-1853.1 1071.5,-1641.1 1071.5,-1641.1 1071.5,-1635.1 1077.5,-1629.1 1083.5,-1629.1"/>
<text text-anchor="middle" x="1227.62" y="-1852.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust140" class="cluster">
<title>cluster_src/client/Shared/infrastructure/hooks/useDebounce</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1131.62,-1637.1C1131.62,-1637.1 1363.75,-1637.1 1363.75,-1637.1 1369.75,-1637.1 1375.75,-1643.1 1375.75,-1649.1 1375.75,-1649.1 1375.75,-1678.1 1375.75,-1678.1 1375.75,-1684.1 1369.75,-1690.1 1363.75,-1690.1 1363.75,-1690.1 1131.62,-1690.1 1131.62,-1690.1 1125.62,-1690.1 1119.62,-1684.1 1119.62,-1678.1 1119.62,-1678.1 1119.62,-1649.1 1119.62,-1649.1 1119.62,-1643.1 1125.62,-1637.1 1131.62,-1637.1"/>
<text text-anchor="middle" x="1247.69" y="-1677.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDebounce</text>
</g>
<g id="clust141" class="cluster">
<title>cluster_src/client/Shared/infrastructure/hooks/useGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1122.5,-1759.1C1122.5,-1759.1 1186.75,-1759.1 1186.75,-1759.1 1192.75,-1759.1 1198.75,-1765.1 1198.75,-1771.1 1198.75,-1771.1 1198.75,-1800.1 1198.75,-1800.1 1198.75,-1806.1 1192.75,-1812.1 1186.75,-1812.1 1186.75,-1812.1 1122.5,-1812.1 1122.5,-1812.1 1116.5,-1812.1 1110.5,-1806.1 1110.5,-1800.1 1110.5,-1800.1 1110.5,-1771.1 1110.5,-1771.1 1110.5,-1765.1 1116.5,-1759.1 1122.5,-1759.1"/>
<text text-anchor="middle" x="1154.62" y="-1799.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameId</text>
</g>
<g id="clust142" class="cluster">
<title>cluster_src/client/Shared/infrastructure/hooks/useLocale</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1124.75,-1698.1C1124.75,-1698.1 1184.5,-1698.1 1184.5,-1698.1 1190.5,-1698.1 1196.5,-1704.1 1196.5,-1710.1 1196.5,-1710.1 1196.5,-1739.1 1196.5,-1739.1 1196.5,-1745.1 1190.5,-1751.1 1184.5,-1751.1 1184.5,-1751.1 1124.75,-1751.1 1124.75,-1751.1 1118.75,-1751.1 1112.75,-1745.1 1112.75,-1739.1 1112.75,-1739.1 1112.75,-1710.1 1112.75,-1710.1 1112.75,-1704.1 1118.75,-1698.1 1124.75,-1698.1"/>
<text text-anchor="middle" x="1154.62" y="-1738.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useLocale</text>
</g>
<g id="clust143" class="cluster">
<title>cluster_src/client/Shared/infrastructure/layouts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M663.88,-1812.1C663.88,-1812.1 765.88,-1812.1 765.88,-1812.1 771.88,-1812.1 777.88,-1818.1 777.88,-1824.1 777.88,-1824.1 777.88,-1949.1 777.88,-1949.1 777.88,-1955.1 771.88,-1961.1 765.88,-1961.1 765.88,-1961.1 663.88,-1961.1 663.88,-1961.1 657.88,-1961.1 651.88,-1955.1 651.88,-1949.1 651.88,-1949.1 651.88,-1824.1 651.88,-1824.1 651.88,-1818.1 657.88,-1812.1 663.88,-1812.1"/>
<text text-anchor="middle" x="714.88" y="-1948.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">layouts</text>
</g>
<g id="clust144" class="cluster">
<title>cluster_src/client/Shared/infrastructure/layouts/FullPageLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M671.88,-1881.1C671.88,-1881.1 757.88,-1881.1 757.88,-1881.1 763.88,-1881.1 769.88,-1887.1 769.88,-1893.1 769.88,-1893.1 769.88,-1922.1 769.88,-1922.1 769.88,-1928.1 763.88,-1934.1 757.88,-1934.1 757.88,-1934.1 671.88,-1934.1 671.88,-1934.1 665.88,-1934.1 659.88,-1928.1 659.88,-1922.1 659.88,-1922.1 659.88,-1893.1 659.88,-1893.1 659.88,-1887.1 665.88,-1881.1 671.88,-1881.1"/>
<text text-anchor="middle" x="714.88" y="-1921.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FullPageLayout</text>
</g>
<g id="clust145" class="cluster">
<title>cluster_src/client/Shared/infrastructure/layouts/RootLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M680.5,-1820.1C680.5,-1820.1 749.25,-1820.1 749.25,-1820.1 755.25,-1820.1 761.25,-1826.1 761.25,-1832.1 761.25,-1832.1 761.25,-1861.1 761.25,-1861.1 761.25,-1867.1 755.25,-1873.1 749.25,-1873.1 749.25,-1873.1 680.5,-1873.1 680.5,-1873.1 674.5,-1873.1 668.5,-1867.1 668.5,-1861.1 668.5,-1861.1 668.5,-1832.1 668.5,-1832.1 668.5,-1826.1 674.5,-1820.1 680.5,-1820.1"/>
<text text-anchor="middle" x="714.88" y="-1860.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RootLayout</text>
</g>
<g id="clust146" class="cluster">
<title>cluster_src/client/Shared/infrastructure/lib</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1102.25,-1445.1C1102.25,-1445.1 1207,-1445.1 1207,-1445.1 1213,-1445.1 1219,-1451.1 1219,-1457.1 1219,-1457.1 1219,-1548.1 1219,-1548.1 1219,-1554.1 1213,-1560.1 1207,-1560.1 1207,-1560.1 1102.25,-1560.1 1102.25,-1560.1 1096.25,-1560.1 1090.25,-1554.1 1090.25,-1548.1 1090.25,-1548.1 1090.25,-1457.1 1090.25,-1457.1 1090.25,-1451.1 1096.25,-1445.1 1102.25,-1445.1"/>
<text text-anchor="middle" x="1154.62" y="-1547.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">lib</text>
</g>
<g id="clust147" class="cluster">
<title>cluster_src/client/Shared/infrastructure/pages</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M650.62,-1165.1C650.62,-1165.1 779.12,-1165.1 779.12,-1165.1 785.12,-1165.1 791.12,-1171.1 791.12,-1177.1 791.12,-1177.1 791.12,-1398.1 791.12,-1398.1 791.12,-1404.1 785.12,-1410.1 779.12,-1410.1 779.12,-1410.1 650.62,-1410.1 650.62,-1410.1 644.62,-1410.1 638.62,-1404.1 638.62,-1398.1 638.62,-1398.1 638.62,-1177.1 638.62,-1177.1 638.62,-1171.1 644.62,-1165.1 650.62,-1165.1"/>
<text text-anchor="middle" x="714.88" y="-1397.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">pages</text>
</g>
<g id="clust148" class="cluster">
<title>cluster_src/client/Shared/infrastructure/pages/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M658.62,-1173.1C658.62,-1173.1 771.12,-1173.1 771.12,-1173.1 777.12,-1173.1 783.12,-1179.1 783.12,-1185.1 783.12,-1185.1 783.12,-1371.1 783.12,-1371.1 783.12,-1377.1 777.12,-1383.1 771.12,-1383.1 771.12,-1383.1 658.62,-1383.1 658.62,-1383.1 652.62,-1383.1 646.62,-1377.1 646.62,-1371.1 646.62,-1371.1 646.62,-1185.1 646.62,-1185.1 646.62,-1179.1 652.62,-1173.1 658.62,-1173.1"/>
<text text-anchor="middle" x="714.88" y="-1370.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust149" class="cluster">
<title>cluster_src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M666.62,-1242.1C666.62,-1242.1 763.12,-1242.1 763.12,-1242.1 769.12,-1242.1 775.12,-1248.1 775.12,-1254.1 775.12,-1254.1 775.12,-1283.1 775.12,-1283.1 775.12,-1289.1 769.12,-1295.1 763.12,-1295.1 763.12,-1295.1 666.62,-1295.1 666.62,-1295.1 660.62,-1295.1 654.62,-1289.1 654.62,-1283.1 654.62,-1283.1 654.62,-1254.1 654.62,-1254.1 654.62,-1248.1 660.62,-1242.1 666.62,-1242.1"/>
<text text-anchor="middle" x="714.88" y="-1282.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsPage</text>
</g>
<g id="clust150" class="cluster">
<title>cluster_src/client/Shared/infrastructure/pages/Catalog/GameListPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M673.75,-1303.1C673.75,-1303.1 756,-1303.1 756,-1303.1 762,-1303.1 768,-1309.1 768,-1315.1 768,-1315.1 768,-1344.1 768,-1344.1 768,-1350.1 762,-1356.1 756,-1356.1 756,-1356.1 673.75,-1356.1 673.75,-1356.1 667.75,-1356.1 661.75,-1350.1 661.75,-1344.1 661.75,-1344.1 661.75,-1315.1 661.75,-1315.1 661.75,-1309.1 667.75,-1303.1 673.75,-1303.1"/>
<text text-anchor="middle" x="714.88" y="-1343.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameListPage</text>
</g>
<g id="clust151" class="cluster">
<title>cluster_src/client/Shared/infrastructure/pages/Catalog/PlayGamePage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M671.88,-1181.1C671.88,-1181.1 757.88,-1181.1 757.88,-1181.1 763.88,-1181.1 769.88,-1187.1 769.88,-1193.1 769.88,-1193.1 769.88,-1222.1 769.88,-1222.1 769.88,-1228.1 763.88,-1234.1 757.88,-1234.1 757.88,-1234.1 671.88,-1234.1 671.88,-1234.1 665.88,-1234.1 659.88,-1228.1 659.88,-1222.1 659.88,-1222.1 659.88,-1193.1 659.88,-1193.1 659.88,-1187.1 665.88,-1181.1 671.88,-1181.1"/>
<text text-anchor="middle" x="714.88" y="-1221.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGamePage</text>
</g>
<g id="clust152" class="cluster">
<title>cluster_src/client/Shared/infrastructure/providers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M894.88,-1789.1C894.88,-1789.1 1005.62,-1789.1 1005.62,-1789.1 1011.62,-1789.1 1017.62,-1795.1 1017.62,-1801.1 1017.62,-1801.1 1017.62,-1892.1 1017.62,-1892.1 1017.62,-1898.1 1011.62,-1904.1 1005.62,-1904.1 1005.62,-1904.1 894.88,-1904.1 894.88,-1904.1 888.88,-1904.1 882.88,-1898.1 882.88,-1892.1 882.88,-1892.1 882.88,-1801.1 882.88,-1801.1 882.88,-1795.1 888.88,-1789.1 894.88,-1789.1"/>
<text text-anchor="middle" x="950.25" y="-1891.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">providers</text>
</g>
<g id="clust153" class="cluster">
<title>cluster_src/client/Shared/infrastructure/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1260.38,-1873.1C1260.38,-1873.1 1392.38,-1873.1 1392.38,-1873.1 1398.38,-1873.1 1404.38,-1879.1 1404.38,-1885.1 1404.38,-1885.1 1404.38,-1949.1 1404.38,-1949.1 1404.38,-1955.1 1398.38,-1961.1 1392.38,-1961.1 1392.38,-1961.1 1260.38,-1961.1 1260.38,-1961.1 1254.38,-1961.1 1248.38,-1955.1 1248.38,-1949.1 1248.38,-1949.1 1248.38,-1885.1 1248.38,-1885.1 1248.38,-1879.1 1254.38,-1873.1 1260.38,-1873.1"/>
<text text-anchor="middle" x="1326.38" y="-1948.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust154" class="cluster">
<title>cluster_src/client/Shared/infrastructure/services/location</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1268.38,-1881.1C1268.38,-1881.1 1384.38,-1881.1 1384.38,-1881.1 1390.38,-1881.1 1396.38,-1887.1 1396.38,-1893.1 1396.38,-1893.1 1396.38,-1922.1 1396.38,-1922.1 1396.38,-1928.1 1390.38,-1934.1 1384.38,-1934.1 1384.38,-1934.1 1268.38,-1934.1 1268.38,-1934.1 1262.38,-1934.1 1256.38,-1928.1 1256.38,-1922.1 1256.38,-1922.1 1256.38,-1893.1 1256.38,-1893.1 1256.38,-1887.1 1262.38,-1881.1 1268.38,-1881.1"/>
<text text-anchor="middle" x="1326.38" y="-1921.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">location</text>
</g>
<g id="clust155" class="cluster">
<title>cluster_src/client/Shared/infrastructure/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1131.62,-1908.1C1131.62,-1908.1 1177.62,-1908.1 1177.62,-1908.1 1183.62,-1908.1 1189.62,-1914.1 1189.62,-1920.1 1189.62,-1920.1 1189.62,-1949.1 1189.62,-1949.1 1189.62,-1955.1 1183.62,-1961.1 1177.62,-1961.1 1177.62,-1961.1 1131.62,-1961.1 1131.62,-1961.1 1125.62,-1961.1 1119.62,-1955.1 1119.62,-1949.1 1119.62,-1949.1 1119.62,-1920.1 1119.62,-1920.1 1119.62,-1914.1 1125.62,-1908.1 1131.62,-1908.1"/>
<text text-anchor="middle" x="1154.62" y="-1948.55" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx -->
<g id="node1" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx</title>
<g id="a_node1"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/loading.tsx" xlink:title="loading.tsx">
<path fill="#ccffcc" stroke="black" d="M118.08,-6389.35C118.08,-6389.35 70.17,-6389.35 70.17,-6389.35 67.08,-6389.35 64,-6386.27 64,-6383.18 64,-6383.18 64,-6377.02 64,-6377.02 64,-6373.93 67.08,-6370.85 70.17,-6370.85 70.17,-6370.85 118.08,-6370.85 118.08,-6370.85 121.17,-6370.85 124.25,-6373.93 124.25,-6377.02 124.25,-6377.02 124.25,-6383.18 124.25,-6383.18 124.25,-6386.27 121.17,-6389.35 118.08,-6389.35"/>
<text text-anchor="start" x="72" y="-6376.8" font-family="Helvetica,sans-Serif" font-size="9.00">loading.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx -->
<g id="node2" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx</title>
<g id="a_node2"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6420.35C114.96,-6420.35 73.29,-6420.35 73.29,-6420.35 70.21,-6420.35 67.12,-6417.27 67.12,-6414.18 67.12,-6414.18 67.12,-6408.02 67.12,-6408.02 67.12,-6404.93 70.21,-6401.85 73.29,-6401.85 73.29,-6401.85 114.96,-6401.85 114.96,-6401.85 118.04,-6401.85 121.12,-6404.93 121.12,-6408.02 121.12,-6408.02 121.12,-6414.18 121.12,-6414.18 121.12,-6417.27 118.04,-6420.35 114.96,-6420.35"/>
<text text-anchor="start" x="76.88" y="-6407.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="node3" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<g id="a_node3"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx" xlink:title="DeckBuilderCardsGrid.tsx">
<path fill="#bbfeff" stroke="black" d="M369.21,-5031.35C369.21,-5031.35 259.79,-5031.35 259.79,-5031.35 256.71,-5031.35 253.62,-5028.27 253.62,-5025.18 253.62,-5025.18 253.62,-5019.02 253.62,-5019.02 253.62,-5015.93 256.71,-5012.85 259.79,-5012.85 259.79,-5012.85 369.21,-5012.85 369.21,-5012.85 372.29,-5012.85 375.38,-5015.93 375.38,-5019.02 375.38,-5019.02 375.38,-5025.18 375.38,-5025.18 375.38,-5028.27 372.29,-5031.35 369.21,-5031.35"/>
<text text-anchor="start" x="261.62" y="-5018.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCardsGrid.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge1" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6413.34C138.82,-6413.21 160.29,-6409.7 172.25,-6395.1 183.97,-6380.79 169.21,-5077.95 180.25,-5063.1 195.36,-5042.77 220.39,-5032.21 244.7,-5026.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="244.8,-5028.95 250.28,-5025.72 243.99,-5024.83 244.8,-5028.95"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="node4" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<g id="a_node4"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx" xlink:title="DeckBuilderPanel.tsx">
<path fill="#bbfeff" stroke="black" d="M360.21,-4848.35C360.21,-4848.35 268.79,-4848.35 268.79,-4848.35 265.71,-4848.35 262.62,-4845.27 262.62,-4842.18 262.62,-4842.18 262.62,-4836.02 262.62,-4836.02 262.62,-4832.93 265.71,-4829.85 268.79,-4829.85 268.79,-4829.85 360.21,-4829.85 360.21,-4829.85 363.29,-4829.85 366.38,-4832.93 366.38,-4836.02 366.38,-4836.02 366.38,-4842.18 366.38,-4842.18 366.38,-4845.27 363.29,-4848.35 360.21,-4848.35"/>
<text text-anchor="start" x="270.62" y="-4835.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderPanel.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge2" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6413.34C138.82,-6413.21 160.3,-6409.7 172.25,-6395.1 185.04,-6379.47 171.26,-4959.18 180.25,-4941.1 201.54,-4898.29 250.28,-4868.46 282.54,-4852.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="283.12,-4854.6 287.63,-4850.11 281.31,-4850.81 283.12,-4854.6"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="node5" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<g id="a_node5"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx" xlink:title="DeckBuildingFilters.tsx">
<path fill="#bbfeff" stroke="black" d="M362.83,-4970.35C362.83,-4970.35 266.17,-4970.35 266.17,-4970.35 263.08,-4970.35 260,-4967.27 260,-4964.18 260,-4964.18 260,-4958.02 260,-4958.02 260,-4954.93 263.08,-4951.85 266.17,-4951.85 266.17,-4951.85 362.83,-4951.85 362.83,-4951.85 365.92,-4951.85 369,-4954.93 369,-4958.02 369,-4958.02 369,-4964.18 369,-4964.18 369,-4967.27 365.92,-4970.35 362.83,-4970.35"/>
<text text-anchor="start" x="268" y="-4957.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingFilters.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge3" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6413.34C138.82,-6413.21 160.3,-6409.7 172.25,-6395.1 184.51,-6380.13 168.71,-5017.63 180.25,-5002.1 196.66,-4980.02 224.76,-4969.46 250.94,-4964.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.16,-4966.64 256.74,-4963.59 250.47,-4962.5 251.16,-4966.64"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="node6" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<g id="a_node6"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx" xlink:title="DeckDraftInitializer.tsx">
<path fill="#bbfeff" stroke="black" d="M361.33,-5092.35C361.33,-5092.35 267.67,-5092.35 267.67,-5092.35 264.58,-5092.35 261.5,-5089.27 261.5,-5086.18 261.5,-5086.18 261.5,-5080.02 261.5,-5080.02 261.5,-5076.93 264.58,-5073.85 267.67,-5073.85 267.67,-5073.85 361.33,-5073.85 361.33,-5073.85 364.42,-5073.85 367.5,-5076.93 367.5,-5080.02 367.5,-5080.02 367.5,-5086.18 367.5,-5086.18 367.5,-5089.27 364.42,-5092.35 361.33,-5092.35"/>
<text text-anchor="start" x="269.5" y="-5079.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge4" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6413.34C138.81,-6413.21 160.29,-6409.7 172.25,-6395.1 194.62,-6367.79 159.19,-5152.43 180.25,-5124.1 196.92,-5101.67 225.66,-5091.14 252.18,-5086.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="252.49,-5088.4 258.09,-5085.38 251.83,-5084.26 252.49,-5088.4"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="node7" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<g id="a_node7"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx" xlink:title="EditDeckInitializer.tsx">
<path fill="#bbfeff" stroke="black" d="M359.46,-5153.35C359.46,-5153.35 269.54,-5153.35 269.54,-5153.35 266.46,-5153.35 263.38,-5150.27 263.38,-5147.18 263.38,-5147.18 263.38,-5141.02 263.38,-5141.02 263.38,-5137.93 266.46,-5134.85 269.54,-5134.85 269.54,-5134.85 359.46,-5134.85 359.46,-5134.85 362.54,-5134.85 365.62,-5137.93 365.62,-5141.02 365.62,-5141.02 365.62,-5147.18 365.62,-5147.18 365.62,-5150.27 362.54,-5153.35 359.46,-5153.35"/>
<text text-anchor="start" x="271.38" y="-5140.8" font-family="Helvetica,sans-Serif" font-size="9.00">EditDeckInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge5" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.55,-6413.34C138.81,-6413.21 160.29,-6409.7 172.25,-6395.1 193.55,-6369.1 160.19,-5212.07 180.25,-5185.1 197.39,-5162.05 227.27,-5151.56 254.39,-5146.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="254.47,-5149.05 260.09,-5146.09 253.85,-5144.9 254.47,-5149.05"/>
</g>
<!-- src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="node39" class="node">
<title>src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<g id="a_node39"><a xlink:href="src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts" xlink:title="useLocale.ts">
<path fill="#ddfeff" stroke="black" d="M1182.33,-1724.35C1182.33,-1724.35 1126.92,-1724.35 1126.92,-1724.35 1123.83,-1724.35 1120.75,-1721.27 1120.75,-1718.18 1120.75,-1718.18 1120.75,-1712.02 1120.75,-1712.02 1120.75,-1708.93 1123.83,-1705.85 1126.92,-1705.85 1126.92,-1705.85 1182.33,-1705.85 1182.33,-1705.85 1185.42,-1705.85 1188.5,-1708.93 1188.5,-1712.02 1188.5,-1712.02 1188.5,-1718.18 1188.5,-1718.18 1188.5,-1721.27 1185.42,-1724.35 1182.33,-1724.35"/>
<text text-anchor="start" x="1128.75" y="-1711.8" font-family="Helvetica,sans-Serif" font-size="9.00">useLocale.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge137" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.32,-5031.75C368.32,-5037.95 385.57,-5047.8 395.12,-5063.1 406.02,-5080.54 388.43,-5418.72 403.12,-5433.1 418.71,-5448.35 583.06,-5447.57 599.38,-5433.1 625.5,-5409.93 583.61,-5379.33 608.75,-5355.1 627.07,-5337.44 811.62,-5338 837,-5336.1 862.15,-5334.22 1046.24,-5339.49 1063.5,-5321.1 1080.44,-5303.04 1058.45,-1777.14 1071.5,-1756.1 1080.65,-1741.34 1096.9,-1731.71 1112.54,-1725.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1112.85,-1727.65 1117.78,-1723.63 1111.42,-1723.7 1112.85,-1727.65"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="node107" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<g id="a_node107"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx" xlink:title="DeckBuildingCard.tsx">
<path fill="#bbfeff" stroke="black" d="M546.21,-5031.35C546.21,-5031.35 454.04,-5031.35 454.04,-5031.35 450.96,-5031.35 447.88,-5028.27 447.88,-5025.18 447.88,-5025.18 447.88,-5019.02 447.88,-5019.02 447.88,-5015.93 450.96,-5012.85 454.04,-5012.85 454.04,-5012.85 546.21,-5012.85 546.21,-5012.85 549.29,-5012.85 552.38,-5015.93 552.38,-5019.02 552.38,-5019.02 552.38,-5025.18 552.38,-5025.18 552.38,-5028.27 549.29,-5031.35 546.21,-5031.35"/>
<text text-anchor="start" x="455.88" y="-5018.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="edge131" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M375.63,-5022.1C395.85,-5022.1 418.42,-5022.1 438.67,-5022.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="438.47,-5024.2 444.47,-5022.1 438.47,-5020 438.47,-5024.2"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="node108" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<g id="a_node108"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx" xlink:title="TotalCardsInDeckCounter.tsx">
<path fill="#bbfeff" stroke="black" d="M561.21,-4909.35C561.21,-4909.35 439.04,-4909.35 439.04,-4909.35 435.96,-4909.35 432.88,-4906.27 432.88,-4903.18 432.88,-4903.18 432.88,-4897.02 432.88,-4897.02 432.88,-4893.93 435.96,-4890.85 439.04,-4890.85 439.04,-4890.85 561.21,-4890.85 561.21,-4890.85 564.29,-4890.85 567.38,-4893.93 567.38,-4897.02 567.38,-4897.02 567.38,-4903.18 567.38,-4903.18 567.38,-4906.27 564.29,-4909.35 561.21,-4909.35"/>
<text text-anchor="start" x="440.88" y="-4896.8" font-family="Helvetica,sans-Serif" font-size="9.00">TotalCardsInDeckCounter.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge132" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M375.69,-5013.87C383.06,-5010.6 389.84,-5006.15 395.12,-5000.1 412.53,-4980.17 387.09,-4962.15 403.12,-4941.1 413.13,-4927.96 428.18,-4918.98 443.41,-4912.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="444.11,-4914.84 449.02,-4910.8 442.66,-4910.9 444.11,-4914.84"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="node109" class="node">
<title>src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<g id="a_node109"><a xlink:href="src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx" xlink:title="useCardsByGameId.tsx">
<path fill="#bbfeff" stroke="black" d="M764.71,-4654.35C764.71,-4654.35 665.04,-4654.35 665.04,-4654.35 661.96,-4654.35 658.88,-4651.27 658.88,-4648.18 658.88,-4648.18 658.88,-4642.02 658.88,-4642.02 658.88,-4638.93 661.96,-4635.85 665.04,-4635.85 665.04,-4635.85 764.71,-4635.85 764.71,-4635.85 767.79,-4635.85 770.88,-4638.93 770.88,-4642.02 770.88,-4642.02 770.88,-4648.18 770.88,-4648.18 770.88,-4651.27 767.79,-4654.35 764.71,-4654.35"/>
<text text-anchor="start" x="666.88" y="-4641.8" font-family="Helvetica,sans-Serif" font-size="9.00">useCardsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="edge133" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M375.53,-5014.74C383.12,-5011.37 390.01,-5006.66 395.12,-5000.1 406.24,-4985.86 390.82,-4688.32 403.12,-4675.1 470.94,-4602.24 601.38,-4618.48 669.69,-4633.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="668.84,-4635.43 675.15,-4634.71 669.77,-4631.34 668.84,-4635.43"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="node110" class="node">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<g id="a_node110"><a xlink:href="src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts" xlink:title="useDeckBuilder.ts">
<path fill="#ddfeff" stroke="black" d="M753.46,-4715.35C753.46,-4715.35 676.29,-4715.35 676.29,-4715.35 673.21,-4715.35 670.12,-4712.27 670.12,-4709.18 670.12,-4709.18 670.12,-4703.02 670.12,-4703.02 670.12,-4699.93 673.21,-4696.85 676.29,-4696.85 676.29,-4696.85 753.46,-4696.85 753.46,-4696.85 756.54,-4696.85 759.62,-4699.93 759.62,-4703.02 759.62,-4703.02 759.62,-4709.18 759.62,-4709.18 759.62,-4712.27 756.54,-4715.35 753.46,-4715.35"/>
<text text-anchor="start" x="678.12" y="-4702.8" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge134" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M375.52,-5014.73C383.11,-5011.36 390,-5006.65 395.12,-5000.1 405.5,-4986.84 391.06,-4708.85 403.12,-4697.1 438.49,-4662.65 582.02,-4682.04 661.05,-4695.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="660.63,-4697.96 666.91,-4696.95 661.37,-4693.83 660.63,-4697.96"/>
</g>
<!-- src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="node111" class="node">
<title>src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<g id="a_node111"><a xlink:href="src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts" xlink:title="useDebounce.ts">
<path fill="#ddfeff" stroke="black" d="M1361.58,-1663.35C1361.58,-1663.35 1291.17,-1663.35 1291.17,-1663.35 1288.08,-1663.35 1285,-1660.27 1285,-1657.18 1285,-1657.18 1285,-1651.02 1285,-1651.02 1285,-1647.93 1288.08,-1644.85 1291.17,-1644.85 1291.17,-1644.85 1361.58,-1644.85 1361.58,-1644.85 1364.67,-1644.85 1367.75,-1647.93 1367.75,-1651.02 1367.75,-1651.02 1367.75,-1657.18 1367.75,-1657.18 1367.75,-1660.27 1364.67,-1663.35 1361.58,-1663.35"/>
<text text-anchor="start" x="1293" y="-1650.8" font-family="Helvetica,sans-Serif" font-size="9.00">useDebounce.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="edge135" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.32,-5031.75C368.33,-5037.95 385.57,-5047.8 395.12,-5063.1 406.31,-5081.01 388.04,-5428.33 403.12,-5443.1 465.45,-5504.12 526.61,-5491.19 599.38,-5443.1 608.24,-5437.24 600.48,-5427.78 608.75,-5421.1 768.88,-5291.73 901.71,-5460.39 1063.5,-5333.1 1212.13,-5216.16 1199.91,-5130.83 1240.38,-4946.1 1258.61,-4862.85 1234.22,-1962.14 1248.38,-1878.1 1261.84,-1798.14 1300.25,-1708.46 1317.18,-1671.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1318.93,-1672.7 1319.55,-1666.37 1315.12,-1670.93 1318.93,-1672.7"/>
</g>
<!-- src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="node112" class="node">
<title>src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts</title>
<g id="a_node112"><a xlink:href="src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts" xlink:title="useGameId.ts">
<path fill="#ddfeff" stroke="black" d="M1184.58,-1785.35C1184.58,-1785.35 1124.67,-1785.35 1124.67,-1785.35 1121.58,-1785.35 1118.5,-1782.27 1118.5,-1779.18 1118.5,-1779.18 1118.5,-1773.02 1118.5,-1773.02 1118.5,-1769.93 1121.58,-1766.85 1124.67,-1766.85 1124.67,-1766.85 1184.58,-1766.85 1184.58,-1766.85 1187.67,-1766.85 1190.75,-1769.93 1190.75,-1773.02 1190.75,-1773.02 1190.75,-1779.18 1190.75,-1779.18 1190.75,-1782.27 1187.67,-1785.35 1184.58,-1785.35"/>
<text text-anchor="start" x="1126.5" y="-1772.8" font-family="Helvetica,sans-Serif" font-size="9.00">useGameId.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge136" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.33,-5031.75C368.33,-5037.95 385.57,-5047.8 395.12,-5063.1 406.42,-5081.2 387.88,-5432.17 403.12,-5447.1 434.28,-5477.61 566.44,-5475.69 599.38,-5447.1 621.75,-5427.68 587.38,-5401.62 608.75,-5381.1 645.38,-5345.92 1029.4,-5372.74 1063.5,-5335.1 1080.03,-5316.85 1055.49,-1807.81 1071.5,-1789.1 1080.83,-1778.2 1095.29,-1773.81 1109.49,-1772.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1109.35,-1774.62 1115.23,-1772.22 1109.14,-1770.42 1109.35,-1774.62"/>
</g>
<!-- src/client/Shared/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="node113" class="node">
<title>src/client/Shared/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<g id="a_node113"><a xlink:href="src/client/Shared/infrastructure/hooks/useResponsiveColumnCount.tsx" xlink:title="useResponsiveColumnCount.tsx">
<path fill="#bbfeff" stroke="black" d="M1223.58,-1838.35C1223.58,-1838.35 1085.67,-1838.35 1085.67,-1838.35 1082.58,-1838.35 1079.5,-1835.27 1079.5,-1832.18 1079.5,-1832.18 1079.5,-1826.02 1079.5,-1826.02 1079.5,-1822.93 1082.58,-1819.85 1085.67,-1819.85 1085.67,-1819.85 1223.58,-1819.85 1223.58,-1819.85 1226.67,-1819.85 1229.75,-1822.93 1229.75,-1826.02 1229.75,-1826.02 1229.75,-1832.18 1229.75,-1832.18 1229.75,-1835.27 1226.67,-1838.35 1223.58,-1838.35"/>
<text text-anchor="start" x="1087.5" y="-1825.8" font-family="Helvetica,sans-Serif" font-size="9.00">useResponsiveColumnCount.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="edge138" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.34,-5031.74C368.34,-5037.94 385.58,-5047.79 395.12,-5063.1 407.23,-5082.53 386.78,-5459.07 403.12,-5475.1 434.26,-5505.64 566.38,-5503.62 599.38,-5475.1 621.13,-5456.3 588.02,-5431.01 608.75,-5411.1 682.04,-5340.71 995.43,-5438.55 1063.5,-5363.1 1079.59,-5345.27 1061.86,-1927.09 1071.5,-1905.1 1083.21,-1878.4 1109.41,-1856.88 1129.09,-1843.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1130.1,-1845.5 1134,-1840.47 1127.82,-1841.98 1130.1,-1845.5"/>
</g>
<!-- src/client/Shared/infrastructure/lib/chunk.ts -->
<g id="node114" class="node">
<title>src/client/Shared/infrastructure/lib/chunk.ts</title>
<g id="a_node114"><a xlink:href="src/client/Shared/infrastructure/lib/chunk.ts" xlink:title="chunk.ts">
<path fill="#ddfeff" stroke="black" d="M1175.46,-1502.35C1175.46,-1502.35 1133.79,-1502.35 1133.79,-1502.35 1130.71,-1502.35 1127.62,-1499.27 1127.62,-1496.18 1127.62,-1496.18 1127.62,-1490.02 1127.62,-1490.02 1127.62,-1486.93 1130.71,-1483.85 1133.79,-1483.85 1133.79,-1483.85 1175.46,-1483.85 1175.46,-1483.85 1178.54,-1483.85 1181.62,-1486.93 1181.62,-1490.02 1181.62,-1490.02 1181.62,-1496.18 1181.62,-1496.18 1181.62,-1499.27 1178.54,-1502.35 1175.46,-1502.35"/>
<text text-anchor="start" x="1137.75" y="-1489.8" font-family="Helvetica,sans-Serif" font-size="9.00">chunk.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/lib/chunk.ts -->
<g id="edge139" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/Shared/infrastructure/lib/chunk.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.31,-5031.75C368.31,-5037.96 385.56,-5047.81 395.12,-5063.1 405.5,-5079.68 389.14,-5401.43 403.12,-5415.1 418.72,-5430.34 583.07,-5429.58 599.38,-5415.1 612.59,-5403.36 601.85,-5352.37 608.75,-5336.1 669.23,-5193.53 767.73,-5208.32 829,-5066.1 1148.74,-4323.96 1009.44,-4069.37 1063.5,-3263.1 1064.31,-3250.95 1063.73,-1518.48 1071.5,-1509.1 1082.78,-1495.47 1101.91,-1491.19 1118.87,-1490.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1118.49,-1492.55 1124.46,-1490.38 1118.44,-1488.35 1118.49,-1492.55"/>
</g>
<!-- src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="node38" class="node">
<title>src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<g id="a_node38"><a xlink:href="src/client/Shared/infrastructure/builders/urlBuilder.ts" xlink:title="urlBuilder.ts">
<path fill="#ddfeff" stroke="black" d="M1180.46,-1594.35C1180.46,-1594.35 1128.79,-1594.35 1128.79,-1594.35 1125.71,-1594.35 1122.62,-1591.27 1122.62,-1588.18 1122.62,-1588.18 1122.62,-1582.02 1122.62,-1582.02 1122.62,-1578.93 1125.71,-1575.85 1128.79,-1575.85 1128.79,-1575.85 1180.46,-1575.85 1180.46,-1575.85 1183.54,-1575.85 1186.62,-1578.93 1186.62,-1582.02 1186.62,-1582.02 1186.62,-1588.18 1186.62,-1588.18 1186.62,-1591.27 1183.54,-1594.35 1180.46,-1594.35"/>
<text text-anchor="start" x="1130.62" y="-1581.8" font-family="Helvetica,sans-Serif" font-size="9.00">urlBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="edge150" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.68,-4848.71C345.08,-4865.33 381.1,-4901.5 395.12,-4941.1 399.25,-4952.75 394.3,-5377.44 403.12,-5386.1 418.68,-5401.38 583.06,-5400.57 599.38,-5386.1 612.44,-5374.52 601.49,-5323.98 608.75,-5308.1 669.24,-5175.85 767.86,-5198.06 829,-5066.1 1168.7,-4332.89 1009.3,-4069.36 1063.5,-3263.1 1065.02,-3240.53 1060.28,-1653.75 1071.5,-1634.1 1080.98,-1617.5 1098.55,-1605.92 1115.01,-1598.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1115.54,-1600.26 1120.19,-1595.93 1113.85,-1596.41 1115.54,-1600.26"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge152" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.68,-4848.7C345.09,-4865.32 381.11,-4901.5 395.12,-4941.1 399.42,-4953.25 393.93,-5396.06 403.12,-5405.1 418.68,-5420.39 582.88,-5419.36 599.38,-5405.1 621.13,-5386.3 588.32,-5361.33 608.75,-5341.1 681.23,-5269.34 996.34,-5355.86 1063.5,-5279.1 1079.61,-5260.69 1058.61,-1776.89 1071.5,-1756.1 1080.65,-1741.34 1096.9,-1731.71 1112.54,-1725.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1112.85,-1727.65 1117.78,-1723.63 1111.42,-1723.7 1112.85,-1727.65"/>
</g>
<!-- src/client/Shared/application/store/appStore.ts -->
<g id="node46" class="node">
<title>src/client/Shared/application/store/appStore.ts</title>
<g id="a_node46"><a xlink:href="src/client/Shared/application/store/appStore.ts" xlink:title="appStore.ts">
<path fill="#ddfeff" stroke="black" d="M1351.46,-2030.35C1351.46,-2030.35 1301.29,-2030.35 1301.29,-2030.35 1298.21,-2030.35 1295.12,-2027.27 1295.12,-2024.18 1295.12,-2024.18 1295.12,-2018.02 1295.12,-2018.02 1295.12,-2014.93 1298.21,-2011.85 1301.29,-2011.85 1301.29,-2011.85 1351.46,-2011.85 1351.46,-2011.85 1354.54,-2011.85 1357.62,-2014.93 1357.62,-2018.02 1357.62,-2018.02 1357.62,-2024.18 1357.62,-2024.18 1357.62,-2027.27 1354.54,-2030.35 1351.46,-2030.35"/>
<text text-anchor="start" x="1303.12" y="-2017.8" font-family="Helvetica,sans-Serif" font-size="9.00">appStore.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge149" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.68,-4848.7C345.09,-4865.32 381.12,-4901.49 395.12,-4941.1 399.51,-4953.52 393.74,-5405.87 403.12,-5415.1 465.32,-5476.25 513.92,-5432.57 599.38,-5415.1 603.81,-5414.19 604.31,-5412.01 608.75,-5411.1 704.66,-5391.49 738.56,-5373.64 829,-5411.1 833.65,-5413.02 832.35,-5417.18 837,-5419.1 930,-5457.62 975.94,-5468.78 1063.5,-5419.1 1196.79,-5343.47 1199.31,-5270.75 1240.38,-5123.1 1247.86,-5096.19 1247.34,-3140.01 1248.38,-3112.1 1264.6,-2676.45 1313.66,-2144.69 1323.6,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.16,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="node47" class="node">
<title>src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<g id="a_node47"><a xlink:href="src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts" xlink:title="clearDeckDraft.ts">
<path fill="#ddfeff" stroke="black" d="M986.96,-2562.35C986.96,-2562.35 913.54,-2562.35 913.54,-2562.35 910.46,-2562.35 907.38,-2559.27 907.38,-2556.18 907.38,-2556.18 907.38,-2550.02 907.38,-2550.02 907.38,-2546.93 910.46,-2543.85 913.54,-2543.85 913.54,-2543.85 986.96,-2543.85 986.96,-2543.85 990.04,-2543.85 993.12,-2546.93 993.12,-2550.02 993.12,-2550.02 993.12,-2556.18 993.12,-2556.18 993.12,-2559.27 990.04,-2562.35 986.96,-2562.35"/>
<text text-anchor="start" x="915.38" y="-2549.8" font-family="Helvetica,sans-Serif" font-size="9.00">clearDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="edge142" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.47,-4848.78C344.56,-4865.52 380.14,-4901.85 395.12,-4941.1 399.89,-4953.58 393.37,-5051.97 403.12,-5061.1 434.97,-5090.89 556.27,-5067.7 599.38,-5061.1 705.1,-5044.91 765.54,-5075.2 829,-4989.1 848.74,-4962.32 817.89,-2621.33 837,-2594.1 851,-2574.15 875.8,-2563.87 898.53,-2558.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="898.7,-2560.71 904.15,-2557.43 897.84,-2556.59 898.7,-2560.71"/>
</g>
<!-- src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="node94" class="node">
<title>src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<g id="a_node94"><a xlink:href="src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts" xlink:title="hasDeckDraft.ts">
<path fill="#ddfeff" stroke="black" d="M1188.71,-4427.35C1188.71,-4427.35 1120.54,-4427.35 1120.54,-4427.35 1117.46,-4427.35 1114.38,-4424.27 1114.38,-4421.18 1114.38,-4421.18 1114.38,-4415.02 1114.38,-4415.02 1114.38,-4411.93 1117.46,-4408.85 1120.54,-4408.85 1120.54,-4408.85 1188.71,-4408.85 1188.71,-4408.85 1191.79,-4408.85 1194.88,-4411.93 1194.88,-4415.02 1194.88,-4415.02 1194.88,-4421.18 1194.88,-4421.18 1194.88,-4424.27 1191.79,-4427.35 1188.71,-4427.35"/>
<text text-anchor="start" x="1122.38" y="-4414.8" font-family="Helvetica,sans-Serif" font-size="9.00">hasDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="edge143" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.49,-4848.77C344.62,-4865.5 380.25,-4901.81 395.12,-4941.1 400.29,-4954.74 392.5,-5062.11 403.12,-5072.1 472.42,-5137.29 747.44,-5077.09 829,-5028.1 971.39,-4942.57 987.27,-4884.67 1063.5,-4737.1 1118.35,-4630.92 1143.52,-4487.1 1151.06,-4436.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1153.13,-4436.98 1151.91,-4430.74 1148.97,-4436.38 1153.13,-4436.98"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge146" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M334.22,-4848.71C351.68,-4857.36 378.64,-4869.93 403.12,-4878.1 414.74,-4881.98 427.37,-4885.4 439.54,-4888.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="439.03,-4890.37 445.35,-4889.69 439.98,-4886.28 439.03,-4890.37"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge147" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M341.05,-4829.45C359.51,-4821.02 383.22,-4806.84 395.12,-4786.1 402.66,-4772.97 392.11,-4660.48 403.12,-4650.1 466.59,-4590.26 529.98,-4597.25 599.38,-4650.1 611.85,-4659.6 597.32,-4673.37 608.75,-4684.1 622.65,-4697.15 642.22,-4703.29 660.71,-4705.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="660.47,-4708.04 666.67,-4706.64 660.95,-4703.86 660.47,-4708.04"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge151" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.54,-4848.75C344.74,-4865.45 380.48,-4901.73 395.12,-4941.1 401.41,-4957.99 390.07,-5090.67 403.12,-5103.1 540.74,-5234.09 689.12,-5194.67 829,-5066.1 1146.65,-4774.14 1010.27,-4552.25 1063.5,-4124.1 1065.5,-4108.01 1060.95,-1801.41 1071.5,-1789.1 1080.84,-1778.21 1095.3,-1773.81 1109.5,-1772.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1109.35,-1774.62 1115.24,-1772.22 1109.14,-1770.43 1109.35,-1774.62"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="node116" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<g id="a_node116"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx" xlink:title="DeckCardRow.tsx">
<path fill="#bbfeff" stroke="black" d="M538.33,-4848.35C538.33,-4848.35 461.92,-4848.35 461.92,-4848.35 458.83,-4848.35 455.75,-4845.27 455.75,-4842.18 455.75,-4842.18 455.75,-4836.02 455.75,-4836.02 455.75,-4832.93 458.83,-4829.85 461.92,-4829.85 461.92,-4829.85 538.33,-4829.85 538.33,-4829.85 541.42,-4829.85 544.5,-4832.93 544.5,-4836.02 544.5,-4836.02 544.5,-4842.18 544.5,-4842.18 544.5,-4845.27 541.42,-4848.35 538.33,-4848.35"/>
<text text-anchor="start" x="463.75" y="-4835.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardRow.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="edge141" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M366.65,-4839.1C391.51,-4839.1 421.35,-4839.1 446.41,-4839.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="446.38,-4841.2 452.38,-4839.1 446.38,-4837 446.38,-4841.2"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="node117" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<g id="a_node117"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx" xlink:title="DeckCardDetailsDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M557.08,-4726.35C557.08,-4726.35 443.17,-4726.35 443.17,-4726.35 440.08,-4726.35 437,-4723.27 437,-4720.18 437,-4720.18 437,-4714.02 437,-4714.02 437,-4710.93 440.08,-4707.85 443.17,-4707.85 443.17,-4707.85 557.08,-4707.85 557.08,-4707.85 560.17,-4707.85 563.25,-4710.93 563.25,-4714.02 563.25,-4714.02 563.25,-4720.18 563.25,-4720.18 563.25,-4723.27 560.17,-4726.35 557.08,-4726.35"/>
<text text-anchor="start" x="445" y="-4713.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardDetailsDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="edge144" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M338.32,-4829.36C356.28,-4820.56 380.63,-4805.95 395.12,-4786.1 402.76,-4775.65 394.56,-4767.81 403.12,-4758.1 414.66,-4745.02 431.09,-4735.98 447.1,-4729.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="447.62,-4731.84 452.56,-4727.83 446.2,-4727.88 447.62,-4731.84"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="node118" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<g id="a_node118"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx" xlink:title="SaveDeckDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M543.21,-4787.35C543.21,-4787.35 457.04,-4787.35 457.04,-4787.35 453.96,-4787.35 450.88,-4784.27 450.88,-4781.18 450.88,-4781.18 450.88,-4775.02 450.88,-4775.02 450.88,-4771.93 453.96,-4768.85 457.04,-4768.85 457.04,-4768.85 543.21,-4768.85 543.21,-4768.85 546.29,-4768.85 549.38,-4771.93 549.38,-4775.02 549.38,-4775.02 549.38,-4781.18 549.38,-4781.18 549.38,-4784.27 546.29,-4787.35 543.21,-4787.35"/>
<text text-anchor="start" x="458.88" y="-4774.8" font-family="Helvetica,sans-Serif" font-size="9.00">SaveDeckDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="edge145" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M344.6,-4829.43C376.2,-4818.94 426.8,-4802.13 461.42,-4790.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.96,-4792.66 466.99,-4788.78 460.63,-4788.67 461.96,-4792.66"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="node119" class="node">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<g id="a_node119"><a xlink:href="src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId.ts" xlink:title="useDeckId.ts">
<path fill="#ddfeff" stroke="black" d="M742.96,-4837.35C742.96,-4837.35 686.79,-4837.35 686.79,-4837.35 683.71,-4837.35 680.62,-4834.27 680.62,-4831.18 680.62,-4831.18 680.62,-4825.02 680.62,-4825.02 680.62,-4821.93 683.71,-4818.85 686.79,-4818.85 686.79,-4818.85 742.96,-4818.85 742.96,-4818.85 746.04,-4818.85 749.12,-4821.93 749.12,-4825.02 749.12,-4825.02 749.12,-4831.18 749.12,-4831.18 749.12,-4834.27 746.04,-4837.35 742.96,-4837.35"/>
<text text-anchor="start" x="688.62" y="-4824.8" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckId.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="edge148" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M340.49,-4829.52C358.91,-4821.05 382.82,-4806.79 395.12,-4786.1 405.28,-4769.03 388.47,-4710.5 403.12,-4697.1 467.5,-4638.25 535.11,-4638.13 599.38,-4697.1 617.29,-4713.54 592.63,-4787.9 608.75,-4806.1 624.18,-4823.52 649.56,-4829.18 671.51,-4830.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="671.26,-4832.5 677.32,-4830.59 671.39,-4828.31 671.26,-4832.5"/>
</g>
<!-- src/client/Shared/infrastructure/providers/ToastProvider.tsx -->
<g id="node120" class="node">
<title>src/client/Shared/infrastructure/providers/ToastProvider.tsx</title>
<g id="a_node120"><a xlink:href="src/client/Shared/infrastructure/providers/ToastProvider.tsx" xlink:title="ToastProvider.tsx">
<path fill="#bbfeff" stroke="black" d="M986.58,-1877.35C986.58,-1877.35 913.92,-1877.35 913.92,-1877.35 910.83,-1877.35 907.75,-1874.27 907.75,-1871.18 907.75,-1871.18 907.75,-1865.02 907.75,-1865.02 907.75,-1861.93 910.83,-1858.85 913.92,-1858.85 913.92,-1858.85 986.58,-1858.85 986.58,-1858.85 989.67,-1858.85 992.75,-1861.93 992.75,-1865.02 992.75,-1865.02 992.75,-1871.18 992.75,-1871.18 992.75,-1874.27 989.67,-1877.35 986.58,-1877.35"/>
<text text-anchor="start" x="915.75" y="-1864.8" font-family="Helvetica,sans-Serif" font-size="9.00">ToastProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ToastProvider.tsx -->
<g id="edge153" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.47,-4848.78C344.56,-4865.52 380.14,-4901.85 395.12,-4941.1 399.89,-4953.58 393.37,-5051.97 403.12,-5061.1 543.31,-5192.25 715.15,-5143.66 829,-4989.1 840.67,-4973.26 833.34,-2175.43 837,-2156.1 857.42,-2048.34 916.78,-1929.08 939.89,-1885.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="941.6,-1886.61 942.58,-1880.33 937.9,-1884.63 941.6,-1886.61"/>
</g>
<!-- src/client/Shared/infrastructure/store/store.ts -->
<g id="node121" class="node">
<title>src/client/Shared/infrastructure/store/store.ts</title>
<g id="a_node121"><a xlink:href="src/client/Shared/infrastructure/store/store.ts" xlink:title="store.ts">
<path fill="#ddfeff" stroke="black" d="M1175.46,-1934.35C1175.46,-1934.35 1133.79,-1934.35 1133.79,-1934.35 1130.71,-1934.35 1127.62,-1931.27 1127.62,-1928.18 1127.62,-1928.18 1127.62,-1922.02 1127.62,-1922.02 1127.62,-1918.93 1130.71,-1915.85 1133.79,-1915.85 1133.79,-1915.85 1175.46,-1915.85 1175.46,-1915.85 1178.54,-1915.85 1181.62,-1918.93 1181.62,-1922.02 1181.62,-1922.02 1181.62,-1928.18 1181.62,-1928.18 1181.62,-1931.27 1178.54,-1934.35 1175.46,-1934.35"/>
<text text-anchor="start" x="1140" y="-1921.8" font-family="Helvetica,sans-Serif" font-size="9.00">store.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/store/store.ts -->
<g id="edge154" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/Shared/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M326.68,-4848.71C345.07,-4865.33 381.09,-4901.5 395.12,-4941.1 403.22,-4963.93 385.84,-5360.13 403.12,-5377.1 418.68,-5392.38 581.18,-5389.12 599.38,-5377.1 608.24,-5371.24 600.6,-5361.92 608.75,-5355.1 767.8,-5222.11 934.02,-5413.03 1063.5,-5251.1 1069.26,-5243.89 1070.92,-2603.31 1071.5,-2594.1 1087.78,-2334.26 1137.79,-2020.77 1150.61,-1943.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1152.64,-1943.74 1151.55,-1937.48 1148.49,-1943.05 1152.64,-1943.74"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="node76" class="node">
<title>src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<g id="a_node76"><a xlink:href="src/client/DeckBuilding/domain/Catalog/Filter.ts" xlink:title="Filter.ts">
<path fill="#ddfeff" stroke="black" d="M1732.58,-4717.35C1732.58,-4717.35 1690.92,-4717.35 1690.92,-4717.35 1687.83,-4717.35 1684.75,-4714.27 1684.75,-4711.18 1684.75,-4711.18 1684.75,-4705.02 1684.75,-4705.02 1684.75,-4701.93 1687.83,-4698.85 1690.92,-4698.85 1690.92,-4698.85 1732.58,-4698.85 1732.58,-4698.85 1735.67,-4698.85 1738.75,-4701.93 1738.75,-4705.02 1738.75,-4705.02 1738.75,-4711.18 1738.75,-4711.18 1738.75,-4714.27 1735.67,-4717.35 1732.58,-4717.35"/>
<text text-anchor="start" x="1697.5" y="-4704.8" font-family="Helvetica,sans-Serif" font-size="9.00">Filter.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge159" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M351.03,-4970.73C367.15,-4976.98 384.87,-4986.88 395.12,-5002.1 406.1,-5018.38 390.88,-5074.75 403.12,-5090.1 493.72,-5203.6 568.65,-5191.1 713.88,-5191.1 713.88,-5191.1 713.88,-5191.1 1327.38,-5191.1 1405.91,-5191.1 1619.66,-5144.09 1662.25,-5078.1 1673.77,-5060.25 1667.45,-4908.16 1670.25,-4887.1 1678.2,-4827.31 1696.54,-4758.06 1705.56,-4726.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1707.48,-4726.95 1707.1,-4720.6 1703.44,-4725.8 1707.48,-4726.95"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge161" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M329.61,-4951.55C348.5,-4937.84 381.26,-4910.61 395.12,-4878.1 403.46,-4858.56 387.79,-4701.8 403.12,-4687.1 466.08,-4626.73 512.45,-4679.94 599.38,-4687.1 619.66,-4688.77 641.77,-4692.08 661.1,-4695.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="660.52,-4697.51 666.79,-4696.5 661.26,-4693.37 660.52,-4697.51"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="node122" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<g id="a_node122"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx" xlink:title="FilterItem.tsx">
<path fill="#bbfeff" stroke="black" d="M527.46,-4970.35C527.46,-4970.35 472.79,-4970.35 472.79,-4970.35 469.71,-4970.35 466.62,-4967.27 466.62,-4964.18 466.62,-4964.18 466.62,-4958.02 466.62,-4958.02 466.62,-4954.93 469.71,-4951.85 472.79,-4951.85 472.79,-4951.85 527.46,-4951.85 527.46,-4951.85 530.54,-4951.85 533.62,-4954.93 533.62,-4958.02 533.62,-4958.02 533.62,-4964.18 533.62,-4964.18 533.62,-4967.27 530.54,-4970.35 527.46,-4970.35"/>
<text text-anchor="start" x="474.62" y="-4957.8" font-family="Helvetica,sans-Serif" font-size="9.00">FilterItem.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="edge160" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M369.11,-4961.1C397.23,-4961.1 431.17,-4961.1 457.33,-4961.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="457.2,-4963.2 463.2,-4961.1 457.2,-4959 457.2,-4963.2"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge167" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.32,-5092.75C368.32,-5098.95 385.57,-5108.8 395.12,-5124.1 405.9,-5141.35 388.59,-5475.88 403.12,-5490.1 465.47,-5551.1 526.99,-5538.76 599.38,-5490.1 608.54,-5483.94 599.78,-5473.53 608.75,-5467.1 773.35,-5349.21 877.77,-5520.71 1063.5,-5440.1 1162.96,-5396.93 1197.42,-5371.65 1240.38,-5272.1 1246.32,-5258.33 1247.82,-3127.09 1248.38,-3112.1 1264.54,-2676.44 1313.64,-2144.69 1323.6,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.15,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="node58" class="node">
<title>src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<g id="a_node58"><a xlink:href="src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts" xlink:title="loadDeckDraft.ts">
<path fill="#ddfeff" stroke="black" d="M985.83,-3233.35C985.83,-3233.35 914.67,-3233.35 914.67,-3233.35 911.58,-3233.35 908.5,-3230.27 908.5,-3227.18 908.5,-3227.18 908.5,-3221.02 908.5,-3221.02 908.5,-3217.93 911.58,-3214.85 914.67,-3214.85 914.67,-3214.85 985.83,-3214.85 985.83,-3214.85 988.92,-3214.85 992,-3217.93 992,-3221.02 992,-3221.02 992,-3227.18 992,-3227.18 992,-3230.27 988.92,-3233.35 985.83,-3233.35"/>
<text text-anchor="start" x="916.5" y="-3220.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="edge165" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M367.68,-5085.98C485.98,-5091.27 768.97,-5096.42 829,-5029.1 954.39,-4888.48 950.37,-3425.54 949.37,-3242.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.47,-3242.85 949.33,-3236.86 947.27,-3242.87 951.47,-3242.85"/>
</g>
<!-- src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="node96" class="node">
<title>src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<g id="a_node96"><a xlink:href="src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts" xlink:title="isCatalogLoading.ts">
<path fill="#ddfeff" stroke="black" d="M1197.33,-4366.35C1197.33,-4366.35 1111.92,-4366.35 1111.92,-4366.35 1108.83,-4366.35 1105.75,-4363.27 1105.75,-4360.18 1105.75,-4360.18 1105.75,-4354.02 1105.75,-4354.02 1105.75,-4350.93 1108.83,-4347.85 1111.92,-4347.85 1111.92,-4347.85 1197.33,-4347.85 1197.33,-4347.85 1200.42,-4347.85 1203.5,-4350.93 1203.5,-4354.02 1203.5,-4354.02 1203.5,-4360.18 1203.5,-4360.18 1203.5,-4363.27 1200.42,-4366.35 1197.33,-4366.35"/>
<text text-anchor="start" x="1113.75" y="-4353.8" font-family="Helvetica,sans-Serif" font-size="9.00">isCatalogLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge166" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M353.4,-5092.81C443.38,-5113.61 673.94,-5153.44 829,-5061.1 981.73,-4970.15 1006.73,-4905.55 1063.5,-4737.1 1069.52,-4719.25 1061.4,-4414 1071.5,-4398.1 1079.64,-4385.28 1093.1,-4376.32 1106.79,-4370.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.3,-4372.19 1112.04,-4367.95 1105.7,-4368.31 1107.3,-4372.19"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="node42" class="node">
<title>src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<g id="a_node42"><a xlink:href="src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts" xlink:title="getCatalogCardById.ts">
<path fill="#ddfeff" stroke="black" d="M1202.58,-3390.35C1202.58,-3390.35 1106.67,-3390.35 1106.67,-3390.35 1103.58,-3390.35 1100.5,-3387.27 1100.5,-3384.18 1100.5,-3384.18 1100.5,-3378.02 1100.5,-3378.02 1100.5,-3374.93 1103.58,-3371.85 1106.67,-3371.85 1106.67,-3371.85 1202.58,-3371.85 1202.58,-3371.85 1205.67,-3371.85 1208.75,-3374.93 1208.75,-3378.02 1208.75,-3378.02 1208.75,-3384.18 1208.75,-3384.18 1208.75,-3387.27 1205.67,-3390.35 1202.58,-3390.35"/>
<text text-anchor="start" x="1108.5" y="-3377.8" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge169" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M365.98,-5143.81C482.66,-5142 765.89,-5130.8 829,-5065.1 1027.82,-4858.14 1034.27,-4080.6 1063.5,-3795.1 1065.61,-3774.48 1060.41,-3439.61 1071.5,-3422.1 1079.63,-3409.27 1093.08,-3400.31 1106.77,-3394.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.29,-3396.18 1112.03,-3391.94 1105.68,-3392.3 1107.29,-3396.18"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge172" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.3,-5153.76C368.3,-5159.96 385.55,-5169.82 395.12,-5185.1 404.86,-5200.64 390,-5502.29 403.12,-5515.1 465.54,-5576.02 512.33,-5520.7 599.38,-5515.1 745.73,-5505.68 1165.43,-5484.16 1240.38,-5358.1 1248.35,-5344.69 1247.8,-3127.69 1248.38,-3112.1 1264.51,-2676.44 1313.64,-2144.69 1323.6,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.15,-2033.82 1321.49,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="node59" class="node">
<title>src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<g id="a_node59"><a xlink:href="src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts" xlink:title="loadDeckIntoBuilder.ts">
<path fill="#ddfeff" stroke="black" d="M997.83,-2989.35C997.83,-2989.35 902.67,-2989.35 902.67,-2989.35 899.58,-2989.35 896.5,-2986.27 896.5,-2983.18 896.5,-2983.18 896.5,-2977.02 896.5,-2977.02 896.5,-2973.93 899.58,-2970.85 902.67,-2970.85 902.67,-2970.85 997.83,-2970.85 997.83,-2970.85 1000.92,-2970.85 1004,-2973.93 1004,-2977.02 1004,-2977.02 1004,-2983.18 1004,-2983.18 1004,-2986.27 1000.92,-2989.35 997.83,-2989.35"/>
<text text-anchor="start" x="904.5" y="-2976.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="edge168" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M365.93,-5136.43C489.55,-5117.13 799.73,-5065.5 829,-5029.1 846.48,-5007.37 820.97,-3043.92 837,-3021.1 848.79,-3004.31 868.22,-2994.38 887.62,-2988.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="888.06,-2990.56 893.29,-2986.95 886.95,-2986.51 888.06,-2990.56"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="edge170" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M365.83,-5146.25C487.04,-5151.08 788.13,-5160.89 829,-5141.1 834.12,-5138.62 832.07,-5133.94 837,-5131.1 925.21,-5080.3 996.11,-5173.39 1063.5,-5097.1 1075.13,-5083.94 1069.33,-4798.53 1071.5,-4781.1 1088.23,-4646.8 1132.56,-4489.25 1148.13,-4436.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.13,-4437.04 1149.83,-4430.69 1146.1,-4435.85 1150.13,-4437.04"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge171" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M365.92,-5150.85C378.08,-5152.2 391.05,-5153.41 403.12,-5154.1 592.19,-5164.85 660.71,-5227.92 829,-5141.1 834.06,-5138.49 832.07,-5133.94 837,-5131.1 925.21,-5080.3 996.64,-5173.86 1063.5,-5097.1 1076.25,-5082.46 1061.19,-4414.56 1071.5,-4398.1 1079.56,-4385.23 1093,-4376.26 1106.7,-4370.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.21,-4372.13 1111.96,-4367.9 1105.61,-4368.25 1107.21,-4372.13"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/Shared/infrastructure/store/store.ts -->
<g id="edge173" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/Shared/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M352.29,-5153.77C368.28,-5159.98 385.53,-5169.83 395.12,-5185.1 404.14,-5199.45 390.98,-5478.27 403.12,-5490.1 465.6,-5550.96 530.54,-5543.67 599.38,-5490.1 612.76,-5479.68 596.21,-5464.52 608.75,-5453.1 684.37,-5384.23 996.88,-5459.71 1063.5,-5382.1 1069.81,-5374.75 1070.9,-2603.76 1071.5,-2594.1 1087.76,-2334.26 1137.79,-2020.77 1150.61,-1943.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1152.64,-1943.74 1151.55,-1937.48 1148.49,-1943.05 1152.64,-1943.74"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx -->
<g id="node8" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx</title>
<g id="a_node8"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-6324.35C115.08,-6324.35 73.17,-6324.35 73.17,-6324.35 70.08,-6324.35 67,-6321.27 67,-6318.18 67,-6318.18 67,-6312.02 67,-6312.02 67,-6308.93 70.08,-6305.85 73.17,-6305.85 73.17,-6305.85 115.08,-6305.85 115.08,-6305.85 118.17,-6305.85 121.25,-6308.93 121.25,-6312.02 121.25,-6312.02 121.25,-6318.18 121.25,-6318.18 121.25,-6321.27 118.17,-6324.35 115.08,-6324.35"/>
<text text-anchor="start" x="75" y="-6311.8" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="node9" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<g id="a_node9"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts" xlink:title="DeckBuilderInitializer.ts">
<path fill="#ddfeff" stroke="black" d="M363.96,-4756.35C363.96,-4756.35 265.04,-4756.35 265.04,-4756.35 261.96,-4756.35 258.88,-4753.27 258.88,-4750.18 258.88,-4750.18 258.88,-4744.02 258.88,-4744.02 258.88,-4740.93 261.96,-4737.85 265.04,-4737.85 265.04,-4737.85 363.96,-4737.85 363.96,-4737.85 367.04,-4737.85 370.12,-4740.93 370.12,-4744.02 370.12,-4744.02 370.12,-4750.18 370.12,-4750.18 370.12,-4753.27 367.04,-4756.35 363.96,-4756.35"/>
<text text-anchor="start" x="266.88" y="-4743.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderInitializer.ts</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="edge6" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.44,-6318.46C138.65,-6319.04 160.11,-6316.38 172.25,-6302.1 185.59,-6286.41 169.65,-4836.76 180.25,-4819.1 198.61,-4788.49 234.96,-4770.1 265.07,-4759.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="265.47,-4761.66 270.5,-4757.77 264.14,-4757.67 265.47,-4761.66"/>
</g>
<!-- src/client/Shared/infrastructure/providers/ReduxProvider.tsx -->
<g id="node10" class="node">
<title>src/client/Shared/infrastructure/providers/ReduxProvider.tsx</title>
<g id="a_node10"><a xlink:href="src/client/Shared/infrastructure/providers/ReduxProvider.tsx" xlink:title="ReduxProvider.tsx">
<path fill="#bbfeff" stroke="black" d="M989.21,-1815.35C989.21,-1815.35 911.29,-1815.35 911.29,-1815.35 908.21,-1815.35 905.12,-1812.27 905.12,-1809.18 905.12,-1809.18 905.12,-1803.02 905.12,-1803.02 905.12,-1799.93 908.21,-1796.85 911.29,-1796.85 911.29,-1796.85 989.21,-1796.85 989.21,-1796.85 992.29,-1796.85 995.38,-1799.93 995.38,-1803.02 995.38,-1803.02 995.38,-1809.18 995.38,-1809.18 995.38,-1812.27 992.29,-1815.35 989.21,-1815.35"/>
<text text-anchor="start" x="913.12" y="-1802.8" font-family="Helvetica,sans-Serif" font-size="9.00">ReduxProvider.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ReduxProvider.tsx -->
<g id="edge7" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ReduxProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-6318.47C138.68,-6319.06 160.14,-6316.4 172.25,-6302.1 184.99,-6287.06 173.55,-644.64 180.25,-626.1 288.18,-327.33 603.55,-102.3 829,-326.1 839.71,-336.74 834.85,-1398.16 837,-1413.1 858.38,-1561.82 921.19,-1733.39 942.26,-1788.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="940.27,-1788.93 944.39,-1793.76 944.19,-1787.41 940.27,-1788.93"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="node115" class="node">
<title>src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<g id="a_node115"><a xlink:href="src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts" xlink:title="useInitializeDeckBuilderFromLocation.ts">
<path fill="#ddfeff" stroke="black" d="M798.83,-4898.35C798.83,-4898.35 630.92,-4898.35 630.92,-4898.35 627.83,-4898.35 624.75,-4895.27 624.75,-4892.18 624.75,-4892.18 624.75,-4886.02 624.75,-4886.02 624.75,-4882.93 627.83,-4879.85 630.92,-4879.85 630.92,-4879.85 798.83,-4879.85 798.83,-4879.85 801.92,-4879.85 805,-4882.93 805,-4886.02 805,-4886.02 805,-4892.18 805,-4892.18 805,-4895.27 801.92,-4898.35 798.83,-4898.35"/>
<text text-anchor="start" x="632.75" y="-4885.8" font-family="Helvetica,sans-Serif" font-size="9.00">useInitializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="edge140" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M327.64,-4737.4C343.77,-4725.2 373.67,-4704.91 403.12,-4697.1 487.43,-4674.74 536.02,-4637.15 599.38,-4697.1 613.12,-4710.1 596.42,-4852.75 608.75,-4867.1 611.28,-4870.04 614.09,-4872.65 617.13,-4874.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="615.87,-4876.65 622.04,-4878.18 618.17,-4873.14 615.87,-4876.65"/>
</g>
<!-- src/client/Shared/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/Shared/infrastructure/store/store.ts -->
<g id="edge242" class="edge">
<title>src/client/Shared/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/Shared/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M995.75,-1800.92C1018.88,-1800.79 1045.91,-1805.04 1063.5,-1822.1 1078.4,-1836.55 1060.33,-1850.61 1071.5,-1868.1 1083.81,-1887.38 1105.21,-1901.92 1123.09,-1911.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1122.01,-1913.29 1128.31,-1914.15 1123.92,-1909.55 1122.01,-1913.29"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx -->
<g id="node11" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx</title>
<g id="a_node11"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6355.35C114.96,-6355.35 73.29,-6355.35 73.29,-6355.35 70.21,-6355.35 67.12,-6352.27 67.12,-6349.18 67.12,-6349.18 67.12,-6343.02 67.12,-6343.02 67.12,-6339.93 70.21,-6336.85 73.29,-6336.85 73.29,-6336.85 114.96,-6336.85 114.96,-6336.85 118.04,-6336.85 121.12,-6339.93 121.12,-6343.02 121.12,-6343.02 121.12,-6349.18 121.12,-6349.18 121.12,-6352.27 118.04,-6355.35 114.96,-6355.35"/>
<text text-anchor="start" x="76.88" y="-6342.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge8" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6348.34C138.81,-6348.21 160.29,-6344.7 172.25,-6330.1 194.55,-6302.87 159.25,-5091.34 180.25,-5063.1 195.36,-5042.77 220.39,-5032.21 244.7,-5026.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="244.8,-5028.95 250.28,-5025.72 243.99,-5024.83 244.8,-5028.95"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge9" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6348.34C138.82,-6348.21 160.3,-6344.7 172.25,-6330.1 184.47,-6315.17 171.66,-4958.37 180.25,-4941.1 201.54,-4898.3 250.29,-4868.46 282.54,-4852.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="283.13,-4854.6 287.63,-4850.11 281.31,-4850.81 283.13,-4854.6"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge10" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6348.34C138.82,-6348.21 160.29,-6344.7 172.25,-6330.1 183.94,-6315.83 169.25,-5016.9 180.25,-5002.1 196.66,-4980.02 224.76,-4969.47 250.94,-4964.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.16,-4966.65 256.74,-4963.59 250.48,-4962.5 251.16,-4966.65"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge11" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.55,-6348.34C138.81,-6348.21 160.29,-6344.7 172.25,-6330.1 193.48,-6304.19 160.26,-5150.98 180.25,-5124.1 196.93,-5101.68 225.66,-5091.14 252.19,-5086.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="252.49,-5088.41 258.09,-5085.39 251.83,-5084.26 252.49,-5088.41"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge12" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.55,-6348.34C138.81,-6348.2 160.29,-6344.69 172.25,-6330.1 192.41,-6305.5 161.26,-5210.62 180.25,-5185.1 197.39,-5162.06 227.27,-5151.57 254.4,-5146.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="254.47,-5149.06 260.09,-5146.09 253.85,-5144.9 254.47,-5149.06"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx -->
<g id="node12" class="node">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx</title>
<g id="a_node12"><a xlink:href="app/[locale]/(connected)/games/[gameId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6290.35C114.96,-6290.35 73.29,-6290.35 73.29,-6290.35 70.21,-6290.35 67.12,-6287.27 67.12,-6284.18 67.12,-6284.18 67.12,-6278.02 67.12,-6278.02 67.12,-6274.93 70.21,-6271.85 73.29,-6271.85 73.29,-6271.85 114.96,-6271.85 114.96,-6271.85 118.04,-6271.85 121.12,-6274.93 121.12,-6278.02 121.12,-6278.02 121.12,-6284.18 121.12,-6284.18 121.12,-6287.27 118.04,-6290.35 114.96,-6290.35"/>
<text text-anchor="start" x="76.88" y="-6277.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="node13" class="node">
<title>src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<g id="a_node13"><a xlink:href="src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx" xlink:title="GameDetailsPage.tsx">
<path fill="#bbfeff" stroke="black" d="M760.96,-1268.35C760.96,-1268.35 668.79,-1268.35 668.79,-1268.35 665.71,-1268.35 662.62,-1265.27 662.62,-1262.18 662.62,-1262.18 662.62,-1256.02 662.62,-1256.02 662.62,-1252.93 665.71,-1249.85 668.79,-1249.85 668.79,-1249.85 760.96,-1249.85 760.96,-1249.85 764.04,-1249.85 767.12,-1252.93 767.12,-1256.02 767.12,-1256.02 767.12,-1262.18 767.12,-1262.18 767.12,-1265.27 764.04,-1268.35 760.96,-1268.35"/>
<text text-anchor="start" x="670.62" y="-1255.8" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="edge13" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.5,-6284.1C138.73,-6284.45 160.2,-6281.51 172.25,-6267.1 184.81,-6252.07 173.6,-644.52 180.25,-626.1 255.45,-417.92 442.03,-201.42 599.38,-357.1 616.75,-374.29 593.24,-1218.21 608.75,-1237.1 619.68,-1250.41 636.42,-1256.98 653.34,-1259.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="652.95,-1262.01 659.18,-1260.76 653.53,-1257.85 652.95,-1262.01"/>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx -->
<g id="node37" class="node">
<title>src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx</title>
<g id="a_node37"><a xlink:href="src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx" xlink:title="ShiningCard.tsx">
<path fill="#bbfeff" stroke="black" d="M984.71,-1165.35C984.71,-1165.35 915.79,-1165.35 915.79,-1165.35 912.71,-1165.35 909.62,-1162.27 909.62,-1159.18 909.62,-1159.18 909.62,-1153.02 909.62,-1153.02 909.62,-1149.93 912.71,-1146.85 915.79,-1146.85 915.79,-1146.85 984.71,-1146.85 984.71,-1146.85 987.79,-1146.85 990.88,-1149.93 990.88,-1153.02 990.88,-1153.02 990.88,-1159.18 990.88,-1159.18 990.88,-1162.27 987.79,-1165.35 984.71,-1165.35"/>
<text text-anchor="start" x="917.62" y="-1152.8" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx -->
<g id="edge239" class="edge">
<title>src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M767.62,-1261.07C789.26,-1259.14 813.04,-1252.97 829,-1237.1 841.85,-1224.31 825.2,-1210.86 837,-1197.1 852.92,-1178.54 877.97,-1168.38 900.37,-1162.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="900.79,-1164.88 906.18,-1161.51 899.86,-1160.78 900.79,-1164.88"/>
</g>
<!-- src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="node136" class="node">
<title>src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<g id="a_node136"><a xlink:href="src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx" xlink:title="PlayGameButton.tsx">
<path fill="#bbfeff" stroke="black" d="M993.33,-955.35C993.33,-955.35 907.17,-955.35 907.17,-955.35 904.08,-955.35 901,-952.27 901,-949.18 901,-949.18 901,-943.02 901,-943.02 901,-939.93 904.08,-936.85 907.17,-936.85 907.17,-936.85 993.33,-936.85 993.33,-936.85 996.42,-936.85 999.5,-939.93 999.5,-943.02 999.5,-943.02 999.5,-949.18 999.5,-949.18 999.5,-952.27 996.42,-955.35 993.33,-955.35"/>
<text text-anchor="start" x="909" y="-942.8" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="edge238" class="edge">
<title>src/client/Shared/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M767.42,-1262.35C789.7,-1260.81 814.05,-1254.59 829,-1237.1 845.11,-1218.25 824.44,-1035.48 837,-1014.1 852.81,-987.18 883.85,-969.6 909.29,-959.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="910.04,-961.03 914.85,-956.87 908.5,-957.12 910.04,-961.03"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx -->
<g id="node14" class="node">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx</title>
<g id="a_node14"><a xlink:href="app/[locale]/(connected)/games/[gameId]/play/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6237.35C114.96,-6237.35 73.29,-6237.35 73.29,-6237.35 70.21,-6237.35 67.12,-6234.27 67.12,-6231.18 67.12,-6231.18 67.12,-6225.02 67.12,-6225.02 67.12,-6221.93 70.21,-6218.85 73.29,-6218.85 73.29,-6218.85 114.96,-6218.85 114.96,-6218.85 118.04,-6218.85 121.12,-6221.93 121.12,-6225.02 121.12,-6225.02 121.12,-6231.18 121.12,-6231.18 121.12,-6234.27 118.04,-6237.35 114.96,-6237.35"/>
<text text-anchor="start" x="76.88" y="-6224.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="node15" class="node">
<title>src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<g id="a_node15"><a xlink:href="src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx" xlink:title="PlayGamePage.tsx">
<path fill="#bbfeff" stroke="black" d="M755.71,-1207.35C755.71,-1207.35 674.04,-1207.35 674.04,-1207.35 670.96,-1207.35 667.88,-1204.27 667.88,-1201.18 667.88,-1201.18 667.88,-1195.02 667.88,-1195.02 667.88,-1191.93 670.96,-1188.85 674.04,-1188.85 674.04,-1188.85 755.71,-1188.85 755.71,-1188.85 758.79,-1188.85 761.88,-1191.93 761.88,-1195.02 761.88,-1195.02 761.88,-1201.18 761.88,-1201.18 761.88,-1204.27 758.79,-1207.35 755.71,-1207.35"/>
<text text-anchor="start" x="675.88" y="-1194.8" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGamePage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="edge14" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-6231.48C138.68,-6232.06 160.14,-6229.4 172.25,-6215.1 185.46,-6199.5 166.72,-344.42 180.25,-329.1 303.86,-189.19 462.1,-174.56 599.38,-301.1 608.8,-309.79 607.37,-403.35 608.75,-416.1 641.76,-722 697.77,-1093.09 711.01,-1179.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="708.93,-1179.81 711.92,-1185.42 713.08,-1179.17 708.93,-1179.81"/>
</g>
<!-- src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="node132" class="node">
<title>src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<g id="a_node132"><a xlink:href="src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx" xlink:title="StartGameButton.tsx">
<path fill="#bbfeff" stroke="black" d="M993.71,-2185.35C993.71,-2185.35 906.79,-2185.35 906.79,-2185.35 903.71,-2185.35 900.62,-2182.27 900.62,-2179.18 900.62,-2179.18 900.62,-2173.02 900.62,-2173.02 900.62,-2169.93 903.71,-2166.85 906.79,-2166.85 906.79,-2166.85 993.71,-2166.85 993.71,-2166.85 996.79,-2166.85 999.88,-2169.93 999.88,-2173.02 999.88,-2173.02 999.88,-2179.18 999.88,-2179.18 999.88,-2182.27 996.79,-2185.35 993.71,-2185.35"/>
<text text-anchor="start" x="908.62" y="-2172.8" font-family="Helvetica,sans-Serif" font-size="9.00">StartGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="edge241" class="edge">
<title>src/client/Shared/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M762.26,-1202.54C786.34,-1207.45 813.82,-1217.73 829,-1239.1 839.74,-1254.23 833.25,-1888.93 837,-1907.1 857.73,-2007.5 915.95,-2117.46 939.34,-2158.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="937.48,-2159.95 942.28,-2164.13 941.13,-2157.88 937.48,-2159.95"/>
</g>
<!-- app/[locale]/(connected)/games/page.tsx -->
<g id="node16" class="node">
<title>app/[locale]/(connected)/games/page.tsx</title>
<g id="a_node16"><a xlink:href="app/[locale]/(connected)/games/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6527.35C114.96,-6527.35 73.29,-6527.35 73.29,-6527.35 70.21,-6527.35 67.12,-6524.27 67.12,-6521.18 67.12,-6521.18 67.12,-6515.02 67.12,-6515.02 67.12,-6511.93 70.21,-6508.85 73.29,-6508.85 73.29,-6508.85 114.96,-6508.85 114.96,-6508.85 118.04,-6508.85 121.12,-6511.93 121.12,-6515.02 121.12,-6515.02 121.12,-6521.18 121.12,-6521.18 121.12,-6524.27 118.04,-6527.35 114.96,-6527.35"/>
<text text-anchor="start" x="76.88" y="-6514.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="node17" class="node">
<title>src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<g id="a_node17"><a xlink:href="src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx" xlink:title="GameListPage.tsx">
<path fill="#bbfeff" stroke="black" d="M753.83,-1329.35C753.83,-1329.35 675.92,-1329.35 675.92,-1329.35 672.83,-1329.35 669.75,-1326.27 669.75,-1323.18 669.75,-1323.18 669.75,-1317.02 669.75,-1317.02 669.75,-1313.93 672.83,-1310.85 675.92,-1310.85 675.92,-1310.85 753.83,-1310.85 753.83,-1310.85 756.92,-1310.85 760,-1313.93 760,-1317.02 760,-1317.02 760,-1323.18 760,-1323.18 760,-1326.27 756.92,-1329.35 753.83,-1329.35"/>
<text text-anchor="start" x="677.75" y="-1316.8" font-family="Helvetica,sans-Serif" font-size="9.00">GameListPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="edge15" class="edge">
<title>app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M112.68,-6508.53C131.3,-6497.3 159.71,-6476.75 172.25,-6450.1 184.36,-6424.35 174.8,-6222.03 180.25,-6194.1 283.11,-5667.14 503.99,-5589.46 599.38,-5061.1 615.38,-4972.42 599.25,-1906.71 608.75,-1817.1 628.92,-1626.93 690.34,-1402.35 708.66,-1338.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="710.57,-1339.05 710.21,-1332.7 706.53,-1337.89 710.57,-1339.05"/>
</g>
<!-- src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="node135" class="node">
<title>src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<g id="a_node135"><a xlink:href="src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx" xlink:title="GameDetailsButton.tsx">
<path fill="#bbfeff" stroke="black" d="M998.58,-894.35C998.58,-894.35 901.92,-894.35 901.92,-894.35 898.83,-894.35 895.75,-891.27 895.75,-888.18 895.75,-888.18 895.75,-882.02 895.75,-882.02 895.75,-878.93 898.83,-875.85 901.92,-875.85 901.92,-875.85 998.58,-875.85 998.58,-875.85 1001.67,-875.85 1004.75,-878.93 1004.75,-882.02 1004.75,-882.02 1004.75,-888.18 1004.75,-888.18 1004.75,-891.27 1001.67,-894.35 998.58,-894.35"/>
<text text-anchor="start" x="903.75" y="-881.8" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="edge240" class="edge">
<title>src/client/Shared/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M760.35,-1323.78C784.44,-1323.14 812.56,-1317.61 829,-1298.1 842.32,-1282.29 824.97,-942.91 837,-926.1 848.78,-909.64 867.88,-899.77 887.02,-893.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="887.36,-895.94 892.58,-892.3 886.23,-891.9 887.36,-895.94"/>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx -->
<g id="node18" class="node">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx</title>
<g id="a_node18"><a xlink:href="app/[locale]/(connected)/matches/[matchId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6596.35C114.96,-6596.35 73.29,-6596.35 73.29,-6596.35 70.21,-6596.35 67.12,-6593.27 67.12,-6590.18 67.12,-6590.18 67.12,-6584.02 67.12,-6584.02 67.12,-6580.93 70.21,-6577.85 73.29,-6577.85 73.29,-6577.85 114.96,-6577.85 114.96,-6577.85 118.04,-6577.85 121.12,-6580.93 121.12,-6584.02 121.12,-6584.02 121.12,-6590.18 121.12,-6590.18 121.12,-6593.27 118.04,-6596.35 114.96,-6596.35"/>
<text text-anchor="start" x="76.88" y="-6583.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="node19" class="node">
<title>src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<g id="a_node19"><a xlink:href="src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx" xlink:title="MatchPage.tsx">
<path fill="#bbfeff" stroke="black" d="M746.71,-5599.35C746.71,-5599.35 683.04,-5599.35 683.04,-5599.35 679.96,-5599.35 676.88,-5596.27 676.88,-5593.18 676.88,-5593.18 676.88,-5587.02 676.88,-5587.02 676.88,-5583.93 679.96,-5580.85 683.04,-5580.85 683.04,-5580.85 746.71,-5580.85 746.71,-5580.85 749.79,-5580.85 752.88,-5583.93 752.88,-5587.02 752.88,-5587.02 752.88,-5593.18 752.88,-5593.18 752.88,-5596.27 749.79,-5599.35 746.71,-5599.35"/>
<text text-anchor="start" x="684.88" y="-5586.8" font-family="Helvetica,sans-Serif" font-size="9.00">MatchPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="edge16" class="edge">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.47,-6584.22C138.93,-6580.81 160.67,-6573.37 172.25,-6557.1 185.99,-6537.81 168.71,-6364.78 180.25,-6344.1 289.51,-6148.25 471.11,-6247.07 599.38,-6063.1 704.61,-5912.15 713.66,-5675.7 714.02,-5608.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="716.12,-5608.85 714.02,-5602.85 711.92,-5608.85 716.12,-5608.85"/>
</g>
<!-- src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="node127" class="node">
<title>src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<g id="a_node127"><a xlink:href="src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx" xlink:title="LeaveMatchButton.tsx">
<path fill="#bbfeff" stroke="black" d="M997.08,-5738.35C997.08,-5738.35 903.42,-5738.35 903.42,-5738.35 900.33,-5738.35 897.25,-5735.27 897.25,-5732.18 897.25,-5732.18 897.25,-5726.02 897.25,-5726.02 897.25,-5722.93 900.33,-5719.85 903.42,-5719.85 903.42,-5719.85 997.08,-5719.85 997.08,-5719.85 1000.17,-5719.85 1003.25,-5722.93 1003.25,-5726.02 1003.25,-5726.02 1003.25,-5732.18 1003.25,-5732.18 1003.25,-5735.27 1000.17,-5738.35 997.08,-5738.35"/>
<text text-anchor="start" x="905.25" y="-5725.8" font-family="Helvetica,sans-Serif" font-size="9.00">LeaveMatchButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="edge211" class="edge">
<title>src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M731.92,-5599.65C772.22,-5623.66 877.89,-5686.6 925.45,-5714.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="924.27,-5716.66 930.5,-5717.93 926.42,-5713.06 924.27,-5716.66"/>
</g>
<!-- src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="node128" class="node">
<title>src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<g id="a_node128"><a xlink:href="src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx" xlink:title="Match.tsx">
<path fill="#bbfeff" stroke="black" d="M971.21,-5799.35C971.21,-5799.35 929.29,-5799.35 929.29,-5799.35 926.21,-5799.35 923.12,-5796.27 923.12,-5793.18 923.12,-5793.18 923.12,-5787.02 923.12,-5787.02 923.12,-5783.93 926.21,-5780.85 929.29,-5780.85 929.29,-5780.85 971.21,-5780.85 971.21,-5780.85 974.29,-5780.85 977.38,-5783.93 977.38,-5787.02 977.38,-5787.02 977.38,-5793.18 977.38,-5793.18 977.38,-5796.27 974.29,-5799.35 971.21,-5799.35"/>
<text text-anchor="start" x="931.12" y="-5786.8" font-family="Helvetica,sans-Serif" font-size="9.00">Match.tsx</text>
</a>
</g>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="edge212" class="edge">
<title>src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M719.35,-5599.77C730.83,-5630.14 770.55,-5724.24 837,-5768.1 859.83,-5783.17 890.58,-5788.39 914.12,-5790"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="913.96,-5792.09 920.06,-5790.31 914.18,-5787.9 913.96,-5792.09"/>
</g>
<!-- src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="node129" class="node">
<title>src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<g id="a_node129"><a xlink:href="src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx" xlink:title="MatchConsoleEvents.tsx">
<path fill="#bbfeff" stroke="black" d="M1002.33,-5922.35C1002.33,-5922.35 898.17,-5922.35 898.17,-5922.35 895.08,-5922.35 892,-5919.27 892,-5916.18 892,-5916.18 892,-5910.02 892,-5910.02 892,-5906.93 895.08,-5903.85 898.17,-5903.85 898.17,-5903.85 1002.33,-5903.85 1002.33,-5903.85 1005.42,-5903.85 1008.5,-5906.93 1008.5,-5910.02 1008.5,-5910.02 1008.5,-5916.18 1008.5,-5916.18 1008.5,-5919.27 1005.42,-5922.35 1002.33,-5922.35"/>
<text text-anchor="start" x="900" y="-5909.8" font-family="Helvetica,sans-Serif" font-size="9.00">MatchConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="edge213" class="edge">
<title>src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M718.93,-5599.67C730.19,-5633.82 772.07,-5751.81 837,-5829.1 862.01,-5858.87 899.5,-5884.19 924.17,-5899.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="922.85,-5900.68 929.09,-5901.92 924.99,-5897.07 922.85,-5900.68"/>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="node130" class="node">
<title>src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<g id="a_node130"><a xlink:href="src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx" xlink:title="ErrorLoadingMatchPage.tsx">
<path fill="#bbfeff" stroke="black" d="M1009.08,-5538.35C1009.08,-5538.35 891.42,-5538.35 891.42,-5538.35 888.33,-5538.35 885.25,-5535.27 885.25,-5532.18 885.25,-5532.18 885.25,-5526.02 885.25,-5526.02 885.25,-5522.93 888.33,-5519.85 891.42,-5519.85 891.42,-5519.85 1009.08,-5519.85 1009.08,-5519.85 1012.17,-5519.85 1015.25,-5522.93 1015.25,-5526.02 1015.25,-5526.02 1015.25,-5532.18 1015.25,-5532.18 1015.25,-5535.27 1012.17,-5538.35 1009.08,-5538.35"/>
<text text-anchor="start" x="893.25" y="-5525.8" font-family="Helvetica,sans-Serif" font-size="9.00">ErrorLoadingMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="edge214" class="edge">
<title>src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M753.11,-5580.37C794.01,-5569.68 859.71,-5552.5 903.66,-5541.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="903.94,-5543.11 909.22,-5539.56 902.88,-5539.05 903.94,-5543.11"/>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="node131" class="node">
<title>src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<g id="a_node131"><a xlink:href="src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx" xlink:title="FinishedMatchPage.tsx">
<path fill="#bbfeff" stroke="black" d="M999.71,-5599.35C999.71,-5599.35 900.79,-5599.35 900.79,-5599.35 897.71,-5599.35 894.62,-5596.27 894.62,-5593.18 894.62,-5593.18 894.62,-5587.02 894.62,-5587.02 894.62,-5583.93 897.71,-5580.85 900.79,-5580.85 900.79,-5580.85 999.71,-5580.85 999.71,-5580.85 1002.79,-5580.85 1005.88,-5583.93 1005.88,-5587.02 1005.88,-5587.02 1005.88,-5593.18 1005.88,-5593.18 1005.88,-5596.27 1002.79,-5599.35 999.71,-5599.35"/>
<text text-anchor="start" x="902.62" y="-5586.8" font-family="Helvetica,sans-Serif" font-size="9.00">FinishedMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="edge215" class="edge">
<title>src/client/Gaming/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M753.37,-5590.1C788.98,-5590.1 843.21,-5590.1 885.59,-5590.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="885.41,-5592.2 891.41,-5590.1 885.41,-5588 885.41,-5592.2"/>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx -->
<g id="node20" class="node">
<title>app/[locale]/(not&#45;connected)/layout.tsx</title>
<g id="a_node20"><a xlink:href="app/[locale]/(not-connected)/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-6772.35C115.08,-6772.35 73.17,-6772.35 73.17,-6772.35 70.08,-6772.35 67,-6769.27 67,-6766.18 67,-6766.18 67,-6760.02 67,-6760.02 67,-6756.93 70.08,-6753.85 73.17,-6753.85 73.17,-6753.85 115.08,-6753.85 115.08,-6753.85 118.17,-6753.85 121.25,-6756.93 121.25,-6760.02 121.25,-6760.02 121.25,-6766.18 121.25,-6766.18 121.25,-6769.27 118.17,-6772.35 115.08,-6772.35"/>
<text text-anchor="start" x="75" y="-6759.8" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="node21" class="node">
<title>src/client/Shared/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<g id="a_node21"><a xlink:href="src/client/Shared/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx" xlink:title="FullPageLayout.tsx">
<path fill="#bbfeff" stroke="black" d="M755.71,-1907.35C755.71,-1907.35 674.04,-1907.35 674.04,-1907.35 670.96,-1907.35 667.88,-1904.27 667.88,-1901.18 667.88,-1901.18 667.88,-1895.02 667.88,-1895.02 667.88,-1891.93 670.96,-1888.85 674.04,-1888.85 674.04,-1888.85 755.71,-1888.85 755.71,-1888.85 758.79,-1888.85 761.88,-1891.93 761.88,-1895.02 761.88,-1895.02 761.88,-1901.18 761.88,-1901.18 761.88,-1904.27 758.79,-1907.35 755.71,-1907.35"/>
<text text-anchor="start" x="675.88" y="-1894.8" font-family="Helvetica,sans-Serif" font-size="9.00">FullPageLayout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/Shared/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="edge17" class="edge">
<title>app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/Shared/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.66,-6765.92C138.73,-6766.14 159.96,-6763.12 172.25,-6749.1 184.28,-6735.38 173.78,-6602.16 180.25,-6585.1 285.79,-6306.92 500.13,-6343.59 599.38,-6063.1 611.27,-6029.49 607.51,-3531.73 608.75,-3496.1 631.34,-2844.81 700.69,-2046.52 712.24,-1916.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="714.31,-1916.79 712.75,-1910.63 710.13,-1916.42 714.31,-1916.79"/>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx -->
<g id="node22" class="node">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx</title>
<g id="a_node22"><a xlink:href="app/[locale]/(not-connected)/signin/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6719.35C114.96,-6719.35 73.29,-6719.35 73.29,-6719.35 70.21,-6719.35 67.12,-6716.27 67.12,-6713.18 67.12,-6713.18 67.12,-6707.02 67.12,-6707.02 67.12,-6703.93 70.21,-6700.85 73.29,-6700.85 73.29,-6700.85 114.96,-6700.85 114.96,-6700.85 118.04,-6700.85 121.12,-6703.93 121.12,-6707.02 121.12,-6707.02 121.12,-6713.18 121.12,-6713.18 121.12,-6716.27 118.04,-6719.35 114.96,-6719.35"/>
<text text-anchor="start" x="76.88" y="-6706.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="node23" class="node">
<title>src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<g id="a_node23"><a xlink:href="src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx" xlink:title="SignInPage.tsx">
<path fill="#bbfeff" stroke="black" d="M532.71,-571.35C532.71,-571.35 467.54,-571.35 467.54,-571.35 464.46,-571.35 461.38,-568.27 461.38,-565.18 461.38,-565.18 461.38,-559.02 461.38,-559.02 461.38,-555.93 464.46,-552.85 467.54,-552.85 467.54,-552.85 532.71,-552.85 532.71,-552.85 535.79,-552.85 538.88,-555.93 538.88,-559.02 538.88,-559.02 538.88,-565.18 538.88,-565.18 538.88,-568.27 535.79,-571.35 532.71,-571.35"/>
<text text-anchor="start" x="469.38" y="-558.8" font-family="Helvetica,sans-Serif" font-size="9.00">SignInPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="edge18" class="edge">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M108.97,-6700.36C127.49,-6686.39 159.46,-6658.73 172.25,-6626.1 181.87,-6601.54 179.48,-4753.46 180.25,-4727.1 193.6,-4268.4 140.02,-979.08 403.12,-603.1 414.66,-586.62 434.24,-576.72 452.72,-570.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="453.03,-572.9 458.19,-569.2 451.85,-568.86 453.03,-572.9"/>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="node36" class="node">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<g id="a_node36"><a xlink:href="src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx" xlink:title="SignInForm.tsx">
<path fill="#bbfeff" stroke="black" d="M747.08,-386.35C747.08,-386.35 682.67,-386.35 682.67,-386.35 679.58,-386.35 676.5,-383.27 676.5,-380.18 676.5,-380.18 676.5,-374.02 676.5,-374.02 676.5,-370.93 679.58,-367.85 682.67,-367.85 682.67,-367.85 747.08,-367.85 747.08,-367.85 750.17,-367.85 753.25,-370.93 753.25,-374.02 753.25,-374.02 753.25,-380.18 753.25,-380.18 753.25,-383.27 750.17,-386.35 747.08,-386.35"/>
<text text-anchor="start" x="684.5" y="-373.8" font-family="Helvetica,sans-Serif" font-size="9.00">SignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="edge31" class="edge">
<title>src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M512.26,-552.41C547.02,-522.19 653.66,-429.46 696.14,-392.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="697.41,-394.2 700.56,-388.68 694.66,-391.03 697.41,-394.2"/>
</g>
<!-- src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx -->
<g id="node40" class="node">
<title>src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx</title>
<g id="a_node40"><a xlink:href="src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx" xlink:title="Sparkles.tsx">
<path fill="#bbfeff" stroke="black" d="M976.83,-1383.35C976.83,-1383.35 923.67,-1383.35 923.67,-1383.35 920.58,-1383.35 917.5,-1380.27 917.5,-1377.18 917.5,-1377.18 917.5,-1371.02 917.5,-1371.02 917.5,-1367.93 920.58,-1364.85 923.67,-1364.85 923.67,-1364.85 976.83,-1364.85 976.83,-1364.85 979.92,-1364.85 983,-1367.93 983,-1371.02 983,-1371.02 983,-1377.18 983,-1377.18 983,-1380.27 979.92,-1383.35 976.83,-1383.35"/>
<text text-anchor="start" x="925.5" y="-1370.8" font-family="Helvetica,sans-Serif" font-size="9.00">Sparkles.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx -->
<g id="edge32" class="edge">
<title>src/client/Authentication/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M538.9,-568.43C559.96,-573.98 584.79,-584.24 599.38,-603.1 617.5,-626.54 600.71,-640.58 608.75,-669.1 675.62,-906.35 772.47,-938.18 829,-1178.1 832.75,-1194.02 827.67,-1311.66 837,-1325.1 853.32,-1348.62 883.52,-1360.95 908.49,-1367.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="907.8,-1369.35 914.12,-1368.69 908.77,-1365.27 907.8,-1369.35"/>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx -->
<g id="node24" class="node">
<title>app/[locale]/access&#45;denied/page.tsx</title>
<g id="a_node24"><a xlink:href="app/[locale]/access-denied/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6833.35C114.96,-6833.35 73.29,-6833.35 73.29,-6833.35 70.21,-6833.35 67.12,-6830.27 67.12,-6827.18 67.12,-6827.18 67.12,-6821.02 67.12,-6821.02 67.12,-6817.93 70.21,-6814.85 73.29,-6814.85 73.29,-6814.85 114.96,-6814.85 114.96,-6814.85 118.04,-6814.85 121.12,-6817.93 121.12,-6821.02 121.12,-6821.02 121.12,-6827.18 121.12,-6827.18 121.12,-6830.27 118.04,-6833.35 114.96,-6833.35"/>
<text text-anchor="start" x="76.88" y="-6820.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="node25" class="node">
<title>src/client/Authentication/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<g id="a_node25"><a xlink:href="src/client/Authentication/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx" xlink:title="AccessDeniedPage.tsx">
<path fill="#bbfeff" stroke="black" d="M549.21,-632.35C549.21,-632.35 451.04,-632.35 451.04,-632.35 447.96,-632.35 444.88,-629.27 444.88,-626.18 444.88,-626.18 444.88,-620.02 444.88,-620.02 444.88,-616.93 447.96,-613.85 451.04,-613.85 451.04,-613.85 549.21,-613.85 549.21,-613.85 552.29,-613.85 555.38,-616.93 555.38,-620.02 555.38,-620.02 555.38,-626.18 555.38,-626.18 555.38,-629.27 552.29,-632.35 549.21,-632.35"/>
<text text-anchor="start" x="452.88" y="-619.8" font-family="Helvetica,sans-Serif" font-size="9.00">AccessDeniedPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/Authentication/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="edge19" class="edge">
<title>app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/Authentication/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.37,-6824.15C138.77,-6822.64 160.5,-6817.48 172.25,-6802.1 183.82,-6786.96 177.39,-6134.94 180.25,-6116.1 230.43,-5785.08 344.97,-5725.12 395.12,-5394.1 406.73,-5317.52 401.77,-4774.54 403.12,-4697.1 433.18,-2981.54 492.45,-860.13 498.6,-641.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="500.7,-641.88 498.77,-635.82 496.5,-641.76 500.7,-641.88"/>
</g>
<!-- app/[locale]/page.tsx -->
<g id="node26" class="node">
<title>app/[locale]/page.tsx</title>
<g id="a_node26"><a xlink:href="app/[locale]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6179.35C114.96,-6179.35 73.29,-6179.35 73.29,-6179.35 70.21,-6179.35 67.12,-6176.27 67.12,-6173.18 67.12,-6173.18 67.12,-6167.02 67.12,-6167.02 67.12,-6163.93 70.21,-6160.85 73.29,-6160.85 73.29,-6160.85 114.96,-6160.85 114.96,-6160.85 118.04,-6160.85 121.12,-6163.93 121.12,-6167.02 121.12,-6167.02 121.12,-6173.18 121.12,-6173.18 121.12,-6176.27 118.04,-6179.35 114.96,-6179.35"/>
<text text-anchor="start" x="76.88" y="-6166.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="node27" class="node">
<title>src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<g id="a_node27"><a xlink:href="src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx" xlink:title="RedirectToGameList.tsx">
<path fill="#bbfeff" stroke="black" d="M1000.46,-1295.35C1000.46,-1295.35 900.04,-1295.35 900.04,-1295.35 896.96,-1295.35 893.88,-1292.27 893.88,-1289.18 893.88,-1289.18 893.88,-1283.02 893.88,-1283.02 893.88,-1279.93 896.96,-1276.85 900.04,-1276.85 900.04,-1276.85 1000.46,-1276.85 1000.46,-1276.85 1003.54,-1276.85 1006.62,-1279.93 1006.62,-1283.02 1006.62,-1283.02 1006.62,-1289.18 1006.62,-1289.18 1006.62,-1292.27 1003.54,-1295.35 1000.46,-1295.35"/>
<text text-anchor="start" x="901.88" y="-1282.8" font-family="Helvetica,sans-Serif" font-size="9.00">RedirectToGameList.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/page.tsx&#45;&gt;src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge20" class="edge">
<title>app/[locale]/page.tsx&#45;&gt;src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-6173.48C138.68,-6174.06 160.14,-6171.4 172.25,-6157.1 185.59,-6141.35 165.68,-226.72 180.25,-212.1 282.08,-109.95 731.35,-125.95 829,-232.1 848.26,-253.03 819.59,-1233.61 837,-1256.1 848.56,-1271.04 866.69,-1279.06 885.03,-1283.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="884.19,-1285.21 890.48,-1284.3 885,-1281.08 884.19,-1285.21"/>
</g>
<!-- app/globals.css -->
<g id="node28" class="node">
<title>app/globals.css</title>
<g id="a_node28"><a xlink:href="app/globals.css" xlink:title="globals.css">
<path fill="#ffffcc" stroke="black" d="M339.21,-6913.35C339.21,-6913.35 289.79,-6913.35 289.79,-6913.35 286.71,-6913.35 283.62,-6910.27 283.62,-6907.18 283.62,-6907.18 283.62,-6901.02 283.62,-6901.02 283.62,-6897.93 286.71,-6894.85 289.79,-6894.85 289.79,-6894.85 339.21,-6894.85 339.21,-6894.85 342.29,-6894.85 345.38,-6897.93 345.38,-6901.02 345.38,-6901.02 345.38,-6907.18 345.38,-6907.18 345.38,-6910.27 342.29,-6913.35 339.21,-6913.35"/>
<text text-anchor="start" x="291.62" y="-6900.8" font-family="Helvetica,sans-Serif" font-size="9.00">globals.css</text>
</a>
</g>
</g>
<!-- app/layout.tsx -->
<g id="node29" class="node">
<title>app/layout.tsx</title>
<g id="a_node29"><a xlink:href="app/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-6913.35C115.08,-6913.35 73.17,-6913.35 73.17,-6913.35 70.08,-6913.35 67,-6910.27 67,-6907.18 67,-6907.18 67,-6901.02 67,-6901.02 67,-6897.93 70.08,-6894.85 73.17,-6894.85 73.17,-6894.85 115.08,-6894.85 115.08,-6894.85 118.17,-6894.85 121.25,-6897.93 121.25,-6901.02 121.25,-6901.02 121.25,-6907.18 121.25,-6907.18 121.25,-6910.27 118.17,-6913.35 115.08,-6913.35"/>
<text text-anchor="start" x="75" y="-6900.8" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;app/globals.css -->
<g id="edge21" class="edge">
<title>app/layout.tsx&#45;&gt;app/globals.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.47,-6904.1C159.49,-6904.1 230.02,-6904.1 274.21,-6904.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="274.19,-6906.2 280.19,-6904.1 274.19,-6902 274.19,-6906.2"/>
</g>
<!-- src/client/Shared/infrastructure/components/Background/Background.tsx -->
<g id="node30" class="node">
<title>src/client/Shared/infrastructure/components/Background/Background.tsx</title>
<g id="a_node30"><a xlink:href="src/client/Shared/infrastructure/components/Background/Background.tsx" xlink:title="Background.tsx">
<path fill="#bbfeff" stroke="black" d="M983.58,-825.35C983.58,-825.35 916.92,-825.35 916.92,-825.35 913.83,-825.35 910.75,-822.27 910.75,-819.18 910.75,-819.18 910.75,-813.02 910.75,-813.02 910.75,-809.93 913.83,-806.85 916.92,-806.85 916.92,-806.85 983.58,-806.85 983.58,-806.85 986.67,-806.85 989.75,-809.93 989.75,-813.02 989.75,-813.02 989.75,-819.18 989.75,-819.18 989.75,-822.27 986.67,-825.35 983.58,-825.35"/>
<text text-anchor="start" x="918.75" y="-812.8" font-family="Helvetica,sans-Serif" font-size="9.00">Background.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/Shared/infrastructure/components/Background/Background.tsx -->
<g id="edge22" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/Shared/infrastructure/components/Background/Background.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-6897.67C139.24,-6891.83 161.28,-6881.28 172.25,-6863.1 179.91,-6850.4 179.84,-4741.93 180.25,-4727.1 186.9,-4484.1 239.92,-537.26 403.12,-357.1 530.54,-216.45 679.67,-208.98 829,-326.1 906.08,-386.55 940.26,-715.42 947.67,-797.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="945.57,-797.66 948.19,-803.45 949.76,-797.29 945.57,-797.66"/>
</g>
<!-- src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="node31" class="node">
<title>src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<g id="a_node31"><a xlink:href="src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx" xlink:title="RootLayout.tsx">
<path fill="#bbfeff" stroke="black" d="M747.08,-1846.35C747.08,-1846.35 682.67,-1846.35 682.67,-1846.35 679.58,-1846.35 676.5,-1843.27 676.5,-1840.18 676.5,-1840.18 676.5,-1834.02 676.5,-1834.02 676.5,-1830.93 679.58,-1827.85 682.67,-1827.85 682.67,-1827.85 747.08,-1827.85 747.08,-1827.85 750.17,-1827.85 753.25,-1830.93 753.25,-1834.02 753.25,-1834.02 753.25,-1840.18 753.25,-1840.18 753.25,-1843.27 750.17,-1846.35 747.08,-1846.35"/>
<text text-anchor="start" x="684.5" y="-1833.8" font-family="Helvetica,sans-Serif" font-size="9.00">RootLayout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="edge23" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.54,-6894.67C137.72,-6887.86 157.98,-6877.35 172.25,-6863.1 457.51,-6578.35 501.09,-6453.99 599.38,-6063.1 606.46,-6034.91 592.33,-1902.08 608.75,-1878.1 621.98,-1858.78 645.78,-1848.56 667.39,-1843.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.81,-1845.22 673.21,-1841.86 666.89,-1841.12 667.81,-1845.22"/>
</g>
<!-- src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ToastProvider.tsx -->
<g id="edge236" class="edge">
<title>src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M753.63,-1842.11C793.1,-1847.36 855.24,-1855.61 898.87,-1861.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="898.38,-1863.46 904.61,-1862.17 898.94,-1859.3 898.38,-1863.46"/>
</g>
<!-- src/client/Shared/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="node143" class="node">
<title>src/client/Shared/infrastructure/providers/ConvexClientProvider.tsx</title>
<g id="a_node143"><a xlink:href="src/client/Shared/infrastructure/providers/ConvexClientProvider.tsx" xlink:title="ConvexClientProvider.tsx">
<path fill="#bbfeff" stroke="black" d="M1003.46,-1846.35C1003.46,-1846.35 897.04,-1846.35 897.04,-1846.35 893.96,-1846.35 890.88,-1843.27 890.88,-1840.18 890.88,-1840.18 890.88,-1834.02 890.88,-1834.02 890.88,-1830.93 893.96,-1827.85 897.04,-1827.85 897.04,-1827.85 1003.46,-1827.85 1003.46,-1827.85 1006.54,-1827.85 1009.62,-1830.93 1009.62,-1834.02 1009.62,-1834.02 1009.62,-1840.18 1009.62,-1840.18 1009.62,-1843.27 1006.54,-1846.35 1003.46,-1846.35"/>
<text text-anchor="start" x="898.88" y="-1833.8" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexClientProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="edge235" class="edge">
<title>src/client/Shared/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/Shared/infrastructure/providers/ConvexClientProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M753.63,-1837.1C788.12,-1837.1 839.94,-1837.1 881.58,-1837.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="881.54,-1839.2 887.54,-1837.1 881.54,-1835 881.54,-1839.2"/>
</g>
<!-- app/page.tsx -->
<g id="node32" class="node">
<title>app/page.tsx</title>
<g id="a_node32"><a xlink:href="app/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6145.35C114.96,-6145.35 73.29,-6145.35 73.29,-6145.35 70.21,-6145.35 67.12,-6142.27 67.12,-6139.18 67.12,-6139.18 67.12,-6133.02 67.12,-6133.02 67.12,-6129.93 70.21,-6126.85 73.29,-6126.85 73.29,-6126.85 114.96,-6126.85 114.96,-6126.85 118.04,-6126.85 121.12,-6129.93 121.12,-6133.02 121.12,-6133.02 121.12,-6139.18 121.12,-6139.18 121.12,-6142.27 118.04,-6145.35 114.96,-6145.35"/>
<text text-anchor="start" x="76.88" y="-6132.8" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/page.tsx&#45;&gt;src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge24" class="edge">
<title>app/page.tsx&#45;&gt;src/client/Shared/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M95.17,-6126.49C96.36,-5861.56 122.45,-209.09 180.25,-152.1 385.58,50.33 624.38,51.04 829,-152.1 850.76,-173.71 818.24,-1231.84 837,-1256.1 848.56,-1271.04 866.69,-1279.07 885.03,-1283.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="884.18,-1285.21 890.47,-1284.31 884.99,-1281.09 884.18,-1285.21"/>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="node33" class="node">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<g id="a_node33"><a xlink:href="src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx" xlink:title="SignInButton.tsx">
<path fill="#bbfeff" stroke="black" d="M534.96,-386.35C534.96,-386.35 465.29,-386.35 465.29,-386.35 462.21,-386.35 459.12,-383.27 459.12,-380.18 459.12,-380.18 459.12,-374.02 459.12,-374.02 459.12,-370.93 462.21,-367.85 465.29,-367.85 465.29,-367.85 534.96,-367.85 534.96,-367.85 538.04,-367.85 541.12,-370.93 541.12,-374.02 541.12,-374.02 541.12,-380.18 541.12,-380.18 541.12,-383.27 538.04,-386.35 534.96,-386.35"/>
<text text-anchor="start" x="467.12" y="-373.8" font-family="Helvetica,sans-Serif" font-size="9.00">SignInButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="node34" class="node">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<g id="a_node34"><a xlink:href="src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx" xlink:title="useSignInForm.tsx">
<path fill="#bbfeff" stroke="black" d="M754.58,-355.35C754.58,-355.35 675.17,-355.35 675.17,-355.35 672.08,-355.35 669,-352.27 669,-349.18 669,-349.18 669,-343.02 669,-343.02 669,-339.93 672.08,-336.85 675.17,-336.85 675.17,-336.85 754.58,-336.85 754.58,-336.85 757.67,-336.85 760.75,-339.93 760.75,-343.02 760.75,-343.02 760.75,-349.18 760.75,-349.18 760.75,-352.27 757.67,-355.35 754.58,-355.35"/>
<text text-anchor="start" x="677" y="-342.8" font-family="Helvetica,sans-Serif" font-size="9.00">useSignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="edge25" class="edge">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.39,-371.23C574.96,-366.34 623.08,-359.33 659.97,-353.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="659.98,-356.07 665.62,-353.13 659.38,-351.92 659.98,-356.07"/>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx -->
<g id="node35" class="node">
<title>src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx</title>
<g id="a_node35"><a xlink:href="src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx" xlink:title="ShiningButton.tsx">
<path fill="#bbfeff" stroke="black" d="M987.71,-1104.35C987.71,-1104.35 912.79,-1104.35 912.79,-1104.35 909.71,-1104.35 906.62,-1101.27 906.62,-1098.18 906.62,-1098.18 906.62,-1092.02 906.62,-1092.02 906.62,-1088.93 909.71,-1085.85 912.79,-1085.85 912.79,-1085.85 987.71,-1085.85 987.71,-1085.85 990.79,-1085.85 993.88,-1088.93 993.88,-1092.02 993.88,-1092.02 993.88,-1098.18 993.88,-1098.18 993.88,-1101.27 990.79,-1104.35 987.71,-1104.35"/>
<text text-anchor="start" x="914.62" y="-1091.8" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx -->
<g id="edge26" class="edge">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M511.27,-386.61C558.57,-431.73 758.17,-630.77 829,-841.1 837.23,-865.54 820.27,-1053.47 837,-1073.1 851.66,-1090.3 875.48,-1096.56 897.4,-1098.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="897.21,-1100.29 903.31,-1098.48 897.42,-1096.1 897.21,-1100.29"/>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="edge29" class="edge">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M761.19,-344.67C783,-345.87 808.74,-350.18 829,-362.1 977.89,-449.71 1007.8,-508.57 1063.5,-672.1 1071.48,-695.53 1056.34,-1543.53 1071.5,-1563.1 1081.49,-1576 1098.16,-1581.86 1113.89,-1584.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1113.26,-1586.39 1119.47,-1585.05 1113.77,-1582.23 1113.26,-1586.39"/>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge30" class="edge">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M761.01,-340C784.7,-339.51 812.22,-343.71 829,-362.1 841.15,-375.41 830.45,-507.31 837,-524.1 894.16,-670.66 1009.31,-648.41 1063.5,-796.1 1072.08,-819.49 1056.24,-1673.4 1071.5,-1693.1 1081.02,-1705.39 1096.61,-1711.29 1111.66,-1713.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1111.18,-1716.03 1117.41,-1714.79 1111.77,-1711.88 1111.18,-1716.03"/>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.css -->
<g id="node138" class="node">
<title>src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.css</title>
<g id="a_node138"><a xlink:href="src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.css" xlink:title="ShiningButton.css">
<path fill="#ffffcc" stroke="black" d="M1193.21,-1104.35C1193.21,-1104.35 1116.04,-1104.35 1116.04,-1104.35 1112.96,-1104.35 1109.88,-1101.27 1109.88,-1098.18 1109.88,-1098.18 1109.88,-1092.02 1109.88,-1092.02 1109.88,-1088.93 1112.96,-1085.85 1116.04,-1085.85 1116.04,-1085.85 1193.21,-1085.85 1193.21,-1085.85 1196.29,-1085.85 1199.38,-1088.93 1199.38,-1092.02 1199.38,-1092.02 1199.38,-1098.18 1199.38,-1098.18 1199.38,-1101.27 1196.29,-1104.35 1193.21,-1104.35"/>
<text text-anchor="start" x="1117.88" y="-1091.8" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.css</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.css -->
<g id="edge229" class="edge">
<title>src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M994.17,-1095.1C1025.38,-1095.1 1067.8,-1095.1 1101.04,-1095.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1100.69,-1097.2 1106.69,-1095.1 1100.69,-1093 1100.69,-1097.2"/>
</g>
<!-- src/client/Shared/helpers/Tailwind/utils.ts -->
<g id="node139" class="node">
<title>src/client/Shared/helpers/Tailwind/utils.ts</title>
<g id="a_node139"><a xlink:href="src/client/Shared/helpers/Tailwind/utils.ts" xlink:title="utils.ts">
<path fill="#ddfeff" stroke="black" d="M1175.46,-1471.35C1175.46,-1471.35 1133.79,-1471.35 1133.79,-1471.35 1130.71,-1471.35 1127.62,-1468.27 1127.62,-1465.18 1127.62,-1465.18 1127.62,-1459.02 1127.62,-1459.02 1127.62,-1455.93 1130.71,-1452.85 1133.79,-1452.85 1133.79,-1452.85 1175.46,-1452.85 1175.46,-1452.85 1178.54,-1452.85 1181.62,-1455.93 1181.62,-1459.02 1181.62,-1459.02 1181.62,-1465.18 1181.62,-1465.18 1181.62,-1468.27 1178.54,-1471.35 1175.46,-1471.35"/>
<text text-anchor="start" x="1141.88" y="-1458.8" font-family="Helvetica,sans-Serif" font-size="9.00">utils.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/Shared/helpers/Tailwind/utils.ts -->
<g id="edge230" class="edge">
<title>src/client/Shared/infrastructure/components/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/Shared/helpers/Tailwind/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M994.33,-1099.97C1018.35,-1105 1046.72,-1115.25 1063.5,-1136.1 1071.8,-1146.41 1068.66,-1182.18 1071.5,-1195.1 1092.53,-1290.65 1131.41,-1401.42 1146.95,-1444.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1144.98,-1444.8 1149.02,-1449.71 1148.93,-1443.36 1144.98,-1444.8"/>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="edge27" class="edge">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M676.27,-377.1C641.21,-377.1 588.71,-377.1 550.33,-377.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="550.42,-375 544.42,-377.1 550.42,-379.2 550.42,-375"/>
</g>
<!-- src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx -->
<g id="edge28" class="edge">
<title>src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M719.04,-386.75C734.7,-434.98 804.06,-654.96 829,-841.1 831.16,-857.24 826.48,-1121.67 837,-1134.1 852.29,-1152.17 877.67,-1158.18 900.43,-1159.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="900.24,-1161.54 906.31,-1159.63 900.37,-1157.34 900.24,-1161.54"/>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/Shared/helpers/Tailwind/utils.ts -->
<g id="edge232" class="edge">
<title>src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/Shared/helpers/Tailwind/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M991.27,-1161.86C1015.11,-1167.23 1044.35,-1177.52 1063.5,-1197.1 1133.28,-1268.46 1149.1,-1395.89 1152.63,-1443.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.53,-1443.52 1153.02,-1449.37 1154.72,-1443.24 1150.53,-1443.52"/>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.css -->
<g id="node140" class="node">
<title>src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.css</title>
<g id="a_node140"><a xlink:href="src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.css" xlink:title="ShiningCard.css">
<path fill="#ffffcc" stroke="black" d="M1190.21,-1165.35C1190.21,-1165.35 1119.04,-1165.35 1119.04,-1165.35 1115.96,-1165.35 1112.88,-1162.27 1112.88,-1159.18 1112.88,-1159.18 1112.88,-1153.02 1112.88,-1153.02 1112.88,-1149.93 1115.96,-1146.85 1119.04,-1146.85 1119.04,-1146.85 1190.21,-1146.85 1190.21,-1146.85 1193.29,-1146.85 1196.38,-1149.93 1196.38,-1153.02 1196.38,-1153.02 1196.38,-1159.18 1196.38,-1159.18 1196.38,-1162.27 1193.29,-1165.35 1190.21,-1165.35"/>
<text text-anchor="start" x="1120.88" y="-1152.8" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.css</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.css -->
<g id="edge231" class="edge">
<title>src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M991.23,-1156.1C1023.48,-1156.1 1069.02,-1156.1 1103.67,-1156.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1103.66,-1158.2 1109.66,-1156.1 1103.66,-1154 1103.66,-1158.2"/>
</g>
<!-- src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx&#45;&gt;src/client/Shared/helpers/Tailwind/utils.ts -->
<g id="edge233" class="edge">
<title>src/client/Shared/infrastructure/components/Sparkles/Sparkles.tsx&#45;&gt;src/client/Shared/helpers/Tailwind/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M973.29,-1383.68C1009.62,-1399.48 1082.12,-1431 1123.26,-1448.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1122.19,-1450.72 1128.53,-1451.19 1123.86,-1446.87 1122.19,-1450.72"/>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="node41" class="node">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts</title>
<g id="a_node41"><a xlink:href="src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts" xlink:title="addCardToDeck.ts">
<path fill="#ddfeff" stroke="black" d="M989.96,-2928.35C989.96,-2928.35 910.54,-2928.35 910.54,-2928.35 907.46,-2928.35 904.38,-2925.27 904.38,-2922.18 904.38,-2922.18 904.38,-2916.02 904.38,-2916.02 904.38,-2912.93 907.46,-2909.85 910.54,-2909.85 910.54,-2909.85 989.96,-2909.85 989.96,-2909.85 993.04,-2909.85 996.12,-2912.93 996.12,-2916.02 996.12,-2916.02 996.12,-2922.18 996.12,-2922.18 996.12,-2925.27 993.04,-2928.35 989.96,-2928.35"/>
<text text-anchor="start" x="912.38" y="-2915.8" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge33" class="edge">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.37,-2923.51C1020.44,-2928.4 1048.18,-2938.66 1063.5,-2960.1 1076.39,-2978.14 1057.83,-3341.65 1071.5,-3359.1 1076.83,-3365.9 1084.01,-3370.74 1091.91,-3374.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1090.83,-3376 1097.18,-3376.09 1092.27,-3372.05 1090.83,-3376"/>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="node43" class="node">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<g id="a_node43"><a xlink:href="src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest.ts" xlink:title="addCardToDeckRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1211.58,-2928.35C1211.58,-2928.35 1097.67,-2928.35 1097.67,-2928.35 1094.58,-2928.35 1091.5,-2925.27 1091.5,-2922.18 1091.5,-2922.18 1091.5,-2916.02 1091.5,-2916.02 1091.5,-2912.93 1094.58,-2909.85 1097.67,-2909.85 1097.67,-2909.85 1211.58,-2909.85 1211.58,-2909.85 1214.67,-2909.85 1217.75,-2912.93 1217.75,-2916.02 1217.75,-2916.02 1217.75,-2922.18 1217.75,-2922.18 1217.75,-2925.27 1214.67,-2928.35 1211.58,-2928.35"/>
<text text-anchor="start" x="1099.5" y="-2915.8" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="edge34" class="edge">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.42,-2919.1C1021.75,-2919.1 1053.93,-2919.1 1082.57,-2919.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1082.31,-2921.2 1088.31,-2919.1 1082.31,-2917 1082.31,-2921.2"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts -->
<g id="node44" class="node">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts</title>
<g id="a_node44"><a xlink:href="src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts" xlink:title="DeckBuilder.ts">
<path fill="#ddfeff" stroke="black" d="M1185.71,-4885.35C1185.71,-4885.35 1123.54,-4885.35 1123.54,-4885.35 1120.46,-4885.35 1117.38,-4882.27 1117.38,-4879.18 1117.38,-4879.18 1117.38,-4873.02 1117.38,-4873.02 1117.38,-4869.93 1120.46,-4866.85 1123.54,-4866.85 1123.54,-4866.85 1185.71,-4866.85 1185.71,-4866.85 1188.79,-4866.85 1191.88,-4869.93 1191.88,-4873.02 1191.88,-4873.02 1191.88,-4879.18 1191.88,-4879.18 1191.88,-4882.27 1188.79,-4885.35 1185.71,-4885.35"/>
<text text-anchor="start" x="1125.38" y="-4872.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge35" class="edge">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.49,-2923.42C1020.61,-2928.28 1048.35,-2938.54 1063.5,-2960.1 1078.62,-2981.62 1055.44,-4833.26 1071.5,-4854.1 1080.31,-4865.52 1094.35,-4871.43 1108.36,-4874.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.78,-4876.4 1114.04,-4875.33 1108.48,-4872.26 1107.78,-4876.4"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="node45" class="node">
<title>src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<g id="a_node45"><a xlink:href="src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts" xlink:title="deckBuilderEvents.ts">
<path fill="#ddfeff" stroke="black" d="M1522.08,-4916.35C1522.08,-4916.35 1432.92,-4916.35 1432.92,-4916.35 1429.83,-4916.35 1426.75,-4913.27 1426.75,-4910.18 1426.75,-4910.18 1426.75,-4904.02 1426.75,-4904.02 1426.75,-4900.93 1429.83,-4897.85 1432.92,-4897.85 1432.92,-4897.85 1522.08,-4897.85 1522.08,-4897.85 1525.17,-4897.85 1528.25,-4900.93 1528.25,-4904.02 1528.25,-4904.02 1528.25,-4910.18 1528.25,-4910.18 1528.25,-4913.27 1525.17,-4916.35 1522.08,-4916.35"/>
<text text-anchor="start" x="1434.75" y="-4903.8" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge36" class="edge">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.5,-2923.41C1020.61,-2928.28 1048.36,-2938.54 1063.5,-2960.1 1079.77,-2983.26 1051.45,-4978.12 1071.5,-4998.1 1084.92,-5011.46 1395.56,-5006.82 1412.38,-4998.1 1442.08,-4982.69 1460.94,-4946.5 1470.03,-4924.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1471.87,-4925.75 1472.12,-4919.39 1467.96,-4924.2 1471.87,-4925.75"/>
</g>
<!-- src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge37" class="edge">
<title>src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.51,-2922.65C1020.17,-2921.83 1047.46,-2916.17 1063.5,-2897.1 1076.77,-2881.32 1058.18,-2541.83 1071.5,-2526.1 1120.86,-2467.8 1186.61,-2548.37 1240.38,-2494.1 1305.83,-2428.02 1321.75,-2118.25 1324.76,-2039.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.86,-2039.68 1324.97,-2033.61 1322.66,-2039.53 1326.86,-2039.68"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge91" class="edge">
<title>src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1209.07,-3377.92C1221.1,-3374.59 1232.59,-3368.83 1240.38,-3359.1 1248.95,-3348.38 1247.67,-3125.81 1248.38,-3112.1 1270.78,-2676.72 1314.93,-2144.75 1323.8,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.88,-2039.98 1324.29,-2033.83 1321.69,-2039.63 1325.88,-2039.98"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge122" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1192.25,-4878.21C1207.79,-4880.3 1225.68,-4884.34 1240.38,-4892.1 1244.82,-4894.45 1243.76,-4898.1 1248.38,-4900.1 1302.19,-4923.36 1370.01,-4922.07 1417.66,-4916.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1417.89,-4918.93 1423.61,-4916.14 1417.4,-4914.76 1417.89,-4918.93"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/CatalogCard.ts -->
<g id="node57" class="node">
<title>src/client/DeckBuilding/domain/Catalog/CatalogCard.ts</title>
<g id="a_node57"><a xlink:href="src/client/DeckBuilding/domain/Catalog/CatalogCard.ts" xlink:title="CatalogCard.ts">
<path fill="#ddfeff" stroke="black" d="M1639.08,-4749.35C1639.08,-4749.35 1573.92,-4749.35 1573.92,-4749.35 1570.83,-4749.35 1567.75,-4746.27 1567.75,-4743.18 1567.75,-4743.18 1567.75,-4737.02 1567.75,-4737.02 1567.75,-4733.93 1570.83,-4730.85 1573.92,-4730.85 1573.92,-4730.85 1639.08,-4730.85 1639.08,-4730.85 1642.17,-4730.85 1645.25,-4733.93 1645.25,-4737.02 1645.25,-4737.02 1645.25,-4743.18 1645.25,-4743.18 1645.25,-4746.27 1642.17,-4749.35 1639.08,-4749.35"/>
<text text-anchor="start" x="1575.75" y="-4736.8" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogCard.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts -->
<g id="edge121" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1192.3,-4875.7C1209.4,-4873.5 1228.54,-4867.78 1240.38,-4854.1 1251.05,-4841.76 1236.71,-4792.51 1248.38,-4781.1 1296,-4734.53 1481.34,-4766.3 1547,-4755.1 1552.29,-4754.2 1557.8,-4753.06 1563.24,-4751.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1563.39,-4753.93 1568.73,-4750.49 1562.41,-4749.85 1563.39,-4753.93"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="node100" class="node">
<title>src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts</title>
<g id="a_node100"><a xlink:href="src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts" xlink:title="deckBuilderReducer.ts">
<path fill="#ddfeff" stroke="black" d="M1374.33,-4894.35C1374.33,-4894.35 1278.42,-4894.35 1278.42,-4894.35 1275.33,-4894.35 1272.25,-4891.27 1272.25,-4888.18 1272.25,-4888.18 1272.25,-4882.02 1272.25,-4882.02 1272.25,-4878.93 1275.33,-4875.85 1278.42,-4875.85 1278.42,-4875.85 1374.33,-4875.85 1374.33,-4875.85 1377.42,-4875.85 1380.5,-4878.93 1380.5,-4882.02 1380.5,-4882.02 1380.5,-4888.18 1380.5,-4888.18 1380.5,-4891.27 1377.42,-4894.35 1374.33,-4894.35"/>
<text text-anchor="start" x="1280.25" y="-4881.8" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge123" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1192.04,-4878.03C1212.69,-4879.13 1239.08,-4880.52 1262.94,-4881.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1262.81,-4883.89 1268.91,-4882.11 1263.03,-4879.69 1262.81,-4883.89"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="node104" class="node">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts</title>
<g id="a_node104"><a xlink:href="src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts" xlink:title="DeckBuilderCard.ts">
<path fill="#ddfeff" stroke="black" d="M1648.08,-4916.35C1648.08,-4916.35 1564.92,-4916.35 1564.92,-4916.35 1561.83,-4916.35 1558.75,-4913.27 1558.75,-4910.18 1558.75,-4910.18 1558.75,-4904.02 1558.75,-4904.02 1558.75,-4900.93 1561.83,-4897.85 1564.92,-4897.85 1564.92,-4897.85 1648.08,-4897.85 1648.08,-4897.85 1651.17,-4897.85 1654.25,-4900.93 1654.25,-4904.02 1654.25,-4904.02 1654.25,-4910.18 1654.25,-4910.18 1654.25,-4913.27 1651.17,-4916.35 1648.08,-4916.35"/>
<text text-anchor="start" x="1566.75" y="-4903.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCard.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge125" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1528.69,-4907.1C1535.62,-4907.1 1542.76,-4907.1 1549.77,-4907.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1549.54,-4909.2 1555.54,-4907.1 1549.54,-4905 1549.54,-4909.2"/>
</g>
<!-- src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="node95" class="node">
<title>src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts</title>
<g id="a_node95"><a xlink:href="src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts" xlink:title="DeckDraftService.ts">
<path fill="#ddfeff" stroke="black" d="M1368.33,-4523.35C1368.33,-4523.35 1284.42,-4523.35 1284.42,-4523.35 1281.33,-4523.35 1278.25,-4520.27 1278.25,-4517.18 1278.25,-4517.18 1278.25,-4511.02 1278.25,-4511.02 1278.25,-4507.93 1281.33,-4504.85 1284.42,-4504.85 1284.42,-4504.85 1368.33,-4504.85 1368.33,-4504.85 1371.42,-4504.85 1374.5,-4507.93 1374.5,-4511.02 1374.5,-4511.02 1374.5,-4517.18 1374.5,-4517.18 1374.5,-4520.27 1371.42,-4523.35 1368.33,-4523.35"/>
<text text-anchor="start" x="1286.25" y="-4510.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge219" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1325.42,-2030.36C1317.4,-2108.41 1261.29,-2664.83 1244.38,-3118.1 1243.68,-3136.6 1239.01,-4433.38 1244.38,-4451.1 1251.53,-4474.72 1274.64,-4491.14 1294.51,-4501.28"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1293.42,-4503.08 1299.74,-4503.82 1295.26,-4499.31 1293.42,-4503.08"/>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge223" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1325.42,-2030.36C1317.4,-2108.41 1261.29,-2664.83 1244.38,-3118.1 1243.48,-3142.11 1237.41,-4825.1 1244.38,-4848.1 1247.55,-4858.6 1255.16,-4866.12 1264.52,-4871.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1263.34,-4873.27 1269.65,-4874.05 1265.2,-4869.5 1263.34,-4873.27"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts -->
<g id="node101" class="node">
<title>src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts</title>
<g id="a_node101"><a xlink:href="src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts" xlink:title="catalogFiltersReducer.ts">
<path fill="#ddfeff" stroke="black" d="M1528.46,-4677.35C1528.46,-4677.35 1426.54,-4677.35 1426.54,-4677.35 1423.46,-4677.35 1420.38,-4674.27 1420.38,-4671.18 1420.38,-4671.18 1420.38,-4665.02 1420.38,-4665.02 1420.38,-4661.93 1423.46,-4658.85 1426.54,-4658.85 1426.54,-4658.85 1528.46,-4658.85 1528.46,-4658.85 1531.54,-4658.85 1534.62,-4661.93 1534.62,-4665.02 1534.62,-4665.02 1534.62,-4671.18 1534.62,-4671.18 1534.62,-4674.27 1531.54,-4677.35 1528.46,-4677.35"/>
<text text-anchor="start" x="1428.38" y="-4664.8" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFiltersReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge220" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1328.4,-2030.8C1336.75,-2110.26 1393.71,-2662.23 1412.38,-3112.1 1412.8,-3122.36 1413.82,-4524.22 1425.31,-4649.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1423.23,-4649.82 1426.3,-4655.38 1427.37,-4649.12 1423.23,-4649.82"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogReducer.ts -->
<g id="node102" class="node">
<title>src/client/DeckBuilding/domain/Catalog/catalogReducer.ts</title>
<g id="a_node102"><a xlink:href="src/client/DeckBuilding/domain/Catalog/catalogReducer.ts" xlink:title="catalogReducer.ts">
<path fill="#ddfeff" stroke="black" d="M1364.96,-4727.35C1364.96,-4727.35 1287.79,-4727.35 1287.79,-4727.35 1284.71,-4727.35 1281.62,-4724.27 1281.62,-4721.18 1281.62,-4721.18 1281.62,-4715.02 1281.62,-4715.02 1281.62,-4711.93 1284.71,-4708.85 1287.79,-4708.85 1287.79,-4708.85 1364.96,-4708.85 1364.96,-4708.85 1368.04,-4708.85 1371.12,-4711.93 1371.12,-4715.02 1371.12,-4715.02 1371.12,-4721.18 1371.12,-4721.18 1371.12,-4724.27 1368.04,-4727.35 1364.96,-4727.35"/>
<text text-anchor="start" x="1289.62" y="-4714.8" font-family="Helvetica,sans-Serif" font-size="9.00">catalogReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogReducer.ts -->
<g id="edge221" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1325.42,-2030.36C1317.4,-2108.41 1261.29,-2664.83 1244.38,-3118.1 1243.57,-3139.74 1238.1,-4656.38 1244.38,-4677.1 1248.54,-4690.85 1259.96,-4700 1273,-4706.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1272.14,-4708 1278.49,-4708.34 1273.74,-4704.12 1272.14,-4708"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts -->
<g id="node103" class="node">
<title>src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts</title>
<g id="a_node103"><a xlink:href="src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts" xlink:title="catalogSearchReducer.ts">
<path fill="#ddfeff" stroke="black" d="M1379.58,-4646.35C1379.58,-4646.35 1273.17,-4646.35 1273.17,-4646.35 1270.08,-4646.35 1267,-4643.27 1267,-4640.18 1267,-4640.18 1267,-4634.02 1267,-4634.02 1267,-4630.93 1270.08,-4627.85 1273.17,-4627.85 1273.17,-4627.85 1379.58,-4627.85 1379.58,-4627.85 1382.67,-4627.85 1385.75,-4630.93 1385.75,-4634.02 1385.75,-4634.02 1385.75,-4640.18 1385.75,-4640.18 1385.75,-4643.27 1382.67,-4646.35 1379.58,-4646.35"/>
<text text-anchor="start" x="1275" y="-4633.8" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts -->
<g id="edge222" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1325.42,-2030.36C1317.4,-2108.41 1261.29,-2664.83 1244.38,-3118.1 1243.6,-3138.84 1238.36,-4592.24 1244.38,-4612.1 1246.78,-4620.05 1252.04,-4625.72 1258.78,-4629.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1257.57,-4631.48 1263.89,-4632.16 1259.37,-4627.69 1257.57,-4631.48"/>
</g>
<!-- src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts -->
<g id="node106" class="node">
<title>src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts</title>
<g id="a_node106"><a xlink:href="src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts" xlink:title="gameSettingsReducer.ts">
<path fill="#ddfeff" stroke="black" d="M1206.33,-4810.35C1206.33,-4810.35 1102.92,-4810.35 1102.92,-4810.35 1099.83,-4810.35 1096.75,-4807.27 1096.75,-4804.18 1096.75,-4804.18 1096.75,-4798.02 1096.75,-4798.02 1096.75,-4794.93 1099.83,-4791.85 1102.92,-4791.85 1102.92,-4791.85 1206.33,-4791.85 1206.33,-4791.85 1209.42,-4791.85 1212.5,-4794.93 1212.5,-4798.02 1212.5,-4798.02 1212.5,-4804.18 1212.5,-4804.18 1212.5,-4807.27 1209.42,-4810.35 1206.33,-4810.35"/>
<text text-anchor="start" x="1104.75" y="-4797.8" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts -->
<g id="edge224" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1324.45,-2030.81C1316.92,-2110.28 1265.56,-2662.41 1248.38,-3112.1 1245.52,-3186.77 1249.6,-4382.95 1240.38,-4457.1 1224.54,-4584.38 1178.27,-4732.54 1161.66,-4783.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1159.76,-4782.18 1159.86,-4788.53 1163.74,-4783.5 1159.76,-4782.18"/>
</g>
<!-- src/client/Shared/application/services/LocationService.ts -->
<g id="node134" class="node">
<title>src/client/Shared/application/services/LocationService.ts</title>
<g id="a_node134"><a xlink:href="src/client/Shared/application/services/LocationService.ts" xlink:title="LocationService.ts">
<path fill="#ddfeff" stroke="black" d="M1516.83,-2030.35C1516.83,-2030.35 1438.17,-2030.35 1438.17,-2030.35 1435.08,-2030.35 1432,-2027.27 1432,-2024.18 1432,-2024.18 1432,-2018.02 1432,-2018.02 1432,-2014.93 1435.08,-2011.85 1438.17,-2011.85 1438.17,-2011.85 1516.83,-2011.85 1516.83,-2011.85 1519.92,-2011.85 1523,-2014.93 1523,-2018.02 1523,-2018.02 1523,-2024.18 1523,-2024.18 1523,-2027.27 1519.92,-2030.35 1516.83,-2030.35"/>
<text text-anchor="start" x="1440" y="-2017.8" font-family="Helvetica,sans-Serif" font-size="9.00">LocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/Shared/application/services/LocationService.ts -->
<g id="edge218" class="edge">
<title>src/client/Shared/application/store/appStore.ts&#45;&gt;src/client/Shared/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1357.91,-2021.1C1376.98,-2021.1 1402.02,-2021.1 1424.18,-2021.1"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1424.06,-2023.2 1430.06,-2021.1 1424.06,-2019 1424.06,-2023.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge38" class="edge">
<title>src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M993.55,-2556.59C1017.77,-2556.09 1046.61,-2550.75 1063.5,-2531.1 1077.06,-2515.33 1063.72,-2363.39 1071.5,-2344.1 1113.88,-2239.01 1171.98,-2243.44 1240.38,-2153.1 1270.09,-2113.86 1300.18,-2064.29 1315.33,-2038.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.14,-2039.57 1318.34,-2033.33 1313.51,-2037.45 1317.14,-2039.57"/>
</g>
<!-- src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts -->
<g id="node48" class="node">
<title>src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts</title>
<g id="a_node48"><a xlink:href="src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts" xlink:title="filterCatalog.ts">
<path fill="#ddfeff" stroke="black" d="M1185.71,-3141.35C1185.71,-3141.35 1123.54,-3141.35 1123.54,-3141.35 1120.46,-3141.35 1117.38,-3138.27 1117.38,-3135.18 1117.38,-3135.18 1117.38,-3129.02 1117.38,-3129.02 1117.38,-3125.93 1120.46,-3122.85 1123.54,-3122.85 1123.54,-3122.85 1185.71,-3122.85 1185.71,-3122.85 1188.79,-3122.85 1191.88,-3125.93 1191.88,-3129.02 1191.88,-3129.02 1191.88,-3135.18 1191.88,-3135.18 1191.88,-3138.27 1188.79,-3141.35 1185.71,-3141.35"/>
<text text-anchor="start" x="1125.38" y="-3128.8" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalog.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge41" class="edge">
<title>src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1184.27,-3122.43C1203.58,-3114.32 1227.74,-3100.7 1240.38,-3080.1 1297.07,-2987.69 1321.32,-2171.76 1324.9,-2039.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.99,-2039.69 1325.05,-2033.63 1322.79,-2039.57 1326.99,-2039.69"/>
</g>
<!-- src/client/DeckBuilding/application/commands/filterCatalog/filterCatalogRequest.ts -->
<g id="node49" class="node">
<title>src/client/DeckBuilding/application/commands/filterCatalog/filterCatalogRequest.ts</title>
<g id="a_node49"><a xlink:href="src/client/DeckBuilding/application/commands/filterCatalog/filterCatalogRequest.ts" xlink:title="filterCatalogRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1374.71,-3141.35C1374.71,-3141.35 1278.04,-3141.35 1278.04,-3141.35 1274.96,-3141.35 1271.88,-3138.27 1271.88,-3135.18 1271.88,-3135.18 1271.88,-3129.02 1271.88,-3129.02 1271.88,-3125.93 1274.96,-3122.85 1278.04,-3122.85 1278.04,-3122.85 1374.71,-3122.85 1374.71,-3122.85 1377.79,-3122.85 1380.88,-3125.93 1380.88,-3129.02 1380.88,-3129.02 1380.88,-3135.18 1380.88,-3135.18 1380.88,-3138.27 1377.79,-3141.35 1374.71,-3141.35"/>
<text text-anchor="start" x="1279.88" y="-3128.8" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalogRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/DeckBuilding/application/commands/filterCatalog/filterCatalogRequest.ts -->
<g id="edge39" class="edge">
<title>src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/DeckBuilding/application/commands/filterCatalog/filterCatalogRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1192.04,-3132.1C1212.6,-3132.1 1238.86,-3132.1 1262.64,-3132.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1262.58,-3134.2 1268.58,-3132.1 1262.58,-3130 1262.58,-3134.2"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts -->
<g id="node50" class="node">
<title>src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts</title>
<g id="a_node50"><a xlink:href="src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts" xlink:title="CatalogFilters.ts">
<path fill="#ddfeff" stroke="black" d="M1361.21,-4677.35C1361.21,-4677.35 1291.54,-4677.35 1291.54,-4677.35 1288.46,-4677.35 1285.38,-4674.27 1285.38,-4671.18 1285.38,-4671.18 1285.38,-4665.02 1285.38,-4665.02 1285.38,-4661.93 1288.46,-4658.85 1291.54,-4658.85 1291.54,-4658.85 1361.21,-4658.85 1361.21,-4658.85 1364.29,-4658.85 1367.38,-4661.93 1367.38,-4665.02 1367.38,-4665.02 1367.38,-4671.18 1367.38,-4671.18 1367.38,-4674.27 1364.29,-4677.35 1361.21,-4677.35"/>
<text text-anchor="start" x="1293.38" y="-4664.8" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts -->
<g id="edge40" class="edge">
<title>src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1182.33,-3141.69C1202.28,-3150.43 1228.12,-3165.46 1240.38,-3188.1 1250.06,-3205.98 1235.5,-4636.36 1248.38,-4652.1 1255.33,-4660.61 1265.53,-4665.34 1276.23,-4667.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1275.62,-4669.87 1281.9,-4668.86 1276.37,-4665.73 1275.62,-4669.87"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts -->
<g id="node75" class="node">
<title>src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts</title>
<g id="a_node75"><a xlink:href="src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts" xlink:title="catalogFilterEvents.ts">
<path fill="#ddfeff" stroke="black" d="M1651.83,-4695.35C1651.83,-4695.35 1561.17,-4695.35 1561.17,-4695.35 1558.08,-4695.35 1555,-4692.27 1555,-4689.18 1555,-4689.18 1555,-4683.02 1555,-4683.02 1555,-4679.93 1558.08,-4676.85 1561.17,-4676.85 1561.17,-4676.85 1651.83,-4676.85 1651.83,-4676.85 1654.92,-4676.85 1658,-4679.93 1658,-4683.02 1658,-4683.02 1658,-4689.18 1658,-4689.18 1658,-4692.27 1654.92,-4695.35 1651.83,-4695.35"/>
<text text-anchor="start" x="1563" y="-4682.8" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFilterEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge111" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1367.5,-4675.86C1383.84,-4678.69 1402.94,-4681.56 1420.38,-4683.1 1462.14,-4686.79 1509.41,-4687.5 1545.73,-4687.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1545.71,-4689.4 1551.69,-4687.26 1545.68,-4685.2 1545.71,-4689.4"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge113" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1365.16,-4677.83C1381.97,-4681.85 1402.06,-4686.23 1420.38,-4689.1 1511.67,-4703.41 1620.75,-4706.97 1675.59,-4707.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1675.49,-4709.94 1681.52,-4707.92 1675.55,-4705.74 1675.49,-4709.94"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge112" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1367.73,-4668.1C1381.14,-4668.1 1396.44,-4668.1 1411.24,-4668.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1410.85,-4670.2 1416.85,-4668.1 1410.85,-4666 1410.85,-4670.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="node51" class="node">
<title>src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts</title>
<g id="a_node51"><a xlink:href="src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts" xlink:title="hideCardDetails.ts">
<path fill="#ddfeff" stroke="black" d="M989.96,-3172.35C989.96,-3172.35 910.54,-3172.35 910.54,-3172.35 907.46,-3172.35 904.38,-3169.27 904.38,-3166.18 904.38,-3166.18 904.38,-3160.02 904.38,-3160.02 904.38,-3156.93 907.46,-3153.85 910.54,-3153.85 910.54,-3153.85 989.96,-3153.85 989.96,-3153.85 993.04,-3153.85 996.12,-3156.93 996.12,-3160.02 996.12,-3160.02 996.12,-3166.18 996.12,-3166.18 996.12,-3169.27 993.04,-3172.35 989.96,-3172.35"/>
<text text-anchor="start" x="912.38" y="-3159.8" font-family="Helvetica,sans-Serif" font-size="9.00">hideCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge42" class="edge">
<title>src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.49,-3167.42C1020.6,-3172.29 1048.35,-3182.54 1063.5,-3204.1 1076.68,-3222.85 1057.5,-4835.95 1071.5,-4854.1 1080.31,-4865.52 1094.36,-4871.43 1108.37,-4874.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.78,-4876.4 1114.05,-4875.33 1108.48,-4872.26 1107.78,-4876.4"/>
</g>
<!-- src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge43" class="edge">
<title>src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.49,-3167.42C1020.61,-3172.28 1048.35,-3182.54 1063.5,-3204.1 1077.94,-3224.65 1053.7,-4994.38 1071.5,-5012.1 1084.92,-5025.46 1395.86,-5021.37 1412.38,-5012.1 1445.58,-4993.47 1463.63,-4949.89 1471.53,-4925.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1473.49,-4925.9 1473.21,-4919.55 1469.47,-4924.69 1473.49,-4925.9"/>
</g>
<!-- src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge44" class="edge">
<title>src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.57,-3166.69C1020.24,-3165.89 1047.52,-3160.23 1063.5,-3141.1 1083.37,-3117.3 1050.74,-2606.13 1071.5,-2583.1 1122.11,-2526.96 1185.93,-2615.53 1240.38,-2563.1 1317.59,-2488.74 1324.73,-2126.13 1325.33,-2039.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1327.43,-2039.75 1325.36,-2033.73 1323.23,-2039.72 1327.43,-2039.75"/>
</g>
<!-- src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="node52" class="node">
<title>src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<g id="a_node52"><a xlink:href="src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts" xlink:title="initializeDeckBuilderFromLocation.ts">
<path fill="#ddfeff" stroke="black" d="M1026.71,-3111.35C1026.71,-3111.35 873.79,-3111.35 873.79,-3111.35 870.71,-3111.35 867.62,-3108.27 867.62,-3105.18 867.62,-3105.18 867.62,-3099.02 867.62,-3099.02 867.62,-3095.93 870.71,-3092.85 873.79,-3092.85 873.79,-3092.85 1026.71,-3092.85 1026.71,-3092.85 1029.79,-3092.85 1032.88,-3095.93 1032.88,-3099.02 1032.88,-3099.02 1032.88,-3105.18 1032.88,-3105.18 1032.88,-3108.27 1029.79,-3111.35 1026.71,-3111.35"/>
<text text-anchor="start" x="875.62" y="-3098.8" font-family="Helvetica,sans-Serif" font-size="9.00">initializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge47" class="edge">
<title>src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1033.33,-3099.62C1044.94,-3095.71 1055.6,-3089.54 1063.5,-3080.1 1081.71,-3058.32 1052.54,-2590.23 1071.5,-2569.1 1122.01,-2512.81 1185.93,-2600.6 1240.38,-2548.1 1315.37,-2475.77 1324.2,-2123.92 1325.24,-2039.39"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1327.34,-2039.49 1325.3,-2033.47 1323.14,-2039.44 1327.34,-2039.49"/>
</g>
<!-- src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge45" class="edge">
<title>src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1016.96,-3111.84C1046.82,-3116.27 1081.4,-3121.39 1108.31,-3125.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.89,-3127.44 1114.13,-3126.24 1108.5,-3123.29 1107.89,-3127.44"/>
</g>
<!-- src/client/DeckBuilding/application/commands/search/search.ts -->
<g id="node53" class="node">
<title>src/client/DeckBuilding/application/commands/search/search.ts</title>
<g id="a_node53"><a xlink:href="src/client/DeckBuilding/application/commands/search/search.ts" xlink:title="search.ts">
<path fill="#ddfeff" stroke="black" d="M1175.46,-3217.35C1175.46,-3217.35 1133.79,-3217.35 1133.79,-3217.35 1130.71,-3217.35 1127.62,-3214.27 1127.62,-3211.18 1127.62,-3211.18 1127.62,-3205.02 1127.62,-3205.02 1127.62,-3201.93 1130.71,-3198.85 1133.79,-3198.85 1133.79,-3198.85 1175.46,-3198.85 1175.46,-3198.85 1178.54,-3198.85 1181.62,-3201.93 1181.62,-3205.02 1181.62,-3205.02 1181.62,-3211.18 1181.62,-3211.18 1181.62,-3214.27 1178.54,-3217.35 1175.46,-3217.35"/>
<text text-anchor="start" x="1136.25" y="-3204.8" font-family="Helvetica,sans-Serif" font-size="9.00">search.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/commands/search/search.ts -->
<g id="edge46" class="edge">
<title>src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1008.01,-3111.77C1027.93,-3117.76 1048.94,-3127.46 1063.5,-3143.1 1072.32,-3152.57 1063.04,-3161.31 1071.5,-3171.1 1083.69,-3185.2 1102.34,-3194.19 1118.79,-3199.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1117.9,-3201.7 1124.25,-3201.49 1119.15,-3197.69 1117.9,-3201.7"/>
</g>
<!-- src/client/DeckBuilding/application/commands/search/search.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge70" class="edge">
<title>src/client/DeckBuilding/application/commands/search/search.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1182.02,-3203.98C1201.51,-3199.44 1226.92,-3190.09 1240.38,-3171.1 1248.94,-3159.01 1314.39,-2185.25 1324.16,-2039.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.23,-2039.74 1324.54,-2033.61 1322.04,-2039.46 1326.23,-2039.74"/>
</g>
<!-- src/client/DeckBuilding/application/commands/search/searchRequest.ts -->
<g id="node67" class="node">
<title>src/client/DeckBuilding/application/commands/search/searchRequest.ts</title>
<g id="a_node67"><a xlink:href="src/client/DeckBuilding/application/commands/search/searchRequest.ts" xlink:title="searchRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1363.83,-3217.35C1363.83,-3217.35 1288.92,-3217.35 1288.92,-3217.35 1285.83,-3217.35 1282.75,-3214.27 1282.75,-3211.18 1282.75,-3211.18 1282.75,-3205.02 1282.75,-3205.02 1282.75,-3201.93 1285.83,-3198.85 1288.92,-3198.85 1288.92,-3198.85 1363.83,-3198.85 1363.83,-3198.85 1366.92,-3198.85 1370,-3201.93 1370,-3205.02 1370,-3205.02 1370,-3211.18 1370,-3211.18 1370,-3214.27 1366.92,-3217.35 1363.83,-3217.35"/>
<text text-anchor="start" x="1290.75" y="-3204.8" font-family="Helvetica,sans-Serif" font-size="9.00">searchRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/search/search.ts&#45;&gt;src/client/DeckBuilding/application/commands/search/searchRequest.ts -->
<g id="edge68" class="edge">
<title>src/client/DeckBuilding/application/commands/search/search.ts&#45;&gt;src/client/DeckBuilding/application/commands/search/searchRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1181.78,-3208.1C1206.15,-3208.1 1243.21,-3208.1 1273.57,-3208.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1273.32,-3210.2 1279.32,-3208.1 1273.32,-3206 1273.32,-3210.2"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts -->
<g id="node68" class="node">
<title>src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts</title>
<g id="a_node68"><a xlink:href="src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts" xlink:title="catalogSearchEvents.ts">
<path fill="#ddfeff" stroke="black" d="M1527.33,-4646.35C1527.33,-4646.35 1427.67,-4646.35 1427.67,-4646.35 1424.58,-4646.35 1421.5,-4643.27 1421.5,-4640.18 1421.5,-4640.18 1421.5,-4634.02 1421.5,-4634.02 1421.5,-4630.93 1424.58,-4627.85 1427.67,-4627.85 1427.67,-4627.85 1527.33,-4627.85 1527.33,-4627.85 1530.42,-4627.85 1533.5,-4630.93 1533.5,-4634.02 1533.5,-4634.02 1533.5,-4640.18 1533.5,-4640.18 1533.5,-4643.27 1530.42,-4646.35 1527.33,-4646.35"/>
<text text-anchor="start" x="1429.5" y="-4633.8" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/search/search.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge69" class="edge">
<title>src/client/DeckBuilding/application/commands/search/search.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1169.48,-3217.75C1189.57,-3232.68 1225.97,-3263.53 1240.38,-3300.1 1247.01,-3316.95 1236.25,-4590.65 1248.38,-4604.1 1272.93,-4631.33 1376.24,-4615.91 1412.38,-4622.1 1418.05,-4623.07 1423.98,-4624.24 1429.84,-4625.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1429.07,-4627.48 1435.38,-4626.72 1429.98,-4623.38 1429.07,-4627.48"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="node54" class="node">
<title>src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<g id="a_node54"><a xlink:href="src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts" xlink:title="loadCatalogCards.ts">
<path fill="#ddfeff" stroke="black" d="M994.08,-2623.35C994.08,-2623.35 906.42,-2623.35 906.42,-2623.35 903.33,-2623.35 900.25,-2620.27 900.25,-2617.18 900.25,-2617.18 900.25,-2611.02 900.25,-2611.02 900.25,-2607.93 903.33,-2604.85 906.42,-2604.85 906.42,-2604.85 994.08,-2604.85 994.08,-2604.85 997.17,-2604.85 1000.25,-2607.93 1000.25,-2611.02 1000.25,-2611.02 1000.25,-2617.18 1000.25,-2617.18 1000.25,-2620.27 997.17,-2623.35 994.08,-2623.35"/>
<text text-anchor="start" x="908.25" y="-2610.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge50" class="edge">
<title>src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1000.62,-2617.3C1023.18,-2615.93 1048.22,-2609.87 1063.5,-2592.1 1076.84,-2576.59 1062.86,-2426.65 1071.5,-2408.1 1114.51,-2315.75 1183.72,-2337.77 1240.38,-2253.1 1288.08,-2181.81 1313.12,-2080.34 1321.8,-2039.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1323.81,-2039.95 1322.96,-2033.65 1319.7,-2039.1 1323.81,-2039.95"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="node55" class="node">
<title>src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<g id="a_node55"><a xlink:href="src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts" xlink:title="loadCatalogCardsRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1215.71,-2623.35C1215.71,-2623.35 1093.54,-2623.35 1093.54,-2623.35 1090.46,-2623.35 1087.38,-2620.27 1087.38,-2617.18 1087.38,-2617.18 1087.38,-2611.02 1087.38,-2611.02 1087.38,-2607.93 1090.46,-2604.85 1093.54,-2604.85 1093.54,-2604.85 1215.71,-2604.85 1215.71,-2604.85 1218.79,-2604.85 1221.88,-2607.93 1221.88,-2611.02 1221.88,-2611.02 1221.88,-2617.18 1221.88,-2617.18 1221.88,-2620.27 1218.79,-2623.35 1215.71,-2623.35"/>
<text text-anchor="start" x="1095.38" y="-2610.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCardsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="edge48" class="edge">
<title>src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1000.5,-2614.1C1023.83,-2614.1 1052.23,-2614.1 1078.19,-2614.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1077.87,-2616.2 1083.87,-2614.1 1077.87,-2612 1077.87,-2616.2"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogEvents.ts -->
<g id="node56" class="node">
<title>src/client/DeckBuilding/domain/Catalog/catalogEvents.ts</title>
<g id="a_node56"><a xlink:href="src/client/DeckBuilding/domain/Catalog/catalogEvents.ts" xlink:title="catalogEvents.ts">
<path fill="#ddfeff" stroke="black" d="M1512.71,-4749.35C1512.71,-4749.35 1442.29,-4749.35 1442.29,-4749.35 1439.21,-4749.35 1436.12,-4746.27 1436.12,-4743.18 1436.12,-4743.18 1436.12,-4737.02 1436.12,-4737.02 1436.12,-4733.93 1439.21,-4730.85 1442.29,-4730.85 1442.29,-4730.85 1512.71,-4730.85 1512.71,-4730.85 1515.79,-4730.85 1518.88,-4733.93 1518.88,-4737.02 1518.88,-4737.02 1518.88,-4743.18 1518.88,-4743.18 1518.88,-4746.27 1515.79,-4749.35 1512.71,-4749.35"/>
<text text-anchor="start" x="1444.12" y="-4736.8" font-family="Helvetica,sans-Serif" font-size="9.00">catalogEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogEvents.ts -->
<g id="edge49" class="edge">
<title>src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1000.52,-2619.27C1023.64,-2624.44 1049.2,-2634.72 1063.5,-2655.1 1072.64,-2668.12 1060.23,-4934.87 1071.5,-4946.1 1098.33,-4972.83 1384.95,-4972.22 1412.38,-4946.1 1425.66,-4933.44 1411.67,-4797.26 1420.38,-4781.1 1426.45,-4769.82 1437.15,-4760.84 1447.59,-4754.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1448.37,-4756.17 1452.46,-4751.31 1446.23,-4752.56 1448.37,-4756.17"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts -->
<g id="edge51" class="edge">
<title>src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1196.64,-2623.72C1213.18,-2629.83 1230.62,-2639.61 1240.38,-2655.1 1254.43,-2677.4 1231.92,-4532.5 1248.38,-4553.1 1333.39,-4659.53 1456.94,-4519.9 1547,-4622.1 1558.67,-4635.34 1546.43,-4685.67 1555,-4701.1 1560.51,-4711.01 1569.68,-4719.23 1578.71,-4725.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1577.46,-4727.22 1583.64,-4728.71 1579.74,-4723.69 1577.46,-4727.22"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts -->
<g id="edge114" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1519.03,-4740.1C1531.65,-4740.1 1545.68,-4740.1 1558.69,-4740.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1558.48,-4742.2 1564.48,-4740.1 1558.48,-4738 1558.48,-4742.2"/>
</g>
<!-- src/client/DeckBuilding/domain/CardData/CardData..ts -->
<g id="node78" class="node">
<title>src/client/DeckBuilding/domain/CardData/CardData..ts</title>
<g id="a_node78"><a xlink:href="src/client/DeckBuilding/domain/CardData/CardData..ts" xlink:title="CardData..ts">
<path fill="#ddfeff" stroke="black" d="M1739.08,-4916.35C1739.08,-4916.35 1684.42,-4916.35 1684.42,-4916.35 1681.33,-4916.35 1678.25,-4913.27 1678.25,-4910.18 1678.25,-4910.18 1678.25,-4904.02 1678.25,-4904.02 1678.25,-4900.93 1681.33,-4897.85 1684.42,-4897.85 1684.42,-4897.85 1739.08,-4897.85 1739.08,-4897.85 1742.17,-4897.85 1745.25,-4900.93 1745.25,-4904.02 1745.25,-4904.02 1745.25,-4910.18 1745.25,-4910.18 1745.25,-4913.27 1742.17,-4916.35 1739.08,-4916.35"/>
<text text-anchor="start" x="1686.25" y="-4903.8" font-family="Helvetica,sans-Serif" font-size="9.00">CardData..ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts -->
<g id="edge110" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1613.35,-4749.56C1630.17,-4776.77 1678.87,-4855.53 1700.35,-4890.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1698.43,-4891.17 1703.38,-4895.17 1702.01,-4888.96 1698.43,-4891.17"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge52" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M956.92,-3233.65C972.14,-3258.43 1016.46,-3325.17 1071.5,-3359.1 1077.73,-3362.94 1084.63,-3366.12 1091.7,-3368.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1090.84,-3370.67 1097.19,-3370.61 1092.19,-3366.7 1090.84,-3370.67"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge53" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M951.7,-3233.61C958.35,-3372.81 1036.44,-4993.3 1071.5,-5026.1 1182.14,-5129.6 1282.3,-5103.77 1412.38,-5026.1 1449.15,-5004.14 1466.08,-4953.02 1472.77,-4925.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1474.79,-4926.06 1474.06,-4919.75 1470.69,-4925.14 1474.79,-4926.06"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge54" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M992.37,-3227.79C1016.95,-3227.53 1046.58,-3222.38 1063.5,-3202.1 1085.14,-3176.16 1048.68,-2619 1071.5,-2594.1 1096.98,-2566.29 1213.03,-2603.08 1240.38,-2577.1 1320.31,-2501.17 1325.36,-2126.64 1325.44,-2039.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1327.54,-2039.56 1325.44,-2033.56 1323.34,-2039.57 1327.54,-2039.56"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge56" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.41,-2986.35C1026.37,-2991.83 1049.84,-3002.05 1063.5,-3021.1 1085.39,-3051.63 1048.28,-3329.57 1071.5,-3359.1 1076.84,-3365.89 1084.02,-3370.72 1091.94,-3374.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1090.85,-3375.98 1097.21,-3376.07 1092.29,-3372.04 1090.85,-3375.98"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge57" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.29,-2986.16C1026.37,-2991.59 1049.97,-3001.83 1063.5,-3021.1 1079.39,-3043.73 1051.91,-4992.59 1071.5,-5012.1 1125.17,-5065.56 1346.31,-5049.16 1412.38,-5012.1 1445.58,-4993.47 1463.63,-4949.89 1471.53,-4925.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1473.49,-4925.9 1473.21,-4919.55 1469.47,-4924.69 1473.49,-4925.9"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge58" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.27,-2983.22C1025.93,-2981.48 1049.2,-2975.14 1063.5,-2958.1 1078.4,-2940.35 1056.3,-2558.59 1071.5,-2541.1 1121.37,-2483.74 1186.3,-2567.52 1240.38,-2514.1 1309.48,-2445.84 1322.71,-2120.51 1324.96,-2039.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1327.05,-2039.68 1325.11,-2033.63 1322.85,-2039.57 1327.05,-2039.68"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts -->
<g id="node60" class="node">
<title>src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts</title>
<g id="a_node60"><a xlink:href="src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts" xlink:title="loadDeckIntoBuilderRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1219.83,-2989.35C1219.83,-2989.35 1089.42,-2989.35 1089.42,-2989.35 1086.33,-2989.35 1083.25,-2986.27 1083.25,-2983.18 1083.25,-2983.18 1083.25,-2977.02 1083.25,-2977.02 1083.25,-2973.93 1086.33,-2970.85 1089.42,-2970.85 1089.42,-2970.85 1219.83,-2970.85 1219.83,-2970.85 1222.92,-2970.85 1226,-2973.93 1226,-2977.02 1226,-2977.02 1226,-2983.18 1226,-2983.18 1226,-2986.27 1222.92,-2989.35 1219.83,-2989.35"/>
<text text-anchor="start" x="1091.25" y="-2976.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilderRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts -->
<g id="edge55" class="edge">
<title>src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.42,-2980.1C1025.71,-2980.1 1050.69,-2980.1 1074.05,-2980.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1073.89,-2982.2 1079.89,-2980.1 1073.89,-2978 1073.89,-2982.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="node61" class="node">
<title>src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts</title>
<g id="a_node61"><a xlink:href="src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts" xlink:title="loadGameSettings.ts">
<path fill="#ddfeff" stroke="black" d="M994.46,-2745.35C994.46,-2745.35 906.04,-2745.35 906.04,-2745.35 902.96,-2745.35 899.88,-2742.27 899.88,-2739.18 899.88,-2739.18 899.88,-2733.02 899.88,-2733.02 899.88,-2729.93 902.96,-2726.85 906.04,-2726.85 906.04,-2726.85 994.46,-2726.85 994.46,-2726.85 997.54,-2726.85 1000.62,-2729.93 1000.62,-2733.02 1000.62,-2733.02 1000.62,-2739.18 1000.62,-2739.18 1000.62,-2742.27 997.54,-2745.35 994.46,-2745.35"/>
<text text-anchor="start" x="907.88" y="-2732.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge61" class="edge">
<title>src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1000.98,-2739.35C1023.49,-2737.96 1048.37,-2731.87 1063.5,-2714.1 1080.87,-2693.7 1056.69,-2495.43 1071.5,-2473.1 1117.2,-2404.2 1188.34,-2459.34 1240.38,-2395.1 1286.11,-2338.63 1315.65,-2106.58 1323.36,-2039.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.41,-2039.93 1324,-2033.73 1321.24,-2039.46 1325.41,-2039.93"/>
</g>
<!-- src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="node62" class="node">
<title>src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<g id="a_node62"><a xlink:href="src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettingsRequest.ts" xlink:title="loadGameSettingsRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1216.08,-2745.35C1216.08,-2745.35 1093.17,-2745.35 1093.17,-2745.35 1090.08,-2745.35 1087,-2742.27 1087,-2739.18 1087,-2739.18 1087,-2733.02 1087,-2733.02 1087,-2729.93 1090.08,-2726.85 1093.17,-2726.85 1093.17,-2726.85 1216.08,-2726.85 1216.08,-2726.85 1219.17,-2726.85 1222.25,-2729.93 1222.25,-2733.02 1222.25,-2733.02 1222.25,-2739.18 1222.25,-2739.18 1222.25,-2742.27 1219.17,-2745.35 1216.08,-2745.35"/>
<text text-anchor="start" x="1095" y="-2732.8" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettingsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="edge59" class="edge">
<title>src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1001.02,-2736.1C1024.15,-2736.1 1052.17,-2736.1 1077.86,-2736.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1077.81,-2738.2 1083.81,-2736.1 1077.81,-2734 1077.81,-2738.2"/>
</g>
<!-- src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts -->
<g id="node63" class="node">
<title>src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts</title>
<g id="a_node63"><a xlink:href="src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts" xlink:title="gameSettingsEvents.ts">
<path fill="#ddfeff" stroke="black" d="M1374.71,-4824.35C1374.71,-4824.35 1278.04,-4824.35 1278.04,-4824.35 1274.96,-4824.35 1271.88,-4821.27 1271.88,-4818.18 1271.88,-4818.18 1271.88,-4812.02 1271.88,-4812.02 1271.88,-4808.93 1274.96,-4805.85 1278.04,-4805.85 1278.04,-4805.85 1374.71,-4805.85 1374.71,-4805.85 1377.79,-4805.85 1380.88,-4808.93 1380.88,-4812.02 1380.88,-4812.02 1380.88,-4818.18 1380.88,-4818.18 1380.88,-4821.27 1377.79,-4824.35 1374.71,-4824.35"/>
<text text-anchor="start" x="1279.88" y="-4811.8" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge60" class="edge">
<title>src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1001.1,-2741.4C1024.06,-2746.61 1049.31,-2756.89 1063.5,-2777.1 1080.81,-2801.75 1050.16,-4924.84 1071.5,-4946.1 1098.09,-4972.59 1212.69,-4971.43 1240.38,-4946.1 1255.19,-4932.55 1237.29,-4872.84 1248.38,-4856.1 1256.62,-4843.66 1269.92,-4834.72 1283.18,-4828.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1283.94,-4830.39 1288.6,-4826.06 1282.26,-4826.54 1283.94,-4830.39"/>
</g>
<!-- src/client/DeckBuilding/domain/GameSettings/GameSettings.ts -->
<g id="node105" class="node">
<title>src/client/DeckBuilding/domain/GameSettings/GameSettings.ts</title>
<g id="a_node105"><a xlink:href="src/client/DeckBuilding/domain/GameSettings/GameSettings.ts" xlink:title="GameSettings.ts">
<path fill="#ddfeff" stroke="black" d="M1512.71,-4817.35C1512.71,-4817.35 1442.29,-4817.35 1442.29,-4817.35 1439.21,-4817.35 1436.12,-4814.27 1436.12,-4811.18 1436.12,-4811.18 1436.12,-4805.02 1436.12,-4805.02 1436.12,-4801.93 1439.21,-4798.85 1442.29,-4798.85 1442.29,-4798.85 1512.71,-4798.85 1512.71,-4798.85 1515.79,-4798.85 1518.88,-4801.93 1518.88,-4805.02 1518.88,-4805.02 1518.88,-4811.18 1518.88,-4811.18 1518.88,-4814.27 1515.79,-4817.35 1512.71,-4817.35"/>
<text text-anchor="start" x="1444.12" y="-4804.8" font-family="Helvetica,sans-Serif" font-size="9.00">GameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/GameSettings.ts -->
<g id="edge128" class="edge">
<title>src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1381.21,-4812.57C1396.14,-4811.87 1412.26,-4811.12 1426.93,-4810.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1426.93,-4812.53 1432.83,-4810.15 1426.73,-4808.33 1426.93,-4812.53"/>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="node64" class="node">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<g id="a_node64"><a xlink:href="src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts" xlink:title="removeCardFromDeck.ts">
<path fill="#ddfeff" stroke="black" d="M1003.08,-3050.35C1003.08,-3050.35 897.42,-3050.35 897.42,-3050.35 894.33,-3050.35 891.25,-3047.27 891.25,-3044.18 891.25,-3044.18 891.25,-3038.02 891.25,-3038.02 891.25,-3034.93 894.33,-3031.85 897.42,-3031.85 897.42,-3031.85 1003.08,-3031.85 1003.08,-3031.85 1006.17,-3031.85 1009.25,-3034.93 1009.25,-3038.02 1009.25,-3038.02 1009.25,-3044.18 1009.25,-3044.18 1009.25,-3047.27 1006.17,-3050.35 1003.08,-3050.35"/>
<text text-anchor="start" x="899.25" y="-3037.8" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge62" class="edge">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1009.63,-3048.79C1029.99,-3054.55 1050.88,-3064.6 1063.5,-3082.1 1081.51,-3107.07 1052.4,-3334.95 1071.5,-3359.1 1076.86,-3365.87 1084.05,-3370.7 1091.97,-3374.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1090.88,-3375.96 1097.24,-3376.04 1092.32,-3372.01 1090.88,-3375.96"/>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge64" class="edge">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1009.56,-3048.54C1030.04,-3054.27 1051.03,-3064.36 1063.5,-3082.1 1077.65,-3102.24 1056.47,-4834.61 1071.5,-4854.1 1080.31,-4865.52 1094.36,-4871.43 1108.36,-4874.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.78,-4876.4 1114.05,-4875.33 1108.48,-4872.26 1107.78,-4876.4"/>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge65" class="edge">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1009.56,-3048.54C1030.05,-3054.27 1051.04,-3064.36 1063.5,-3082.1 1079.24,-3104.51 1052.09,-5034.77 1071.5,-5054.1 1125.17,-5107.55 1349,-5095.6 1412.38,-5054.1 1456.71,-5025.07 1470.56,-4957.96 1474.76,-4925.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1476.84,-4925.74 1475.43,-4919.54 1472.66,-4925.26 1476.84,-4925.74"/>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge66" class="edge">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1009.53,-3043.72C1029.63,-3041.4 1050.37,-3034.78 1063.5,-3019.1 1080.05,-2999.34 1054.38,-2574.37 1071.5,-2555.1 1121.81,-2498.47 1186.03,-2584.87 1240.38,-2532.1 1312.76,-2461.81 1323.56,-2122.18 1325.12,-2039.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1327.21,-2039.61 1325.22,-2033.58 1323.01,-2039.54 1327.21,-2039.61"/>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="node65" class="node">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<g id="a_node65"><a xlink:href="src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts" xlink:title="removeCardFromDeckRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1224.71,-3050.35C1224.71,-3050.35 1084.54,-3050.35 1084.54,-3050.35 1081.46,-3050.35 1078.38,-3047.27 1078.38,-3044.18 1078.38,-3044.18 1078.38,-3038.02 1078.38,-3038.02 1078.38,-3034.93 1081.46,-3031.85 1084.54,-3031.85 1084.54,-3031.85 1224.71,-3031.85 1224.71,-3031.85 1227.79,-3031.85 1230.88,-3034.93 1230.88,-3038.02 1230.88,-3038.02 1230.88,-3044.18 1230.88,-3044.18 1230.88,-3047.27 1227.79,-3050.35 1224.71,-3050.35"/>
<text text-anchor="start" x="1086.38" y="-3037.8" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="edge63" class="edge">
<title>src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1009.51,-3041.1C1028.13,-3041.1 1049.12,-3041.1 1069.21,-3041.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1069.03,-3043.2 1075.03,-3041.1 1069.03,-3039 1069.03,-3043.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="node66" class="node">
<title>src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<g id="a_node66"><a xlink:href="src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts" xlink:title="saveDeckDraft.ts">
<path fill="#ddfeff" stroke="black" d="M986.58,-2501.35C986.58,-2501.35 913.92,-2501.35 913.92,-2501.35 910.83,-2501.35 907.75,-2498.27 907.75,-2495.18 907.75,-2495.18 907.75,-2489.02 907.75,-2489.02 907.75,-2485.93 910.83,-2482.85 913.92,-2482.85 913.92,-2482.85 986.58,-2482.85 986.58,-2482.85 989.67,-2482.85 992.75,-2485.93 992.75,-2489.02 992.75,-2489.02 992.75,-2495.18 992.75,-2495.18 992.75,-2498.27 989.67,-2501.35 986.58,-2501.35"/>
<text text-anchor="start" x="915.75" y="-2488.8" font-family="Helvetica,sans-Serif" font-size="9.00">saveDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge67" class="edge">
<title>src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M993.18,-2491.28C1017.73,-2488.39 1047.06,-2480.28 1063.5,-2459.1 1079.11,-2438.98 1060.23,-2252.93 1071.5,-2230.1 1120.41,-2131.01 1240.5,-2062.28 1296.16,-2034.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1297.02,-2036.57 1301.49,-2032.05 1295.18,-2032.79 1297.02,-2036.57"/>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts -->
<g id="node69" class="node">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts</title>
<g id="a_node69"><a xlink:href="src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts" xlink:title="showCardDetails.ts">
<path fill="#ddfeff" stroke="black" d="M991.83,-2867.35C991.83,-2867.35 908.67,-2867.35 908.67,-2867.35 905.58,-2867.35 902.5,-2864.27 902.5,-2861.18 902.5,-2861.18 902.5,-2855.02 902.5,-2855.02 902.5,-2851.93 905.58,-2848.85 908.67,-2848.85 908.67,-2848.85 991.83,-2848.85 991.83,-2848.85 994.92,-2848.85 998,-2851.93 998,-2855.02 998,-2855.02 998,-2861.18 998,-2861.18 998,-2864.27 994.92,-2867.35 991.83,-2867.35"/>
<text text-anchor="start" x="910.5" y="-2854.8" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge72" class="edge">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.39,-2862.91C1021.99,-2867.96 1048.63,-2878.23 1063.5,-2899.1 1078.33,-2919.92 1055.76,-3338.96 1071.5,-3359.1 1076.82,-3365.9 1083.99,-3370.75 1091.9,-3374.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1090.81,-3376.01 1097.16,-3376.1 1092.25,-3372.06 1090.81,-3376.01"/>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge73" class="edge">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.5,-2862.83C1022.13,-2867.85 1048.77,-2878.13 1063.5,-2899.1 1079.11,-2921.32 1054.93,-4832.59 1071.5,-4854.1 1080.3,-4865.52 1094.35,-4871.43 1108.36,-4874.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.78,-4876.4 1114.04,-4875.33 1108.48,-4872.26 1107.78,-4876.4"/>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge74" class="edge">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.5,-2862.83C1022.13,-2867.85 1048.77,-2878.13 1063.5,-2899.1 1080.03,-2922.64 1051.12,-4949.8 1071.5,-4970.1 1098.33,-4996.83 1377.32,-4984.44 1412.38,-4970.1 1435.01,-4960.84 1453.96,-4939.32 1465.25,-4924.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1466.95,-4925.25 1468.7,-4919.14 1463.52,-4922.83 1466.95,-4925.25"/>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge75" class="edge">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.48,-2861.55C1021.65,-2860.48 1047.88,-2854.62 1063.5,-2836.1 1086.87,-2808.41 1048.68,-2538.24 1071.5,-2510.1 1120.08,-2450.19 1187.09,-2525.87 1240.38,-2470.1 1301.21,-2406.43 1320.46,-2115.52 1324.49,-2039.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.57,-2039.95 1324.78,-2033.85 1322.37,-2039.73 1326.57,-2039.95"/>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="node70" class="node">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<g id="a_node70"><a xlink:href="src/client/DeckBuilding/application/commands/showCardDetails/showCardDetailsRequest.ts" xlink:title="showCardDetailsRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1213.46,-2867.35C1213.46,-2867.35 1095.79,-2867.35 1095.79,-2867.35 1092.71,-2867.35 1089.62,-2864.27 1089.62,-2861.18 1089.62,-2861.18 1089.62,-2855.02 1089.62,-2855.02 1089.62,-2851.93 1092.71,-2848.85 1095.79,-2848.85 1095.79,-2848.85 1213.46,-2848.85 1213.46,-2848.85 1216.54,-2848.85 1219.62,-2851.93 1219.62,-2855.02 1219.62,-2855.02 1219.62,-2861.18 1219.62,-2861.18 1219.62,-2864.27 1216.54,-2867.35 1213.46,-2867.35"/>
<text text-anchor="start" x="1097.62" y="-2854.8" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetailsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="edge71" class="edge">
<title>src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/DeckBuilding/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.44,-2858.1C1022.89,-2858.1 1053.29,-2858.1 1080.69,-2858.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1080.44,-2860.2 1086.44,-2858.1 1080.44,-2856 1080.44,-2860.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="node71" class="node">
<title>src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<g id="a_node71"><a xlink:href="src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts" xlink:title="switchDeckBuilderView.ts">
<path fill="#ddfeff" stroke="black" d="M1004.21,-2806.35C1004.21,-2806.35 896.29,-2806.35 896.29,-2806.35 893.21,-2806.35 890.12,-2803.27 890.12,-2800.18 890.12,-2800.18 890.12,-2794.02 890.12,-2794.02 890.12,-2790.93 893.21,-2787.85 896.29,-2787.85 896.29,-2787.85 1004.21,-2787.85 1004.21,-2787.85 1007.29,-2787.85 1010.38,-2790.93 1010.38,-2794.02 1010.38,-2794.02 1010.38,-2800.18 1010.38,-2800.18 1010.38,-2803.27 1007.29,-2806.35 1004.21,-2806.35"/>
<text text-anchor="start" x="898.12" y="-2793.8" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge77" class="edge">
<title>src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1010.74,-2804.87C1030.85,-2810.66 1051.28,-2820.7 1063.5,-2838.1 1079.59,-2861.01 1054.41,-4831.92 1071.5,-4854.1 1080.3,-4865.53 1094.35,-4871.43 1108.36,-4874.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.78,-4876.4 1114.04,-4875.34 1108.48,-4872.26 1107.78,-4876.4"/>
</g>
<!-- src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge78" class="edge">
<title>src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1010.74,-2804.87C1030.85,-2810.66 1051.28,-2820.7 1063.5,-2838.1 1080.52,-2862.33 1050.52,-4949.2 1071.5,-4970.1 1098.33,-4996.83 1377.32,-4984.44 1412.38,-4970.1 1435.01,-4960.84 1453.96,-4939.32 1465.25,-4924.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1466.95,-4925.25 1468.7,-4919.14 1463.52,-4922.83 1466.95,-4925.25"/>
</g>
<!-- src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge79" class="edge">
<title>src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1010.61,-2799.5C1030.33,-2797.06 1050.54,-2790.4 1063.5,-2775.1 1083.76,-2751.18 1052.73,-2518.2 1071.5,-2493.1 1118.78,-2429.89 1187.82,-2497 1240.38,-2438.1 1294.52,-2377.42 1318.4,-2112.73 1324.03,-2039.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.12,-2039.96 1324.47,-2033.82 1321.93,-2039.64 1326.12,-2039.96"/>
</g>
<!-- src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="node72" class="node">
<title>src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<g id="a_node72"><a xlink:href="src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts" xlink:title="switchDeckBuilderViewRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1226.21,-2806.35C1226.21,-2806.35 1083.04,-2806.35 1083.04,-2806.35 1079.96,-2806.35 1076.88,-2803.27 1076.88,-2800.18 1076.88,-2800.18 1076.88,-2794.02 1076.88,-2794.02 1076.88,-2790.93 1079.96,-2787.85 1083.04,-2787.85 1083.04,-2787.85 1226.21,-2787.85 1226.21,-2787.85 1229.29,-2787.85 1232.38,-2790.93 1232.38,-2794.02 1232.38,-2794.02 1232.38,-2800.18 1232.38,-2800.18 1232.38,-2803.27 1229.29,-2806.35 1226.21,-2806.35"/>
<text text-anchor="start" x="1084.88" y="-2793.8" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderViewRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="edge76" class="edge">
<title>src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1010.87,-2797.1C1028.59,-2797.1 1048.36,-2797.1 1067.41,-2797.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1067.37,-2799.2 1073.37,-2797.1 1067.37,-2795 1067.37,-2799.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="node73" class="node">
<title>src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<g id="a_node73"><a xlink:href="src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts" xlink:title="updateAvailableFilters.ts">
<path fill="#ddfeff" stroke="black" d="M1002.33,-2684.35C1002.33,-2684.35 898.17,-2684.35 898.17,-2684.35 895.08,-2684.35 892,-2681.27 892,-2678.18 892,-2678.18 892,-2672.02 892,-2672.02 892,-2668.93 895.08,-2665.85 898.17,-2665.85 898.17,-2665.85 1002.33,-2665.85 1002.33,-2665.85 1005.42,-2665.85 1008.5,-2668.93 1008.5,-2672.02 1008.5,-2672.02 1008.5,-2678.18 1008.5,-2678.18 1008.5,-2681.27 1005.42,-2684.35 1002.33,-2684.35"/>
<text text-anchor="start" x="900" y="-2671.8" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge82" class="edge">
<title>src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1008.77,-2677.64C1029.02,-2675.36 1050.07,-2668.79 1063.5,-2653.1 1078.4,-2635.7 1060.28,-2467.07 1071.5,-2447.1 1115.62,-2368.59 1187.7,-2408.15 1240.38,-2335.1 1309.28,-2239.55 1322.33,-2091.24 1324.8,-2039.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.9,-2039.69 1325.04,-2033.61 1322.7,-2039.52 1326.9,-2039.69"/>
</g>
<!-- src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="node74" class="node">
<title>src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<g id="a_node74"><a xlink:href="src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts" xlink:title="updateAvailableFiltersRequest.ts">
<path fill="#ddfeff" stroke="black" d="M1223.96,-2684.35C1223.96,-2684.35 1085.29,-2684.35 1085.29,-2684.35 1082.21,-2684.35 1079.12,-2681.27 1079.12,-2678.18 1079.12,-2678.18 1079.12,-2672.02 1079.12,-2672.02 1079.12,-2668.93 1082.21,-2665.85 1085.29,-2665.85 1085.29,-2665.85 1223.96,-2665.85 1223.96,-2665.85 1227.04,-2665.85 1230.12,-2668.93 1230.12,-2672.02 1230.12,-2672.02 1230.12,-2678.18 1230.12,-2678.18 1230.12,-2681.27 1227.04,-2684.35 1223.96,-2684.35"/>
<text text-anchor="start" x="1087.12" y="-2671.8" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFiltersRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="edge80" class="edge">
<title>src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1008.97,-2675.1C1027.9,-2675.1 1049.34,-2675.1 1069.79,-2675.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1069.77,-2677.2 1075.77,-2675.1 1069.77,-2673 1069.77,-2677.2"/>
</g>
<!-- src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge81" class="edge">
<title>src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1008.98,-2682.37C1029.65,-2688.07 1050.92,-2698.19 1063.5,-2716.1 1072.45,-2728.85 1060.46,-4949.1 1071.5,-4960.1 1221.27,-5109.33 1402.62,-5100.55 1547,-4946.1 1563.78,-4928.15 1543.77,-4746.95 1555,-4725.1 1560.14,-4715.09 1569.15,-4706.89 1578.15,-4700.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1579.16,-4702.48 1583.08,-4697.47 1576.89,-4698.94 1579.16,-4702.48"/>
</g>
<!-- src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge83" class="edge">
<title>src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1196.64,-2684.72C1213.17,-2690.83 1230.62,-2700.61 1240.38,-2716.1 1253.98,-2737.69 1232.45,-4533.17 1248.38,-4553.1 1290.89,-4606.31 1481.06,-4605.05 1547,-4622.1 1598.58,-4635.44 1616.95,-4627.06 1662.25,-4655.1 1677.21,-4664.36 1690.42,-4679.51 1699.25,-4691.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1697.41,-4692.29 1702.63,-4695.92 1700.81,-4689.83 1697.41,-4692.29"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge115" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1653.09,-4695.81C1660.78,-4697.45 1668.64,-4699.13 1676,-4700.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1675.09,-4702.65 1681.4,-4701.85 1675.97,-4698.54 1675.09,-4702.65"/>
</g>
<!-- src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts -->
<g id="node77" class="node">
<title>src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts</title>
<g id="a_node77"><a xlink:href="src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts" xlink:title="applyFilterToCard.ts">
<path fill="#ddfeff" stroke="black" d="M1369.08,-4427.35C1369.08,-4427.35 1283.67,-4427.35 1283.67,-4427.35 1280.58,-4427.35 1277.5,-4424.27 1277.5,-4421.18 1277.5,-4421.18 1277.5,-4415.02 1277.5,-4415.02 1277.5,-4411.93 1280.58,-4408.85 1283.67,-4408.85 1283.67,-4408.85 1369.08,-4408.85 1369.08,-4408.85 1372.17,-4408.85 1375.25,-4411.93 1375.25,-4415.02 1375.25,-4415.02 1375.25,-4421.18 1375.25,-4421.18 1375.25,-4424.27 1372.17,-4427.35 1369.08,-4427.35"/>
<text text-anchor="start" x="1285.5" y="-4414.8" font-family="Helvetica,sans-Serif" font-size="9.00">applyFilterToCard.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge85" class="edge">
<title>src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1362.92,-4427.84C1430.09,-4447.93 1576.6,-4500.08 1662.25,-4593.1 1688.16,-4621.24 1701.4,-4665.27 1707.12,-4689.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1705.04,-4690.2 1708.37,-4695.62 1709.15,-4689.31 1705.04,-4690.2"/>
</g>
<!-- src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts -->
<g id="edge84" class="edge">
<title>src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1341.04,-4427.66C1358.99,-4440.12 1391.62,-4462.4 1420.38,-4480.1 1421.2,-4480.61 1661.74,-4616.28 1662.25,-4617.1 1671.71,-4632.44 1667.06,-4761.36 1670.25,-4779.1 1677.49,-4819.38 1693.85,-4864.61 1703.41,-4889.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1701.43,-4889.75 1705.59,-4894.55 1705.33,-4888.2 1701.43,-4889.75"/>
</g>
<!-- src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById.ts -->
<g id="node79" class="node">
<title>src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById.ts</title>
<g id="a_node79"><a xlink:href="src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById.ts" xlink:title="findDeckCardById.ts">
<path fill="#ddfeff" stroke="black" d="M1198.08,-3329.35C1198.08,-3329.35 1111.17,-3329.35 1111.17,-3329.35 1108.08,-3329.35 1105,-3326.27 1105,-3323.18 1105,-3323.18 1105,-3317.02 1105,-3317.02 1105,-3313.93 1108.08,-3310.85 1111.17,-3310.85 1111.17,-3310.85 1198.08,-3310.85 1198.08,-3310.85 1201.17,-3310.85 1204.25,-3313.93 1204.25,-3317.02 1204.25,-3317.02 1204.25,-3323.18 1204.25,-3323.18 1204.25,-3326.27 1201.17,-3329.35 1198.08,-3329.35"/>
<text text-anchor="start" x="1113" y="-3316.8" font-family="Helvetica,sans-Serif" font-size="9.00">findDeckCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge86" class="edge">
<title>src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1173.65,-3310.52C1193.93,-3298.64 1225.91,-3276.34 1240.38,-3247.1 1247.04,-3233.63 1247.4,-3127.09 1248.38,-3112.1 1276.62,-2677.06 1316.14,-2144.82 1323.99,-2039.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.07,-2039.97 1324.42,-2033.83 1321.88,-2039.66 1326.07,-2039.97"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts -->
<g id="node80" class="node">
<title>src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts</title>
<g id="a_node80"><a xlink:href="src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts" xlink:title="getActiveFilters.ts">
<path fill="#ddfeff" stroke="black" d="M1192.08,-3451.35C1192.08,-3451.35 1117.17,-3451.35 1117.17,-3451.35 1114.08,-3451.35 1111,-3448.27 1111,-3445.18 1111,-3445.18 1111,-3439.02 1111,-3439.02 1111,-3435.93 1114.08,-3432.85 1117.17,-3432.85 1117.17,-3432.85 1192.08,-3432.85 1192.08,-3432.85 1195.17,-3432.85 1198.25,-3435.93 1198.25,-3439.02 1198.25,-3439.02 1198.25,-3445.18 1198.25,-3445.18 1198.25,-3448.27 1195.17,-3451.35 1192.08,-3451.35"/>
<text text-anchor="start" x="1119" y="-3438.8" font-family="Helvetica,sans-Serif" font-size="9.00">getActiveFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge87" class="edge">
<title>src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1198.49,-3441.29C1214.1,-3438.66 1230.35,-3432.72 1240.38,-3420.1 1251.02,-3406.7 1247.55,-3129.2 1248.38,-3112.1 1269.39,-2676.65 1314.64,-2144.73 1323.76,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.83,-2039.98 1324.26,-2033.82 1321.65,-2039.62 1325.83,-2039.98"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts -->
<g id="node81" class="node">
<title>src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts</title>
<g id="a_node81"><a xlink:href="src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts" xlink:title="getAvailableFilters.ts">
<path fill="#ddfeff" stroke="black" d="M1198.83,-3634.35C1198.83,-3634.35 1110.42,-3634.35 1110.42,-3634.35 1107.33,-3634.35 1104.25,-3631.27 1104.25,-3628.18 1104.25,-3628.18 1104.25,-3622.02 1104.25,-3622.02 1104.25,-3618.93 1107.33,-3615.85 1110.42,-3615.85 1110.42,-3615.85 1198.83,-3615.85 1198.83,-3615.85 1201.92,-3615.85 1205,-3618.93 1205,-3622.02 1205,-3622.02 1205,-3628.18 1205,-3628.18 1205,-3631.27 1201.92,-3634.35 1198.83,-3634.35"/>
<text text-anchor="start" x="1112.25" y="-3621.8" font-family="Helvetica,sans-Serif" font-size="9.00">getAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge88" class="edge">
<title>src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1205.2,-3623.01C1218.64,-3619.89 1231.82,-3613.97 1240.38,-3603.1 1248.81,-3592.38 1247.78,-3125.73 1248.38,-3112.1 1267.28,-2676.56 1314.21,-2144.71 1323.69,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.77,-2039.99 1324.22,-2033.82 1321.58,-2039.61 1325.77,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts -->
<g id="node82" class="node">
<title>src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts</title>
<g id="a_node82"><a xlink:href="src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts" xlink:title="getCardDetails.ts">
<path fill="#ddfeff" stroke="black" d="M1191.71,-3512.35C1191.71,-3512.35 1117.54,-3512.35 1117.54,-3512.35 1114.46,-3512.35 1111.38,-3509.27 1111.38,-3506.18 1111.38,-3506.18 1111.38,-3500.02 1111.38,-3500.02 1111.38,-3496.93 1114.46,-3493.85 1117.54,-3493.85 1117.54,-3493.85 1191.71,-3493.85 1191.71,-3493.85 1194.79,-3493.85 1197.88,-3496.93 1197.88,-3500.02 1197.88,-3500.02 1197.88,-3506.18 1197.88,-3506.18 1197.88,-3509.27 1194.79,-3512.35 1191.71,-3512.35"/>
<text text-anchor="start" x="1119.38" y="-3499.8" font-family="Helvetica,sans-Serif" font-size="9.00">getCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge89" class="edge">
<title>src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1198.08,-3502.38C1213.82,-3499.79 1230.28,-3493.86 1240.38,-3481.1 1246.74,-3473.06 1247.9,-3122.34 1248.38,-3112.1 1268.45,-2676.61 1314.45,-2144.72 1323.73,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.8,-2039.99 1324.24,-2033.82 1321.62,-2039.61 1325.8,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts -->
<g id="node83" class="node">
<title>src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts</title>
<g id="a_node83"><a xlink:href="src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts" xlink:title="getCardsInDeck.ts">
<path fill="#ddfeff" stroke="black" d="M1193.96,-3573.35C1193.96,-3573.35 1115.29,-3573.35 1115.29,-3573.35 1112.21,-3573.35 1109.12,-3570.27 1109.12,-3567.18 1109.12,-3567.18 1109.12,-3561.02 1109.12,-3561.02 1109.12,-3557.93 1112.21,-3554.85 1115.29,-3554.85 1115.29,-3554.85 1193.96,-3554.85 1193.96,-3554.85 1197.04,-3554.85 1200.12,-3557.93 1200.12,-3561.02 1200.12,-3561.02 1200.12,-3567.18 1200.12,-3567.18 1200.12,-3570.27 1197.04,-3573.35 1193.96,-3573.35"/>
<text text-anchor="start" x="1117.12" y="-3560.8" font-family="Helvetica,sans-Serif" font-size="9.00">getCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge90" class="edge">
<title>src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1200.31,-3563.01C1215.37,-3560.24 1230.77,-3554.28 1240.38,-3542.1 1247.77,-3532.72 1247.84,-3124.03 1248.38,-3112.1 1267.78,-2676.58 1314.31,-2144.72 1323.71,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.78,-2039.99 1324.23,-2033.82 1321.6,-2039.61 1325.78,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards.ts -->
<g id="node84" class="node">
<title>src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards.ts</title>
<g id="a_node84"><a xlink:href="src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards.ts" xlink:title="getCatalogCards.ts">
<path fill="#ddfeff" stroke="black" d="M1195.83,-3695.35C1195.83,-3695.35 1113.42,-3695.35 1113.42,-3695.35 1110.33,-3695.35 1107.25,-3692.27 1107.25,-3689.18 1107.25,-3689.18 1107.25,-3683.02 1107.25,-3683.02 1107.25,-3679.93 1110.33,-3676.85 1113.42,-3676.85 1113.42,-3676.85 1195.83,-3676.85 1195.83,-3676.85 1198.92,-3676.85 1202,-3679.93 1202,-3683.02 1202,-3683.02 1202,-3689.18 1202,-3689.18 1202,-3692.27 1198.92,-3695.35 1195.83,-3695.35"/>
<text text-anchor="start" x="1115.25" y="-3682.8" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge92" class="edge">
<title>src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1202.11,-3684.69C1216.6,-3681.79 1231.17,-3675.82 1240.38,-3664.1 1249.85,-3652.04 1247.72,-3127.42 1248.38,-3112.1 1266.89,-2676.54 1314.13,-2144.71 1323.68,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.75,-2039.99 1324.21,-2033.82 1321.57,-2039.61 1325.75,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts -->
<g id="node85" class="node">
<title>src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts</title>
<g id="a_node85"><a xlink:href="src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts" xlink:title="getCatalogError.ts">
<path fill="#ddfeff" stroke="black" d="M1193.21,-3756.35C1193.21,-3756.35 1116.04,-3756.35 1116.04,-3756.35 1112.96,-3756.35 1109.88,-3753.27 1109.88,-3750.18 1109.88,-3750.18 1109.88,-3744.02 1109.88,-3744.02 1109.88,-3740.93 1112.96,-3737.85 1116.04,-3737.85 1116.04,-3737.85 1193.21,-3737.85 1193.21,-3737.85 1196.29,-3737.85 1199.38,-3740.93 1199.38,-3744.02 1199.38,-3744.02 1199.38,-3750.18 1199.38,-3750.18 1199.38,-3753.27 1196.29,-3756.35 1193.21,-3756.35"/>
<text text-anchor="start" x="1117.88" y="-3743.8" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogError.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge93" class="edge">
<title>src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1199.46,-3746.2C1214.8,-3743.5 1230.62,-3737.54 1240.38,-3725.1 1250.88,-3711.7 1247.66,-3129.11 1248.38,-3112.1 1266.57,-2676.53 1314.06,-2144.71 1323.67,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.74,-2039.99 1324.2,-2033.82 1321.56,-2039.6 1325.74,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts -->
<g id="node86" class="node">
<title>src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts</title>
<g id="a_node86"><a xlink:href="src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts" xlink:title="getDeckBuilderView.ts">
<path fill="#ddfeff" stroke="black" d="M1202.21,-3817.35C1202.21,-3817.35 1107.04,-3817.35 1107.04,-3817.35 1103.96,-3817.35 1100.88,-3814.27 1100.88,-3811.18 1100.88,-3811.18 1100.88,-3805.02 1100.88,-3805.02 1100.88,-3801.93 1103.96,-3798.85 1107.04,-3798.85 1107.04,-3798.85 1202.21,-3798.85 1202.21,-3798.85 1205.29,-3798.85 1208.38,-3801.93 1208.38,-3805.02 1208.38,-3805.02 1208.38,-3811.18 1208.38,-3811.18 1208.38,-3814.27 1205.29,-3817.35 1202.21,-3817.35"/>
<text text-anchor="start" x="1108.88" y="-3804.8" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge94" class="edge">
<title>src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1208.77,-3805.15C1220.95,-3801.84 1232.6,-3796.03 1240.38,-3786.1 1246.15,-3778.73 1247.99,-3121.45 1248.38,-3112.1 1266.32,-2676.51 1314.01,-2144.7 1323.66,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.73,-2039.99 1324.19,-2033.82 1321.55,-2039.6 1325.73,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts -->
<g id="node87" class="node">
<title>src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts</title>
<g id="a_node87"><a xlink:href="src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts" xlink:title="getDeckName.ts">
<path fill="#ddfeff" stroke="black" d="M1190.21,-3878.35C1190.21,-3878.35 1119.04,-3878.35 1119.04,-3878.35 1115.96,-3878.35 1112.88,-3875.27 1112.88,-3872.18 1112.88,-3872.18 1112.88,-3866.02 1112.88,-3866.02 1112.88,-3862.93 1115.96,-3859.85 1119.04,-3859.85 1119.04,-3859.85 1190.21,-3859.85 1190.21,-3859.85 1193.29,-3859.85 1196.38,-3862.93 1196.38,-3866.02 1196.38,-3866.02 1196.38,-3872.18 1196.38,-3872.18 1196.38,-3875.27 1193.29,-3878.35 1190.21,-3878.35"/>
<text text-anchor="start" x="1120.88" y="-3865.8" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckName.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge95" class="edge">
<title>src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1196.83,-3868.65C1212.98,-3866.18 1230.07,-3860.27 1240.38,-3847.1 1246.67,-3839.06 1247.96,-3122.3 1248.38,-3112.1 1266.1,-2676.51 1313.97,-2144.7 1323.65,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.73,-2039.99 1324.19,-2033.82 1321.55,-2039.6 1325.73,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts -->
<g id="node88" class="node">
<title>src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts</title>
<g id="a_node88"><a xlink:href="src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts" xlink:title="getFilteredCards.ts">
<path fill="#ddfeff" stroke="black" d="M1195.08,-3939.35C1195.08,-3939.35 1114.17,-3939.35 1114.17,-3939.35 1111.08,-3939.35 1108,-3936.27 1108,-3933.18 1108,-3933.18 1108,-3927.02 1108,-3927.02 1108,-3923.93 1111.08,-3920.85 1114.17,-3920.85 1114.17,-3920.85 1195.08,-3920.85 1195.08,-3920.85 1198.17,-3920.85 1201.25,-3923.93 1201.25,-3927.02 1201.25,-3927.02 1201.25,-3933.18 1201.25,-3933.18 1201.25,-3936.27 1198.17,-3939.35 1195.08,-3939.35"/>
<text text-anchor="start" x="1116" y="-3926.8" font-family="Helvetica,sans-Serif" font-size="9.00">getFilteredCards.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge97" class="edge">
<title>src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1201.7,-3928.8C1216.33,-3925.93 1231.11,-3919.96 1240.38,-3908.1 1247.18,-3899.39 1247.93,-3123.15 1248.38,-3112.1 1265.92,-2676.5 1313.93,-2144.7 1323.65,-2039.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.72,-2039.99 1324.18,-2033.82 1321.54,-2039.6 1325.72,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts -->
<g id="edge96" class="edge">
<title>src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1193.16,-3939.81C1210.04,-3946.05 1228.74,-3955.92 1240.38,-3971.1 1293.62,-4040.55 1318.37,-4324.59 1324.06,-4399.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1321.96,-4399.75 1324.49,-4405.58 1326.15,-4399.44 1321.96,-4399.75"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts -->
<g id="node89" class="node">
<title>src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts</title>
<g id="a_node89"><a xlink:href="src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts" xlink:title="getGameSettings.ts">
<path fill="#ddfeff" stroke="black" d="M1196.21,-4000.35C1196.21,-4000.35 1113.04,-4000.35 1113.04,-4000.35 1109.96,-4000.35 1106.88,-3997.27 1106.88,-3994.18 1106.88,-3994.18 1106.88,-3988.02 1106.88,-3988.02 1106.88,-3984.93 1109.96,-3981.85 1113.04,-3981.85 1113.04,-3981.85 1196.21,-3981.85 1196.21,-3981.85 1199.29,-3981.85 1202.38,-3984.93 1202.38,-3988.02 1202.38,-3988.02 1202.38,-3994.18 1202.38,-3994.18 1202.38,-3997.27 1199.29,-4000.35 1196.21,-4000.35"/>
<text text-anchor="start" x="1114.88" y="-3987.8" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge98" class="edge">
<title>src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1202.59,-3989.63C1216.93,-3986.69 1231.3,-3980.72 1240.38,-3969.1 1247.7,-3959.72 1247.9,-3123.99 1248.38,-3112.1 1265.76,-2676.49 1313.9,-2144.7 1323.64,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.72,-2039.99 1324.18,-2033.82 1321.53,-2039.6 1325.72,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts -->
<g id="node90" class="node">
<title>src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts</title>
<g id="a_node90"><a xlink:href="src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts" xlink:title="getGameSettingsError.ts">
<path fill="#ddfeff" stroke="black" d="M1205.96,-4061.35C1205.96,-4061.35 1103.29,-4061.35 1103.29,-4061.35 1100.21,-4061.35 1097.12,-4058.27 1097.12,-4055.18 1097.12,-4055.18 1097.12,-4049.02 1097.12,-4049.02 1097.12,-4045.93 1100.21,-4042.85 1103.29,-4042.85 1103.29,-4042.85 1205.96,-4042.85 1205.96,-4042.85 1209.04,-4042.85 1212.12,-4045.93 1212.12,-4049.02 1212.12,-4049.02 1212.12,-4055.18 1212.12,-4055.18 1212.12,-4058.27 1209.04,-4061.35 1205.96,-4061.35"/>
<text text-anchor="start" x="1105.12" y="-4048.8" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettingsError.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge99" class="edge">
<title>src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1212.29,-4048.14C1223.18,-4044.7 1233.36,-4039.09 1240.38,-4030.1 1248.22,-4020.05 1247.87,-3124.84 1248.38,-3112.1 1265.63,-2676.49 1313.87,-2144.7 1323.64,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.71,-2039.99 1324.18,-2033.82 1321.53,-2039.6 1325.71,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts -->
<g id="node91" class="node">
<title>src/client/DeckBuilding/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts</title>
<g id="a_node91"><a xlink:href="src/client/DeckBuilding/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts" xlink:title="getMaxCardsInDeck.ts">
<path fill="#ddfeff" stroke="black" d="M1202.58,-4122.35C1202.58,-4122.35 1106.67,-4122.35 1106.67,-4122.35 1103.58,-4122.35 1100.5,-4119.27 1100.5,-4116.18 1100.5,-4116.18 1100.5,-4110.02 1100.5,-4110.02 1100.5,-4106.93 1103.58,-4103.85 1106.67,-4103.85 1106.67,-4103.85 1202.58,-4103.85 1202.58,-4103.85 1205.67,-4103.85 1208.75,-4106.93 1208.75,-4110.02 1208.75,-4110.02 1208.75,-4116.18 1208.75,-4116.18 1208.75,-4119.27 1205.67,-4122.35 1202.58,-4122.35"/>
<text text-anchor="start" x="1108.5" y="-4109.8" font-family="Helvetica,sans-Serif" font-size="9.00">getMaxCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge100" class="edge">
<title>src/client/DeckBuilding/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1209.23,-4110.05C1221.26,-4106.72 1232.71,-4100.93 1240.38,-4091.1 1248.74,-4080.38 1247.84,-3125.69 1248.38,-3112.1 1265.51,-2676.48 1313.84,-2144.7 1323.63,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.71,-2039.99 1324.18,-2033.82 1321.53,-2039.6 1325.71,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts -->
<g id="node92" class="node">
<title>src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts</title>
<g id="a_node92"><a xlink:href="src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts" xlink:title="getSearchTerm.ts">
<path fill="#ddfeff" stroke="black" d="M1192.08,-4183.35C1192.08,-4183.35 1117.17,-4183.35 1117.17,-4183.35 1114.08,-4183.35 1111,-4180.27 1111,-4177.18 1111,-4177.18 1111,-4171.02 1111,-4171.02 1111,-4167.93 1114.08,-4164.85 1117.17,-4164.85 1117.17,-4164.85 1192.08,-4164.85 1192.08,-4164.85 1195.17,-4164.85 1198.25,-4167.93 1198.25,-4171.02 1198.25,-4171.02 1198.25,-4177.18 1198.25,-4177.18 1198.25,-4180.27 1195.17,-4183.35 1192.08,-4183.35"/>
<text text-anchor="start" x="1119" y="-4170.8" font-family="Helvetica,sans-Serif" font-size="9.00">getSearchTerm.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge101" class="edge">
<title>src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1198.61,-4173.38C1214.23,-4170.76 1230.47,-4164.81 1240.38,-4152.1 1249.26,-4140.71 1247.81,-3126.53 1248.38,-3112.1 1265.41,-2676.48 1313.82,-2144.7 1323.63,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.7,-2039.99 1324.17,-2033.82 1321.52,-2039.6 1325.7,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts -->
<g id="node93" class="node">
<title>src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts</title>
<g id="a_node93"><a xlink:href="src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts" xlink:title="getTotalCardsInDeck.ts">
<path fill="#ddfeff" stroke="black" d="M1203.71,-4244.35C1203.71,-4244.35 1105.54,-4244.35 1105.54,-4244.35 1102.46,-4244.35 1099.38,-4241.27 1099.38,-4238.18 1099.38,-4238.18 1099.38,-4232.02 1099.38,-4232.02 1099.38,-4228.93 1102.46,-4225.85 1105.54,-4225.85 1105.54,-4225.85 1203.71,-4225.85 1203.71,-4225.85 1206.79,-4225.85 1209.88,-4228.93 1209.88,-4232.02 1209.88,-4232.02 1209.88,-4238.18 1209.88,-4238.18 1209.88,-4241.27 1206.79,-4244.35 1203.71,-4244.35"/>
<text text-anchor="start" x="1107.38" y="-4231.8" font-family="Helvetica,sans-Serif" font-size="9.00">getTotalCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge102" class="edge">
<title>src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1210.12,-4231.8C1221.82,-4228.44 1232.9,-4222.69 1240.38,-4213.1 1249.78,-4201.04 1247.78,-3127.38 1248.38,-3112.1 1265.31,-2676.47 1313.8,-2144.69 1323.63,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.7,-2039.99 1324.17,-2033.82 1321.52,-2039.6 1325.7,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge103" class="edge">
<title>src/client/DeckBuilding/application/queries/hasDeckDraft/hasDeckDraft.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1172.84,-4427.83C1203.42,-4445.13 1267.25,-4481.23 1301.86,-4500.8"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1300.8,-4502.61 1307.05,-4503.74 1302.86,-4498.96 1300.8,-4502.61"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts -->
<g id="node98" class="node">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts</title>
<g id="a_node98"><a xlink:href="src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts" xlink:title="DeckDraft.ts">
<path fill="#ddfeff" stroke="black" d="M1181.21,-4916.35C1181.21,-4916.35 1128.04,-4916.35 1128.04,-4916.35 1124.96,-4916.35 1121.88,-4913.27 1121.88,-4910.18 1121.88,-4910.18 1121.88,-4904.02 1121.88,-4904.02 1121.88,-4900.93 1124.96,-4897.85 1128.04,-4897.85 1128.04,-4897.85 1181.21,-4897.85 1181.21,-4897.85 1184.29,-4897.85 1187.38,-4900.93 1187.38,-4904.02 1187.38,-4904.02 1187.38,-4910.18 1187.38,-4910.18 1187.38,-4913.27 1184.29,-4916.35 1181.21,-4916.35"/>
<text text-anchor="start" x="1129.88" y="-4903.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts -->
<g id="edge106" class="edge">
<title>src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1314.89,-4523.65C1297.24,-4540.5 1262.2,-4577.47 1248.38,-4617.1 1243.36,-4631.48 1250.26,-4879.52 1240.38,-4891.1 1229.71,-4903.59 1212.63,-4908.31 1196.58,-4909.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1196.74,-4907.53 1190.85,-4909.9 1196.94,-4911.72 1196.74,-4907.53"/>
</g>
<!-- src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge104" class="edge">
<title>src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1203.94,-4355.36C1217.84,-4352.33 1231.6,-4346.37 1240.38,-4335.1 1250.81,-4321.7 1247.72,-3129.07 1248.38,-3112.1 1265.16,-2676.47 1313.77,-2144.69 1323.62,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.7,-2039.99 1324.17,-2033.82 1321.52,-2039.6 1325.7,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts -->
<g id="node97" class="node">
<title>src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts</title>
<g id="a_node97"><a xlink:href="src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts" xlink:title="isGameSettingsLoading.ts">
<path fill="#ddfeff" stroke="black" d="M1210.08,-4305.35C1210.08,-4305.35 1099.17,-4305.35 1099.17,-4305.35 1096.08,-4305.35 1093,-4302.27 1093,-4299.18 1093,-4299.18 1093,-4293.02 1093,-4293.02 1093,-4289.93 1096.08,-4286.85 1099.17,-4286.85 1099.17,-4286.85 1210.08,-4286.85 1210.08,-4286.85 1213.17,-4286.85 1216.25,-4289.93 1216.25,-4293.02 1216.25,-4293.02 1216.25,-4299.18 1216.25,-4299.18 1216.25,-4302.27 1213.17,-4305.35 1210.08,-4305.35"/>
<text text-anchor="start" x="1101" y="-4292.8" font-family="Helvetica,sans-Serif" font-size="9.00">isGameSettingsLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge105" class="edge">
<title>src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1216.62,-4290.65C1225.85,-4287.15 1234.3,-4281.89 1240.38,-4274.1 1250.29,-4261.37 1247.75,-3128.23 1248.38,-3112.1 1265.23,-2676.47 1313.79,-2144.69 1323.62,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.7,-2039.99 1324.17,-2033.82 1321.52,-2039.6 1325.7,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts -->
<g id="node99" class="node">
<title>src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts</title>
<g id="a_node99"><a xlink:href="src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts" xlink:title="subscribeToDeckDraft.ts">
<path fill="#ddfeff" stroke="black" d="M766.21,-3525.35C766.21,-3525.35 663.54,-3525.35 663.54,-3525.35 660.46,-3525.35 657.38,-3522.27 657.38,-3519.18 657.38,-3519.18 657.38,-3513.02 657.38,-3513.02 657.38,-3509.93 660.46,-3506.85 663.54,-3506.85 663.54,-3506.85 766.21,-3506.85 766.21,-3506.85 769.29,-3506.85 772.38,-3509.93 772.38,-3513.02 772.38,-3513.02 772.38,-3519.18 772.38,-3519.18 772.38,-3522.27 769.29,-3525.35 766.21,-3525.35"/>
<text text-anchor="start" x="665.38" y="-3512.8" font-family="Helvetica,sans-Serif" font-size="9.00">subscribeToDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge109" class="edge">
<title>src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M716.13,-3525.43C720.28,-3668.57 780.33,-5404.15 1240.38,-4946.1 1249.4,-4937.11 1247.9,-3124.83 1248.38,-3112.1 1264.68,-2676.45 1313.67,-2144.69 1323.61,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.16,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="edge107" class="edge">
<title>src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M715.72,-3506.38C714.47,-3414.25 708.87,-2694.29 837,-2533.1 851.99,-2514.25 876.46,-2504.06 898.74,-2498.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="899.12,-2500.63 904.52,-2497.27 898.2,-2496.53 899.12,-2500.63"/>
</g>
<!-- src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge108" class="edge">
<title>src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M716.65,-3525.75C726.2,-3641.7 824.84,-4766.81 1071.5,-4960.1 1130.58,-5006.39 1170.17,-4986.64 1240.38,-4960.1 1271.25,-4948.43 1298.16,-4919.85 1313.04,-4901.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1314.37,-4903.24 1316.44,-4897.22 1311.08,-4900.64 1314.37,-4903.24"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge127" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1380.79,-4892.98C1392.76,-4894.75 1405.53,-4896.63 1417.7,-4898.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1417.21,-4900.48 1423.45,-4899.27 1417.82,-4896.32 1417.21,-4900.48"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge126" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1380.91,-4883.55C1425.56,-4883.03 1490.73,-4884.09 1547,-4892.1 1552.72,-4892.91 1558.69,-4894.07 1564.53,-4895.39"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1563.69,-4897.35 1570.01,-4896.7 1564.67,-4893.26 1563.69,-4897.35"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge116" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1534.94,-4676.1C1538.67,-4676.63 1542.43,-4677.16 1546.17,-4677.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1545.6,-4679.73 1551.84,-4678.49 1546.19,-4675.57 1545.6,-4679.73"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge117" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1534.75,-4660.08C1571.96,-4656.92 1621.26,-4656.87 1662.25,-4671.1 1674.72,-4675.43 1686.55,-4684.32 1695.4,-4692.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1693.83,-4693.7 1699.63,-4696.31 1696.72,-4690.65 1693.83,-4693.7"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogEvents.ts -->
<g id="edge118" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1371.31,-4724.58C1388.81,-4727.16 1409.03,-4730.15 1427.04,-4732.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1426.45,-4734.84 1432.69,-4733.64 1427.06,-4730.68 1426.45,-4734.84"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts -->
<g id="edge119" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1371.37,-4716.68C1415.89,-4715.91 1486.57,-4716.5 1547,-4725.1 1552.72,-4725.91 1558.69,-4727.07 1564.53,-4728.39"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1563.69,-4730.35 1570.01,-4729.7 1564.67,-4726.26 1563.69,-4730.35"/>
</g>
<!-- src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge120" class="edge">
<title>src/client/DeckBuilding/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1385.89,-4637.1C1394.48,-4637.1 1403.38,-4637.1 1412.09,-4637.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1412.06,-4639.2 1418.06,-4637.1 1412.06,-4635 1412.06,-4639.2"/>
</g>
<!-- src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts -->
<g id="edge124" class="edge">
<title>src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1654.59,-4907.1C1659.39,-4907.1 1664.24,-4907.1 1668.97,-4907.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1668.87,-4909.2 1674.87,-4907.1 1668.87,-4905 1668.87,-4909.2"/>
</g>
<!-- src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge130" class="edge">
<title>src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1212.62,-4805.8C1228.63,-4807.12 1246.13,-4808.56 1262.46,-4809.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1262.23,-4812 1268.38,-4810.4 1262.58,-4807.81 1262.23,-4812"/>
</g>
<!-- src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/GameSettings.ts -->
<g id="edge129" class="edge">
<title>src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/DeckBuilding/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1212.79,-4798.72C1264.87,-4797.07 1343.86,-4795.84 1412.38,-4800.1 1417.13,-4800.4 1422.08,-4800.81 1427.02,-4801.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1426.66,-4803.38 1432.85,-4801.93 1427.11,-4799.2 1426.66,-4803.38"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx -->
<g id="edge158" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M552.64,-5022.93C570.17,-5020.38 588.16,-5014.07 599.38,-5000.1 615.99,-4979.4 590.57,-1197.44 608.75,-1178.1 647.26,-1137.13 815.89,-1144.82 900.49,-1151.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="900.22,-1153.62 906.37,-1152.02 900.56,-1149.44 900.22,-1153.62"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts -->
<g id="edge157" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.8,-5031.79C613.58,-5063.65 904.45,-5163.1 1153.62,-5163.1 1153.62,-5163.1 1153.62,-5163.1 1478.5,-5163.1 1519.68,-5163.1 1632.04,-5167.09 1662.25,-5139.1 1693.66,-5110 1706.05,-4974.87 1709.59,-4925.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1711.67,-4925.8 1709.98,-4919.67 1707.48,-4925.51 1711.67,-4925.8"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge174" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M567.7,-4897.12C579.8,-4893.55 591.16,-4887.64 599.38,-4878.1 618.42,-4855.99 591.43,-4770.59 608.75,-4747.1 621.2,-4730.2 641.65,-4720.27 661.21,-4714.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="661.65,-4716.49 666.89,-4712.89 660.55,-4712.43 661.65,-4716.49"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge176" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M548.77,-4909.77C567.12,-4915.83 586.7,-4925.56 599.38,-4941.1 613.11,-4957.94 591.97,-4975.28 608.75,-4989.1 684.31,-5051.33 755.27,-5053.49 829,-4989.1 1120.55,-4734.48 1036.34,-3649.22 1063.5,-3263.1 1064.22,-3252.89 1064.83,-1796.87 1071.5,-1789.1 1080.85,-1778.22 1095.31,-1773.82 1109.51,-1772.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1109.36,-1774.63 1115.25,-1772.23 1109.15,-1770.44 1109.36,-1774.63"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="node124" class="node">
<title>src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<g id="a_node124"><a xlink:href="src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx" xlink:title="useGameSettingsByGameId.tsx">
<path fill="#bbfeff" stroke="black" d="M781.21,-4959.35C781.21,-4959.35 648.54,-4959.35 648.54,-4959.35 645.46,-4959.35 642.38,-4956.27 642.38,-4953.18 642.38,-4953.18 642.38,-4947.02 642.38,-4947.02 642.38,-4943.93 645.46,-4940.85 648.54,-4940.85 648.54,-4940.85 781.21,-4940.85 781.21,-4940.85 784.29,-4940.85 787.38,-4943.93 787.38,-4947.02 787.38,-4947.02 787.38,-4953.18 787.38,-4953.18 787.38,-4956.27 784.29,-4959.35 781.21,-4959.35"/>
<text text-anchor="start" x="650.38" y="-4946.8" font-family="Helvetica,sans-Serif" font-size="9.00">useGameSettingsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="edge175" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M536.4,-4909.74C557.36,-4915.35 584.5,-4922.41 608.75,-4928.1 624.22,-4931.73 641.04,-4935.38 656.6,-4938.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="655.83,-4940.62 662.13,-4939.79 656.68,-4936.51 655.83,-4940.62"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge181" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M771.17,-4652C792.64,-4657.62 815.32,-4667.77 829,-4686.1 846.97,-4710.18 822.45,-4794.81 837,-4821.1 898.97,-4933.04 949.63,-4949.14 1071.5,-4988.1 1142.99,-5010.95 1187.19,-5041.06 1240.38,-4988.1 1249.61,-4978.91 1247.89,-3125.12 1248.38,-3112.1 1264.66,-2676.45 1313.67,-2144.69 1323.61,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.16,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="edge177" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M717.23,-4635.42C728.29,-4556.13 803.69,-4005.29 829,-3555.1 829.7,-3542.62 829.79,-2665.31 837,-2655.1 849.56,-2637.3 870.71,-2627.21 891.27,-2621.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="891.78,-2623.53 897.09,-2620.03 890.76,-2619.45 891.78,-2623.53"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts -->
<g id="edge178" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M716.11,-4635.56C718.22,-4555.75 736.47,-3998.99 837,-3873.1 902.41,-3791.19 1028.94,-3762.31 1100.66,-3752.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1100.88,-3754.37 1106.55,-3751.5 1100.32,-3750.21 1100.88,-3754.37"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts -->
<g id="edge179" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M716.77,-4635.58C721.67,-4587.2 748.31,-4364.94 837,-4211.1 911.49,-4081.9 951,-4058.96 1071.5,-3971.1 1086.74,-3959.99 1105.45,-3950.39 1121.15,-3943.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1121.65,-3945.35 1126.29,-3941.01 1119.95,-3941.51 1121.65,-3945.35"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge180" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M728.35,-4635.44C773.22,-4600.92 931.18,-4481.27 1071.5,-4398.1 1088.54,-4388 1108.4,-4378.04 1124.32,-4370.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1124.98,-4372.48 1129.51,-4368.02 1123.19,-4368.68 1124.98,-4372.48"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="edge182" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.9,-4709.9C784.16,-4709.37 812.6,-4703.9 829,-4684.1 844.27,-4665.66 823.23,-2979.69 837,-2960.1 850.38,-2941.07 873.57,-2930.84 895.41,-2925.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="895.67,-2927.46 901.06,-2924.09 894.74,-2923.36 895.67,-2927.46"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge199" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.95,-4710.36C784.38,-4715.19 812.98,-4725.43 829,-4747.1 850.16,-4775.73 816.64,-4877.9 837,-4907.1 942.63,-5058.62 1109.49,-5137.43 1240.38,-5007.1 1249.7,-4997.81 1247.88,-3125.25 1248.38,-3112.1 1264.65,-2676.45 1313.67,-2144.69 1323.61,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.16,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge183" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.87,-4709.87C784.12,-4709.33 812.56,-4703.87 829,-4684.1 837.02,-4674.46 834.38,-3793.37 837,-3781.1 889.51,-3535.38 999.09,-3505.97 1063.5,-3263.1 1072.09,-3230.7 1054.29,-3216.86 1071.5,-3188.1 1082.95,-3168.97 1103.76,-3154.88 1121.57,-3145.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1122.33,-3147.59 1126.78,-3143.05 1120.47,-3143.82 1122.33,-3147.59"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="edge184" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.9,-4709.89C784.16,-4709.36 812.59,-4703.89 829,-4684.1 842.12,-4668.27 825.17,-3220.91 837,-3204.1 850.38,-3185.07 873.57,-3174.85 895.41,-3169.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="895.67,-3171.46 901.06,-3168.09 894.75,-3167.36 895.67,-3171.46"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/search/search.ts -->
<g id="edge186" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.87,-4709.87C784.12,-4709.33 812.56,-4703.86 829,-4684.1 844.69,-4665.24 820.39,-3819.15 837,-3801.1 871.22,-3763.9 1029.14,-3817.17 1063.5,-3780.1 1081.63,-3760.54 1061.79,-3324.94 1071.5,-3300.1 1084.14,-3267.78 1113.7,-3239.5 1133.68,-3223.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1134.81,-3224.89 1138.2,-3219.51 1132.2,-3221.61 1134.81,-3224.89"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="edge185" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.9,-4709.9C784.16,-4709.36 812.6,-4703.9 829,-4684.1 843.19,-4666.97 824.2,-3100.3 837,-3082.1 847.81,-3066.72 865.04,-3057.09 882.78,-3051.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="882.94,-3053.22 888.05,-3049.44 881.7,-3049.2 882.94,-3053.22"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts -->
<g id="edge187" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.9,-4709.9C784.16,-4709.37 812.6,-4703.9 829,-4684.1 844.81,-4665 822.75,-2919.38 837,-2899.1 849.95,-2880.67 872.1,-2870.5 893.31,-2864.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="893.71,-2866.96 899.06,-2863.53 892.74,-2862.88 893.71,-2866.96"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="edge188" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.91,-4709.9C784.17,-4709.37 812.6,-4703.9 829,-4684.1 845.35,-4664.35 822.26,-2859.08 837,-2838.1 847.51,-2823.15 864.07,-2813.63 881.28,-2807.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="881.8,-2809.62 886.88,-2805.8 880.52,-2805.62 881.8,-2809.62"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="edge189" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.91,-4709.9C784.17,-4709.37 812.61,-4703.91 829,-4684.1 846.43,-4663.04 821.29,-2738.47 837,-2716.1 847.91,-2700.57 865.36,-2690.9 883.28,-2684.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="883.5,-2687.02 888.63,-2683.26 882.28,-2683 883.5,-2687.02"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge197" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M760.1,-4710.52C784.42,-4715.4 812.86,-4725.65 829,-4747.1 844.94,-4768.28 823.71,-4843.17 837,-4866.1 898.36,-4971.93 1203.05,-5120.1 1325.38,-5120.1 1325.38,-5120.1 1325.38,-5120.1 1478.5,-5120.1 1590.97,-5120.1 1610.94,-5046.19 1662.25,-4946.1 1674.32,-4922.55 1665.55,-4913.14 1670.25,-4887.1 1680.97,-4827.74 1697.98,-4758.29 1706.12,-4726.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1708.06,-4727.01 1707.51,-4720.67 1703.99,-4725.97 1708.06,-4727.01"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts -->
<g id="edge190" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.87,-4709.87C784.12,-4709.33 812.56,-4703.86 829,-4684.1 844.39,-4665.61 820.6,-3835.7 837,-3818.1 871.43,-3781.17 1028.77,-3836.76 1063.5,-3800.1 1075.62,-3787.31 1062.05,-3497.96 1071.5,-3483.1 1079.65,-3470.29 1093.11,-3461.33 1106.8,-3455.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.32,-3457.2 1112.06,-3452.96 1105.71,-3453.32 1107.32,-3457.2"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts -->
<g id="edge191" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.87,-4709.87C784.12,-4709.33 812.55,-4703.86 829,-4684.1 844.03,-4666.04 821.09,-3855.39 837,-3838.1 871.23,-3800.9 1028.9,-3853.95 1063.5,-3817.1 1084.27,-3794.97 1055.16,-3569.67 1071.5,-3544.1 1079.68,-3531.3 1093.15,-3522.35 1106.83,-3516.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.35,-3518.22 1112.08,-3513.98 1105.74,-3514.34 1107.35,-3518.22"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts -->
<g id="edge192" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.87,-4709.87C784.11,-4709.32 812.55,-4703.86 829,-4684.1 843.57,-4666.59 821.79,-3881.06 837,-3864.1 904.68,-3788.62 994.89,-3911.74 1063.5,-3837.1 1080.95,-3818.11 1057.56,-3626.8 1071.5,-3605.1 1079.71,-3592.32 1093.19,-3583.38 1106.87,-3577.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.38,-3579.24 1112.12,-3575 1105.77,-3575.36 1107.38,-3579.24"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts -->
<g id="edge193" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.86,-4709.86C784.11,-4709.32 812.54,-4703.85 829,-4684.1 842.58,-4667.79 823.86,-3936.77 837,-3920.1 901.25,-3838.57 980.81,-3925.85 1063.5,-3863.1 1069.21,-3858.77 1066.29,-3854.02 1071.5,-3849.1 1084.2,-3837.09 1101.1,-3827.85 1116.26,-3821.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1116.94,-3823.19 1121.67,-3818.94 1115.33,-3819.31 1116.94,-3823.19"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts -->
<g id="edge194" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getDeckName/getDeckName.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M760.1,-4709.82C784.26,-4709.22 812.54,-4703.72 829,-4684.1 845.89,-4663.96 828.25,-4235.88 837,-4211.1 893.45,-4051.19 940.28,-4017.52 1071.5,-3910.1 1085.55,-3898.6 1103.35,-3889.22 1118.79,-3882.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1119.62,-3884.28 1124.3,-3879.98 1117.96,-3880.42 1119.62,-3884.28"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts -->
<g id="edge195" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M760.01,-4709.75C784.15,-4709.13 812.43,-4703.63 829,-4684.1 839.1,-4672.19 830.04,-4417.08 837,-4403.1 895.81,-4284.91 1048.15,-4214.09 1117.86,-4186.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1118.26,-4189 1123.11,-4184.89 1116.76,-4185.08 1118.26,-4189"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts -->
<g id="edge196" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.97,-4704.46C781.97,-4701.98 808.25,-4696.38 829,-4684.1 969.26,-4601.05 1003.26,-4553.56 1063.5,-4402.1 1073.87,-4376.03 1055.99,-4299.48 1071.5,-4276.1 1079.97,-4263.33 1093.66,-4254.37 1107.45,-4248.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1108.01,-4250.2 1112.75,-4245.97 1106.4,-4246.32 1108.01,-4250.2"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge198" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M754.6,-4715.81C777.32,-4722.53 806,-4732.86 829,-4747.1 955.55,-4825.46 948.45,-4894.35 1071.5,-4978.1 1143.13,-5026.85 1163.76,-5041.44 1248.38,-5060.1 1380.93,-5089.33 1444.64,-5085.25 1547,-4996.1 1570.22,-4975.88 1587.98,-4944.31 1597.56,-4924.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1599.42,-4925.69 1600.09,-4919.37 1595.62,-4923.9 1599.42,-4925.69"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge207" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M778.53,-4898.78C797.43,-4904.76 816.47,-4914.44 829,-4930.1 846.63,-4952.14 817.94,-4972.28 837,-4993.1 959.04,-5126.36 1112.33,-5171.61 1240.38,-5044.1 1249.88,-5034.63 1247.87,-3125.51 1248.38,-3112.1 1264.63,-2676.45 1313.66,-2144.69 1323.61,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.16,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="edge205" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M805.34,-4884.26C814.42,-4880.34 822.62,-4874.8 829,-4867.1 844.27,-4848.66 823.23,-3162.69 837,-3143.1 846.26,-3129.92 860.23,-3120.97 875.19,-3114.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="875.84,-3116.88 880.74,-3112.84 874.39,-3112.94 875.84,-3116.88"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts -->
<g id="edge206" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M805.35,-4883.92C814.36,-4880.04 822.55,-4874.61 829,-4867.1 836.17,-4858.75 835.29,-4679.98 837,-4669.1 895.3,-4298.13 1004.84,-4226.01 1063.5,-3855.1 1066.78,-3834.34 1060.07,-3683.74 1071.5,-3666.1 1079.83,-3653.24 1093.49,-3644.26 1107.29,-3638.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.85,-3640.1 1112.6,-3635.87 1106.25,-3636.21 1107.85,-3640.1"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx -->
<g id="edge156" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M544.99,-4840.81C564.76,-4839.06 586.52,-4833.11 599.38,-4817.1 615.31,-4797.25 590.73,-1170.07 608.75,-1152.1 648.43,-1112.52 816.09,-1133.76 900.41,-1147.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="900.04,-1149.51 906.3,-1148.41 900.72,-1145.36 900.04,-1149.51"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts -->
<g id="edge155" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/DeckBuilding/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M544.78,-4846.64C564.64,-4852.4 586.51,-4862.48 599.38,-4880.1 613.71,-4899.74 594.14,-4969.67 608.75,-4989.1 762.7,-5193.86 897.45,-5177.1 1153.62,-5177.1 1153.62,-5177.1 1153.62,-5177.1 1478.5,-5177.1 1560.29,-5177.1 1600.78,-5221.05 1662.25,-5167.1 1698.64,-5135.16 1707.99,-4979.42 1710.15,-4925.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1712.24,-4925.78 1710.37,-4919.71 1708.05,-4925.62 1712.24,-4925.78"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge164" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M563.52,-4713.87C594.41,-4712.28 631.31,-4710.37 660.82,-4708.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="660.82,-4710.95 666.7,-4708.54 660.6,-4706.75 660.82,-4710.95"/>
</g>
<!-- src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge247" class="edge">
<title>src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1172.84,-1934.83C1203.03,-1951.91 1265.66,-1987.33 1300.55,-2007.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1299.48,-2008.87 1305.74,-2009.99 1301.55,-2005.21 1299.48,-2008.87"/>
</g>
<!-- src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge244" class="edge">
<title>src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1157.25,-1934.49C1167.46,-1993.84 1222.69,-2323.06 1240.38,-2594.1 1242.06,-2619.92 1236.68,-4434.02 1248.38,-4457.1 1258.22,-4476.53 1278.14,-4490.99 1295.25,-4500.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1293.9,-4502.16 1300.19,-4503.09 1295.86,-4498.44 1293.9,-4502.16"/>
</g>
<!-- src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts -->
<g id="edge245" class="edge">
<title>src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1145.53,-1934.82C1128.02,-1956.5 1086.99,-2011.21 1071.5,-2065.1 1063.12,-2094.27 1078.92,-2311.96 1063.5,-2338.1 1004.08,-2438.84 900.71,-2374.01 837,-2472.1 720.88,-2650.88 715.83,-3373.11 715.83,-3497.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="713.73,-3497.5 715.83,-3503.5 717.93,-3497.5 713.73,-3497.5"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="node126" class="node">
<title>src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<g id="a_node126"><a xlink:href="src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts" xlink:title="browserDeckDraftService.ts">
<path fill="#ddfeff" stroke="black" d="M372.96,-5364.35C372.96,-5364.35 256.04,-5364.35 256.04,-5364.35 252.96,-5364.35 249.88,-5361.27 249.88,-5358.18 249.88,-5358.18 249.88,-5352.02 249.88,-5352.02 249.88,-5348.93 252.96,-5345.85 256.04,-5345.85 256.04,-5345.85 372.96,-5345.85 372.96,-5345.85 376.04,-5345.85 379.12,-5348.93 379.12,-5352.02 379.12,-5352.02 379.12,-5358.18 379.12,-5358.18 379.12,-5361.27 376.04,-5364.35 372.96,-5364.35"/>
<text text-anchor="start" x="257.88" y="-5351.8" font-family="Helvetica,sans-Serif" font-size="9.00">browserDeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="edge246" class="edge">
<title>src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1152.04,-1934.49C1142.13,-1993.85 1088.46,-2323.15 1071.5,-2594.1 1070.89,-2603.84 1069.87,-5397.7 1063.5,-5405.1 874.84,-5624.3 450.12,-5425.29 341.29,-5368.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="342.41,-5367.16 336.12,-5366.24 340.47,-5370.88 342.41,-5367.16"/>
</g>
<!-- src/client/Shared/infrastructure/services/location/browserLocationService.ts -->
<g id="node145" class="node">
<title>src/client/Shared/infrastructure/services/location/browserLocationService.ts</title>
<g id="a_node145"><a xlink:href="src/client/Shared/infrastructure/services/location/browserLocationService.ts" xlink:title="browserLocationService.ts">
<path fill="#ddfeff" stroke="black" d="M1382.21,-1907.35C1382.21,-1907.35 1270.54,-1907.35 1270.54,-1907.35 1267.46,-1907.35 1264.38,-1904.27 1264.38,-1901.18 1264.38,-1901.18 1264.38,-1895.02 1264.38,-1895.02 1264.38,-1891.93 1267.46,-1888.85 1270.54,-1888.85 1270.54,-1888.85 1382.21,-1888.85 1382.21,-1888.85 1385.29,-1888.85 1388.38,-1891.93 1388.38,-1895.02 1388.38,-1895.02 1388.38,-1901.18 1388.38,-1901.18 1388.38,-1904.27 1385.29,-1907.35 1382.21,-1907.35"/>
<text text-anchor="start" x="1272.38" y="-1894.8" font-family="Helvetica,sans-Serif" font-size="9.00">browserLocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/Shared/infrastructure/services/location/browserLocationService.ts -->
<g id="edge248" class="edge">
<title>src/client/Shared/infrastructure/store/store.ts&#45;&gt;src/client/Shared/infrastructure/services/location/browserLocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1181.78,-1920.94C1201.5,-1917.8 1229.51,-1913.35 1255.57,-1909.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1255.73,-1911.3 1261.32,-1908.29 1255.07,-1907.16 1255.73,-1911.3"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge162" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M534.09,-4970.57C689.8,-5014.63 1352.28,-5194.58 1547,-5124.1 1609.69,-5101.41 1632.27,-5087.64 1662.25,-5028.1 1676.36,-5000.07 1665.88,-4918.18 1670.25,-4887.1 1678.64,-4827.37 1696.77,-4758.09 1705.65,-4726.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1707.57,-4726.96 1707.17,-4720.61 1703.53,-4725.82 1707.57,-4726.96"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx -->
<g id="node123" class="node">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx</title>
<g id="a_node123"><a xlink:href="src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx" xlink:title="DeckBuildingSkeletonCard.tsx">
<path fill="#bbfeff" stroke="black" d="M378.58,-5214.35C378.58,-5214.35 250.42,-5214.35 250.42,-5214.35 247.33,-5214.35 244.25,-5211.27 244.25,-5208.18 244.25,-5208.18 244.25,-5202.02 244.25,-5202.02 244.25,-5198.93 247.33,-5195.85 250.42,-5195.85 250.42,-5195.85 378.58,-5195.85 378.58,-5195.85 381.67,-5195.85 384.75,-5198.93 384.75,-5202.02 384.75,-5202.02 384.75,-5208.18 384.75,-5208.18 384.75,-5211.27 381.67,-5214.35 378.58,-5214.35"/>
<text text-anchor="start" x="252.25" y="-5201.8" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingSkeletonCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx -->
<g id="edge163" class="edge">
<title>src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/Shared/infrastructure/components/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M384.9,-5199.64C500.66,-5186.35 730.19,-5141.16 829,-4989.1 843.35,-4967.02 821.89,-1218.66 837,-1197.1 851.41,-1176.54 877.29,-1166.25 900.57,-1161.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="900.88,-1163.2 906.36,-1159.99 900.07,-1159.08 900.88,-1163.2"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts -->
<g id="edge204" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/Shared/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M722.79,-4959.57C739.52,-4981.66 784.7,-5036.5 837,-5060.1 1000.75,-5133.99 1113.09,-5212.87 1240.38,-5086.1 1250.09,-5076.43 1247.86,-3125.8 1248.38,-3112.1 1264.61,-2676.45 1313.66,-2144.69 1323.6,-2039.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.68,-2039.99 1324.16,-2033.82 1321.5,-2039.59 1325.68,-2039.99"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="edge200" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M787.71,-4950.82C803.55,-4947.37 818.67,-4940.59 829,-4928.1 848.04,-4905.08 819.83,-2801.55 837,-2777.1 849.41,-2759.42 870.3,-2749.34 890.71,-2743.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="891.17,-2745.65 896.47,-2742.14 890.14,-2741.58 891.17,-2745.65"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts -->
<g id="edge201" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M787.75,-4950.54C803.49,-4947.08 818.55,-4940.36 829,-4928.1 846.66,-4907.37 818.07,-4702.68 837,-4683.1 872.07,-4646.84 1028.8,-4704.71 1063.5,-4668.1 1075.65,-4655.28 1062.11,-4047.07 1071.5,-4032.1 1079.57,-4019.23 1093.01,-4010.27 1106.71,-4004.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.22,-4006.13 1111.97,-4001.9 1105.62,-4002.25 1107.22,-4006.13"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts -->
<g id="edge202" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M787.73,-4950.53C803.48,-4947.07 818.54,-4940.35 829,-4928.1 845.68,-4908.57 819.13,-4715.54 837,-4697.1 872.1,-4660.87 1028.78,-4718.69 1063.5,-4682.1 1074.76,-4670.23 1062.8,-4106.96 1071.5,-4093.1 1079.58,-4080.24 1093.02,-4071.27 1106.71,-4065.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.23,-4067.14 1111.97,-4062.91 1105.63,-4063.26 1107.23,-4067.14"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts -->
<g id="edge203" class="edge">
<title>src/client/DeckBuilding/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M787.66,-4945.35C802.25,-4941.94 816.84,-4936.54 829,-4928.1 992.97,-4814.29 1005.68,-4736.13 1063.5,-4545.1 1070.2,-4522.96 1058.96,-4356.53 1071.5,-4337.1 1079.81,-4324.23 1093.46,-4315.24 1107.26,-4309.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1107.82,-4311.08 1112.57,-4306.85 1106.23,-4307.19 1107.82,-4311.08"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/hooks/useDeckById/useDeckById.tsx -->
<g id="node125" class="node">
<title>src/client/DeckBuilding/infrastructure/hooks/useDeckById/useDeckById.tsx</title>
<g id="a_node125"><a xlink:href="src/client/DeckBuilding/infrastructure/hooks/useDeckById/useDeckById.tsx" xlink:title="useDeckById.tsx">
<path fill="#ccffcc" stroke="black" d="M750.46,-4776.35C750.46,-4776.35 679.29,-4776.35 679.29,-4776.35 676.21,-4776.35 673.12,-4773.27 673.12,-4770.18 673.12,-4770.18 673.12,-4764.02 673.12,-4764.02 673.12,-4760.93 676.21,-4757.85 679.29,-4757.85 679.29,-4757.85 750.46,-4757.85 750.46,-4757.85 753.54,-4757.85 756.62,-4760.93 756.62,-4764.02 756.62,-4764.02 756.62,-4770.18 756.62,-4770.18 756.62,-4773.27 753.54,-4776.35 750.46,-4776.35"/>
<text text-anchor="start" x="681.12" y="-4763.8" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckById.tsx</text>
</a>
</g>
</g>
<!-- src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge208" class="edge">
<title>src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/DeckBuilding/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M353.92,-5345.44C488.44,-5311.24 936.81,-5193.89 1063.5,-5118.1 1157.6,-5061.81 1196.48,-5046.58 1240.38,-4946.1 1255.01,-4912.59 1236.42,-4651.66 1248.38,-4617.1 1260.6,-4581.77 1289.77,-4548.59 1308.49,-4529.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1309.88,-4531.45 1312.7,-4525.76 1306.94,-4528.45 1309.88,-4531.45"/>
</g>
<!-- src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts -->
<g id="edge209" class="edge">
<title>src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/DeckBuilding/domain/DeckBuilder/DeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M379.37,-5356.99C437.58,-5357.21 525.49,-5353.78 599.38,-5335.1 818.88,-5279.61 906.06,-5287.81 1063.5,-5125.1 1120.45,-5066.25 1143.42,-4966.32 1150.75,-4925.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1152.78,-4926 1151.71,-4919.73 1148.63,-4925.3 1152.78,-4926"/>
</g>
<!-- src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="edge210" class="edge">
<title>src/client/Gaming/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M977.75,-5793.45C1004.01,-5795.11 1043.16,-5792.77 1063.5,-5768.1 1081.77,-5745.95 1057.3,-1659.05 1071.5,-1634.1 1080.96,-1617.48 1098.52,-1605.9 1114.99,-1598.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1115.52,-1600.25 1120.17,-1595.92 1113.83,-1596.4 1115.52,-1600.25"/>
</g>
<!-- src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="edge216" class="edge">
<title>src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M956.66,-2166.75C975.8,-2133.09 1040.36,-2014.45 1063.5,-1907.1 1069.89,-1877.43 1056.13,-1660.27 1071.5,-1634.1 1081.18,-1617.61 1098.77,-1606.04 1115.2,-1598.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1115.71,-1600.37 1120.35,-1596.03 1114.02,-1596.53 1115.71,-1600.37"/>
</g>
<!-- src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge217" class="edge">
<title>src/client/MatchMaking/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M956.57,-2166.73C975.41,-2133 1039.09,-2014.16 1063.5,-1907.1 1067.23,-1890.72 1062.29,-1770.15 1071.5,-1756.1 1080.86,-1741.82 1096.78,-1732.31 1112.12,-1726.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1112.81,-1728.06 1117.7,-1724 1111.34,-1724.13 1112.81,-1728.06"/>
</g>
<!-- src/client/MatchMaking/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx -->
<g id="node133" class="node">
<title>src/client/MatchMaking/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx</title>
<g id="a_node133"><a xlink:href="src/client/MatchMaking/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx" xlink:title="MatchMakingConsoleEvents.tsx">
<path fill="#ccffcc" stroke="black" d="M1017.33,-2308.35C1017.33,-2308.35 883.17,-2308.35 883.17,-2308.35 880.08,-2308.35 877,-2305.27 877,-2302.18 877,-2302.18 877,-2296.02 877,-2296.02 877,-2292.93 880.08,-2289.85 883.17,-2289.85 883.17,-2289.85 1017.33,-2289.85 1017.33,-2289.85 1020.42,-2289.85 1023.5,-2292.93 1023.5,-2296.02 1023.5,-2296.02 1023.5,-2302.18 1023.5,-2302.18 1023.5,-2305.27 1020.42,-2308.35 1017.33,-2308.35"/>
<text text-anchor="start" x="885" y="-2295.8" font-family="Helvetica,sans-Serif" font-size="9.00">MatchMakingConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="edge225" class="edge">
<title>src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1005.09,-891.43C1026.9,-896.92 1050.06,-907.14 1063.5,-926.1 1083.97,-954.97 1049.78,-1535.16 1071.5,-1563.1 1081.51,-1575.98 1098.19,-1581.84 1113.91,-1584.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1113.28,-1586.38 1119.49,-1585.03 1113.79,-1582.21 1113.28,-1586.38"/>
</g>
<!-- src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge226" class="edge">
<title>src/client/Shared/infrastructure/components/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1005.11,-891.41C1026.92,-896.91 1050.08,-907.13 1063.5,-926.1 1075.8,-943.5 1058.44,-1676.26 1071.5,-1693.1 1081.03,-1705.38 1096.62,-1711.28 1111.67,-1713.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1111.19,-1716.03 1117.42,-1714.79 1111.77,-1711.87 1111.19,-1716.03"/>
</g>
<!-- src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts -->
<g id="edge227" class="edge">
<title>src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M983.25,-955.77C1009.94,-965.71 1046.28,-984.05 1063.5,-1014.1 1078.67,-1040.57 1052.75,-1539.04 1071.5,-1563.1 1081.53,-1575.97 1098.2,-1581.83 1113.92,-1584.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1113.29,-1586.37 1119.5,-1585.02 1113.81,-1582.2 1113.29,-1586.37"/>
</g>
<!-- src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge228" class="edge">
<title>src/client/Shared/infrastructure/components/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/Shared/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M983.27,-955.76C1009.97,-965.69 1046.32,-984.03 1063.5,-1014.1 1072.86,-1030.48 1059.93,-1678.2 1071.5,-1693.1 1081.04,-1705.38 1096.63,-1711.28 1111.68,-1713.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1111.19,-1716.02 1117.43,-1714.78 1111.78,-1711.86 1111.19,-1716.02"/>
</g>
<!-- src/client/Shared/infrastructure/components/JsonObjectViewer/JsonObjectViewer.tsx -->
<g id="node137" class="node">
<title>src/client/Shared/infrastructure/components/JsonObjectViewer/JsonObjectViewer.tsx</title>
<g id="a_node137"><a xlink:href="src/client/Shared/infrastructure/components/JsonObjectViewer/JsonObjectViewer.tsx" xlink:title="JsonObjectViewer.tsx">
<path fill="#ccffcc" stroke="black" d="M995.58,-1043.35C995.58,-1043.35 904.92,-1043.35 904.92,-1043.35 901.83,-1043.35 898.75,-1040.27 898.75,-1037.18 898.75,-1037.18 898.75,-1031.02 898.75,-1031.02 898.75,-1027.93 901.83,-1024.85 904.92,-1024.85 904.92,-1024.85 995.58,-1024.85 995.58,-1024.85 998.67,-1024.85 1001.75,-1027.93 1001.75,-1031.02 1001.75,-1031.02 1001.75,-1037.18 1001.75,-1037.18 1001.75,-1040.27 998.67,-1043.35 995.58,-1043.35"/>
<text text-anchor="start" x="906.75" y="-1030.8" font-family="Helvetica,sans-Serif" font-size="9.00">JsonObjectViewer.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/components/SkeletonHelper/SkeletonHelper.tsx -->
<g id="node141" class="node">
<title>src/client/Shared/infrastructure/components/SkeletonHelper/SkeletonHelper.tsx</title>
<g id="a_node141"><a xlink:href="src/client/Shared/infrastructure/components/SkeletonHelper/SkeletonHelper.tsx" xlink:title="SkeletonHelper.tsx">
<path fill="#ccffcc" stroke="black" d="M990.33,-1226.35C990.33,-1226.35 910.17,-1226.35 910.17,-1226.35 907.08,-1226.35 904,-1223.27 904,-1220.18 904,-1220.18 904,-1214.02 904,-1214.02 904,-1210.93 907.08,-1207.85 910.17,-1207.85 910.17,-1207.85 990.33,-1207.85 990.33,-1207.85 993.42,-1207.85 996.5,-1210.93 996.5,-1214.02 996.5,-1214.02 996.5,-1220.18 996.5,-1220.18 996.5,-1223.27 993.42,-1226.35 990.33,-1226.35"/>
<text text-anchor="start" x="912" y="-1213.8" font-family="Helvetica,sans-Serif" font-size="9.00">SkeletonHelper.tsx</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/hooks/useDebounce/index.ts -->
<g id="node142" class="node">
<title>src/client/Shared/infrastructure/hooks/useDebounce/index.ts</title>
<g id="a_node142"><a xlink:href="src/client/Shared/infrastructure/hooks/useDebounce/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M1175.46,-1663.35C1175.46,-1663.35 1133.79,-1663.35 1133.79,-1663.35 1130.71,-1663.35 1127.62,-1660.27 1127.62,-1657.18 1127.62,-1657.18 1127.62,-1651.02 1127.62,-1651.02 1127.62,-1647.93 1130.71,-1644.85 1133.79,-1644.85 1133.79,-1644.85 1175.46,-1644.85 1175.46,-1644.85 1178.54,-1644.85 1181.62,-1647.93 1181.62,-1651.02 1181.62,-1651.02 1181.62,-1657.18 1181.62,-1657.18 1181.62,-1660.27 1178.54,-1663.35 1175.46,-1663.35"/>
<text text-anchor="start" x="1138.88" y="-1650.8" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="edge234" class="edge">
<title>src/client/Shared/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/Shared/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1181.78,-1654.1C1207.28,-1654.1 1246.67,-1654.1 1277.74,-1654.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1283.64,-1656.2 1277.64,-1654.1 1283.64,-1652 1283.64,-1656.2"/>
</g>
<!-- src/client/Shared/infrastructure/lib/groupByDataProperty.ts -->
<g id="node144" class="node">
<title>src/client/Shared/infrastructure/lib/groupByDataProperty.ts</title>
<g id="a_node144"><a xlink:href="src/client/Shared/infrastructure/lib/groupByDataProperty.ts" xlink:title="groupByDataProperty.ts">
<path fill="#ddfeff" stroke="black" d="M1204.83,-1533.35C1204.83,-1533.35 1104.42,-1533.35 1104.42,-1533.35 1101.33,-1533.35 1098.25,-1530.27 1098.25,-1527.18 1098.25,-1527.18 1098.25,-1521.02 1098.25,-1521.02 1098.25,-1517.93 1101.33,-1514.85 1104.42,-1514.85 1104.42,-1514.85 1204.83,-1514.85 1204.83,-1514.85 1207.92,-1514.85 1211,-1517.93 1211,-1521.02 1211,-1521.02 1211,-1527.18 1211,-1527.18 1211,-1530.27 1207.92,-1533.35 1204.83,-1533.35"/>
<text text-anchor="start" x="1106.25" y="-1520.8" font-family="Helvetica,sans-Serif" font-size="9.00">groupByDataProperty.ts</text>
</a>
</g>
</g>
<!-- src/client/Shared/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts -->
<g id="edge237" class="edge">
<title>src/client/Shared/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/DeckBuilding/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1196.65,-1533.71C1213.19,-1539.82 1230.63,-1549.6 1240.38,-1565.1 1252.87,-1584.98 1237.33,-4925.38 1248.38,-4946.1 1345.11,-5127.6 1514.65,-5296.33 1662.25,-5153.1 1672.86,-5142.8 1668.39,-4901.77 1670.25,-4887.1 1677.85,-4827.26 1696.36,-4758.04 1705.48,-4726.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1707.41,-4726.94 1707.05,-4720.59 1703.37,-4725.78 1707.41,-4726.94"/>
</g>
<!-- src/client/Shared/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/Shared/application/services/LocationService.ts -->
<g id="edge243" class="edge">
<title>src/client/Shared/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/Shared/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1339.08,-1907.75C1364.88,-1929.04 1426.86,-1980.16 1457.99,-2005.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1456.55,-2007.37 1462.52,-2009.57 1459.23,-2004.13 1456.55,-2007.37"/>
</g>
</g>
</svg>
    <script>
      var gMode = new Mode();

var title2ElementMap = (function makeElementMap() {
  /** @type {NodeListOf<SVGGElement>} */
  var nodes = document.querySelectorAll(".node");
  /** @type {NodeListOf<SVGGElement>} */
  var edges = document.querySelectorAll(".edge");
  return new Title2ElementMap(edges, nodes);
})();

function getHoverHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function hoverHighlightHandler(pMouseEvent) {
    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (
      currentHighlightedTitle !== closestTitleText &&
      gMode.get() === gMode.HOVER
    ) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}

function getSelectHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function selectHighlightHandler(pMouseEvent) {
    pMouseEvent.preventDefault();

    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (closestNodeOrEdge) {
      gMode.setToSelect();
    } else {
      gMode.setToHover();
    }
    if (currentHighlightedTitle !== closestTitleText) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}
function Mode() {
  var HOVER = 1;
  var SELECT = 2;

  function setToHover() {
    this._mode = HOVER;
  }
  function setToSelect() {
    this._mode = SELECT;
  }

  /**
   * @returns {number}
   */
  function get() {
    return this._mode || HOVER;
  }

  return {
    HOVER: HOVER,
    SELECT: SELECT,
    setToHover: setToHover,
    setToSelect: setToSelect,
    get: get,
  };
}

/**
 *
 * @param {SVGGelement[]} pEdges
 * @param {SVGGElement[]} pNodes
 * @return {{get: (pTitleText:string) => SVGGElement[]}}
 */
function Title2ElementMap(pEdges, pNodes) {
  /* {{[key: string]: SVGGElement[]}} */
  var elementMap = buildMap(pEdges, pNodes);

  /**
   * @param {NodeListOf<SVGGElement>} pEdges
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement[]}}
   */
  function buildMap(pEdges, pNodes) {
    var title2NodeMap = buildTitle2NodeMap(pNodes);

    return nodeListToArray(pEdges).reduce(addEdgeToMap(title2NodeMap), {});
  }
  /**
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement}}
   */
  function buildTitle2NodeMap(pNodes) {
    return nodeListToArray(pNodes).reduce(addNodeToMap, {});
  }

  function addNodeToMap(pMap, pNode) {
    var titleText = getTitleText(pNode);

    if (titleText) {
      pMap[titleText] = pNode;
    }
    return pMap;
  }

  function addEdgeToMap(pNodeMap) {
    return function (pEdgeMap, pEdge) {
      /** @type {string} */
      var titleText = getTitleText(pEdge);

      if (titleText) {
        var edge = pryEdgeFromTitle(titleText);

        pEdgeMap[titleText] = [pNodeMap[edge.from], pNodeMap[edge.to]];
        (pEdgeMap[edge.from] || (pEdgeMap[edge.from] = [])).push(pEdge);
        (pEdgeMap[edge.to] || (pEdgeMap[edge.to] = [])).push(pEdge);
      }
      return pEdgeMap;
    };
  }

  /**
   *
   * @param {string} pString
   * @return {{from?: string; to?:string;}}
   */
  function pryEdgeFromTitle(pString) {
    var nodeNames = pString.split(/\s*->\s*/);

    return {
      from: nodeNames.shift(),
      to: nodeNames.shift(),
    };
  }
  /**
   *
   * @param {string} pTitleText
   * @return {SVGGElement[]}
   */
  function get(pTitleText) {
    return (pTitleText && elementMap[pTitleText]) || [];
  }
  return {
    get: get,
  };
}

/**
 * @param {SVGGElement} pGElement
 * @return {string?}
 */
function getTitleText(pGElement) {
  /** @type {SVGTitleElement} */
  var title = pGElement && pGElement.querySelector("title");
  /** @type {string} */
  var titleText = title && title.textContent;

  if (titleText) {
    titleText = titleText.trim();
  }
  return titleText;
}

/**
 * @param {NodeListOf<Element>} pNodeList
 * @return {Element[]}
 */
function nodeListToArray(pNodeList) {
  var lReturnValue = [];

  pNodeList.forEach(function (pElement) {
    lReturnValue.push(pElement);
  });

  return lReturnValue;
}

function resetNodesAndEdges() {
  nodeListToArray(document.querySelectorAll(".current")).forEach(
    removeHighlight,
  );
}

/**
 * @param {SVGGElement} pGElement
 */
function removeHighlight(pGElement) {
  if (pGElement && pGElement.classList) {
    pGElement.classList.remove("current");
  }
}

/**
 * @param {SVGGElement} pGroup
 */
function addHighlight(pGroup) {
  if (pGroup && pGroup.classList) {
    pGroup.classList.add("current");
  }
}

var gHints = {
  HIDDEN: 1,
  SHOWN: 2,
  state: 1, // === HIDDEN
  show: function () {
    document.getElementById("hints").removeAttribute("style");
    gHints.state = gHints.SHOWN;
  },
  hide: function () {
    document.getElementById("hints").style = "display:none";
    gHints.state = gHints.HIDDEN;
  },
  toggle: function () {
    if ((gHints.state || gHints.HIDDEN) === gHints.HIDDEN) {
      gHints.show();
    } else {
      gHints.hide();
    }
  },
};

/** @param {KeyboardEvent} pKeyboardEvent */
function keyboardEventHandler(pKeyboardEvent) {
  if (pKeyboardEvent.key === "Escape") {
    resetNodesAndEdges();
    gMode.setToHover();
    gHints.hide();
  }
  if (pKeyboardEvent.key === "F1") {
    pKeyboardEvent.preventDefault();
    gHints.toggle();
  }
}

document.addEventListener("contextmenu", getSelectHandler(title2ElementMap));
document.addEventListener("mouseover", getHoverHandler(title2ElementMap));
document.addEventListener("keydown", keyboardEventHandler);
document.getElementById("close-hints").addEventListener("click", gHints.hide);
document.getElementById("button_help").addEventListener("click", gHints.toggle);
document.querySelector("svg").insertAdjacentHTML(
  "afterbegin",
  `<linearGradient id="edgeGradient">
      <stop offset="0%" stop-color="fuchsia"/>
      <stop offset="100%" stop-color="purple"/>
   </linearGradient>
  `,
);

// Add a small increment to the last value of the path to make gradients on
// horizontal paths work. Without them all browsers I tested with (firefox,
// chrome) do not render the gradient, but instead make the line transparent
// (or the color of the background, I haven't looked into it that deeply,
// but for the hack it doesn't matter which).
function skewLineABit(lDrawingInstructions) {
  var lLastValue = lDrawingInstructions.match(/(\d+\.?\d*)$/)[0];
  // Smaller values than .001 _should_ work as well, but don't in all
  // cases. Even this value is so small that it is not visible to the
  // human eye (tested with the two I have at my disposal).
  var lIncrement = 0.001;
  var lNewLastValue = parseFloat(lLastValue) + lIncrement;

  return lDrawingInstructions.replace(lLastValue, lNewLastValue);
}

nodeListToArray(document.querySelectorAll("path"))
  .filter(function (pElement) {
    return pElement.parentElement.classList.contains("edge");
  })
  .forEach(function (pElement) {
    pElement.attributes.d.value = skewLineABit(pElement.attributes.d.value);
  });

    </script>
  </body>
</html>
