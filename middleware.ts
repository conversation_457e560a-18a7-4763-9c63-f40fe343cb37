import createIntlMiddleware from "next-intl/middleware";
import {routing} from "./i18n/routing";
import {
  convexAuthNextjsMiddleware,
  createRouteMatcher,
  nextjsMiddlewareRedirect,
} from "@convex-dev/auth/nextjs/server";
import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {buildAccessDeniedUrl, buildGameListUrl, buildSignInUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";
import {GetCurrentUserViewModel} from "@/src/server/Authentication/presentation/viewModels/GetCurrentUserViewModel";

const WHITELISTED_USERS = new Set([
  "<EMAIL>",
  "<EMAIL>",
]);

const isSignInPage = createRouteMatcher(["/(en|fr)/signin"]);
const isAccessDeniedPage = createRouteMatcher(["/(en|fr)/access-denied"]);

const intlMiddleware = createIntlMiddleware(routing);

function isWhiteListed(user: GetCurrentUserViewModel['data']) {
  return user !== null && user.email && WHITELISTED_USERS.has(user.email);
}

const authMiddleware = convexAuthNextjsMiddleware(
  async (request, {convexAuth}) => {
    const locale = request.nextUrl.pathname.split("/")[1];

    if (locale !== "en" && locale !== "fr") {
      return nextjsMiddlewareRedirect(request, buildGameListUrl("en"));
    }

    if (request.nextUrl.pathname === "/en" || request.nextUrl.pathname === "/fr") {
      return nextjsMiddlewareRedirect(
        request,
        buildGameListUrl(locale)
      );
    }

    const isAuthenticated = await convexAuth.isAuthenticated();

    if (isSignInPage(request)) {
      return intlMiddleware(request);
    }

    if (isAccessDeniedPage(request)) {
      if (isAuthenticated) {
        try {
          const token = await convexAuth.getToken();
          const user = await fetchQuery(api.queries.getCurrentUser.endpoint, {}, {token});

          if (isWhiteListed(user.data)) {
            return nextjsMiddlewareRedirect(
              request,
              buildGameListUrl(locale)
            );
          }
        } catch (error) {
          console.error("Error fetching user:", error);
        }
      }
      return intlMiddleware(request);
    }

    if (!isAuthenticated) {
      return nextjsMiddlewareRedirect(request, buildSignInUrl(locale));
    }

    try {
      const token = await convexAuth.getToken();
      const user = await fetchQuery(api.queries.getCurrentUser.endpoint, {}, {token});
      if (!isWhiteListed(user.data)) {
        return nextjsMiddlewareRedirect(request, buildAccessDeniedUrl(locale));
      }
    } catch (error) {
      console.error("Error fetching user:", error);
      return nextjsMiddlewareRedirect(request, buildSignInUrl(locale));
    }

    return intlMiddleware(request);
  }
);

export default authMiddleware;

export const config = {
  matcher: [
    "/",
    "/(fr|en)/:path*",
    "/((?!.*\\..*|_next).*)",
    "/(api|trpc)(.*)",
  ],
};
