import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {UpdateGameCommand} from './UpdateGameCommand';

export class UpdateGameCommandHandler {
  constructor(private readonly repository: GameRepository) {}

  async handle({gameId, userId, name}: UpdateGameCommand) {
    const game = await this.repository.findById(gameId);
    if (!game) return;
    if (!game.isOwnedBy(userId)) {
      throw new GameNotOwnedError();
    }
    game.renameTo(name);
    await this.repository.save(game);
  }
}
