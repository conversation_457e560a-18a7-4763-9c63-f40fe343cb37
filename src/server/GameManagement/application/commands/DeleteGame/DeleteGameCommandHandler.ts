import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {DeleteGameCommand} from './DeleteGameCommand';

export class DeleteGameCommandHandler {
  constructor(private readonly repository: GameRepository) {}

  async handle({gameId, userId}: DeleteGameCommand) {
    const game = await this.repository.findById(gameId);
    if (!game) return;
    if (!game.isOwnedBy(userId)) {
      throw new GameNotOwnedError();
    }
    await this.repository.delete(gameId);
  }
}
