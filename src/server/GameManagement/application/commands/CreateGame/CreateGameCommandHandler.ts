import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {Game} from '@/src/server/GameManagement/domain/Game/Game';
import {CreateGameCommand} from './CreateGameCommand';

export class CreateGameCommandHandler {
  constructor(private readonly repository: GameRepository) {}

  handle(command: CreateGameCommand) {
    return this.repository.save(Game.create({ownerId: command.ownerId, name: command.name}));
  }
}
