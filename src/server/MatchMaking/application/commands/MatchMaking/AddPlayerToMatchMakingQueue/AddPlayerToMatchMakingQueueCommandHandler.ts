import {EventBus} from "@/src/server/Shared/application/ports/EventBus";
import {AddPlayerToMatchMakingQueueCommand} from "./AddPlayerToMatchMakingQueueCommand";
import {MatchmakingQueueRepository} from "@/src/server/MatchMaking/application/ports/MatchmakingQueueRepository";
import {MatchmakingQueueItem} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem";
import {TimeService} from "@/src/server/Shared/application/ports/TimeService";
export class AddPlayerToMatchMakingQueueCommandHandler {
  private readonly eventBus: EventBus
  private readonly repository: MatchmakingQueueRepository
  private readonly time: TimeService
  constructor(eventBus: EventBus, repository: MatchmakingQueueRepository, time: TimeService) {
    this.eventBus = eventBus
    this.repository = repository
    this.time = time
  }

  async handle({gameId, deckId, userId}: AddPlayerToMatchMakingQueueCommand) {
    const queue = await this.repository.findByGameId(gameId);
    if (queue.hasPlayer(userId)) return;

    const queueId = await this.repository.save(
      MatchmakingQueueItem.create({
        gameId,
        deckId,
        playerId: userId,
        queuedAt: this.time.now(),
      })
    );

    await this.eventBus.dispatchMatchmakingEvent(gameId, queueId, {
      type: "PlayerAddedToMatchMakingQueue",
      payload: {
        playerId: userId,
        gameId,
        deckId,
        queueId,
      },
    });
  }
}
