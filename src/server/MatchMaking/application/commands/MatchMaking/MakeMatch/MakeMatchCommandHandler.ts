import {EventBus} from "@/src/server/Shared/application/ports/EventBus";
import {MakeMatchCommand} from "./MakeMatchCommand";
import {MatchmakingQueueRepository} from "@/src/server/MatchMaking/application/ports/MatchmakingQueueRepository";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {Match} from "@/src/server/Gaming/domain/Match/Match";

export class MakeMatchCommandHandler {
  private readonly eventBus: EventBus;
  private readonly queueRepository: MatchmakingQueueRepository;
  private readonly matchRepository: MatchRepository;

  constructor(
    eventBus: EventBus,
    queueRepository: MatchmakingQueueRepository,
    matchRepository: MatchRepository
  ) {
    this.eventBus = eventBus;
    this.queueRepository = queueRepository;
    this.matchRepository = matchRepository;
  }

  async handle({gameId, playerId, queueId}: MakeMatchCommand) {
    const queue = await this.queueRepository.findByGameId(gameId);

    const availableOpponents = queue.findOpponentsOf(playerId);
    if (availableOpponents.length === 0) return;

    const opponent = availableOpponents[0];
    const players = [opponent.getPlayerId(), playerId];

    const matchId = await this.matchRepository.save(
      Match.create({
        gameId,
        players,
        status: 'setup',
      })
    );

    await this.eventBus.dispatchMatchmakingEvent(gameId, queueId, {
      type: "MatchCreated",
      payload: {
        matchId,
        players,
      },
    });
  }
}