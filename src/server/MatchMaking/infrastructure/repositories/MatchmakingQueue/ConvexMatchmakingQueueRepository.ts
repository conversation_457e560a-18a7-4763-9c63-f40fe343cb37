import {GenericMutationCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {getManyFrom} from "convex-helpers/server/relationships";
import {MatchmakingQueueRepository} from "@/src/server/MatchMaking/application/ports/MatchmakingQueueRepository";
import {MatchmakingQueueItem} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem";
import {MatchmakingQueue} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue";

export class ConvexMatchmakingQueueRepository implements MatchmakingQueueRepository {
  constructor(private readonly ctx: GenericMutationCtx<DataModel>) {}

  async findByGameId(gameId: string): Promise<MatchmakingQueue> {
    const docs = await getManyFrom(this.ctx.db, 'matchmakingQueue', 'by_gameId', gameId as Id<'games'>);
    const items = docs.map(d => MatchmakingQueueItem.fromSnapshot({ id: d._id, ...d }));
    return MatchmakingQueue.fromItems(items);
  }

  async save(item: MatchmakingQueueItem) {
    const data = item.toSnapshot();
    const id = await this.ctx.db.insert('matchmakingQueue', {
      ...data,
      gameId: data.gameId as Id<'games'>,
      deckId: data.deckId as Id<'decks'>,
    });
    return id;
  }

  async remove(id: string) {
    await this.ctx.db.delete(id as Id<'matchmakingQueue'>);
  }
}
