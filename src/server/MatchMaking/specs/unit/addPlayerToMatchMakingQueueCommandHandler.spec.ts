import {describe, it, expect, beforeEach} from 'vitest';
import {AddPlayerToMatchMakingQueueCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler";
import {InMemoryMatchmakingQueueRepository} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/InMemoryMatchmakingQueueRepository";
import {AddPlayerToMatchMakingQueueCommand} from "@/src/server/MatchMaking/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand";
import {createFakeEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/FakeEventBus";
import {FakeTimeService} from "@/src/server/Shared/infrastructure/providers/Time/FakeTimeService";

let eventBus: ReturnType<typeof createFakeEventBus>;
let repository: InMemoryMatchmakingQueueRepository;
let handler: AddPlayerToMatchMakingQueueCommandHandler;
let time: FakeTimeService;

beforeEach(() => {
  eventBus = createFakeEventBus();
  repository = new InMemoryMatchmakingQueueRepository();
  time = new FakeTimeService();
  handler = new AddPlayerToMatchMakingQueueCommandHandler(eventBus, repository, time);
});

describe('AddPlayerToMatchMakingQueueCommandHandler', () => {
  describe('When the user is not already in the match making queue', () => {
    it('should add the user with his deck to the queue', async () => {
      // Arrange
      const command: AddPlayerToMatchMakingQueueCommand = {
        gameId: 'game1',
        deckId: 'deck1',
        userId: 'user1'
      };

      // Act
      await handler.handle(command);

      // Assert
      const queue = await repository.findByGameId('game1');
      const items = queue.toItems();
      expect(items).toHaveLength(1);
      expect(items[0].belongsTo('user1')).toBe(true);
    });
  });

  describe('When the user is already in the match making queue', () => {
    it('should not add the user twice', async () => {
      // Arrange
      const command: AddPlayerToMatchMakingQueueCommand = {
        gameId: 'game1',
        deckId: 'deck1',
        userId: 'user1'
      };
      await handler.handle(command);

      // Act
      await handler.handle(command);

      // Assert
      const queue = await repository.findByGameId('game1');
      const items = queue.toItems();
      expect(items).toHaveLength(1);
    });
  });
});
