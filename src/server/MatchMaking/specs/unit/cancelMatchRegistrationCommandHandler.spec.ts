import {describe, it, expect, beforeEach} from 'vitest';
import {CancelMatchRegistrationCommandHandler} from '@/src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler';
import {InMemoryMatchmakingQueueRepository} from '@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/InMemoryMatchmakingQueueRepository';
import {MatchmakingQueueItem} from '@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem';
import {createFakeEventBus} from '@/src/server/Shared/infrastructure/gateways/Context/FakeEventBus';
import {CancelMatchRegistrationCommand} from '@/src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand';

let eventBus: ReturnType<typeof createFakeEventBus>;
let repository: InMemoryMatchmakingQueueRepository;
let handler: CancelMatchRegistrationCommandHandler;

beforeEach(() => {
  eventBus = createFakeEventBus();
  repository = new InMemoryMatchmakingQueueRepository();
  handler = new CancelMatchRegistrationCommandHandler(eventBus, repository);
});

describe('CancelMatchRegistrationCommandHandler', () => {
  describe('When the user is in the queue', () => {
    it('should remove him and dispatch events', async () => {
      // Arrange
      const item = MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd1', playerId: 'u1', queuedAt: Date.now()});
      await repository.save(item);

      const command: CancelMatchRegistrationCommand = {gameId: 'g1', userId: 'u1'};

      // Act
      await handler.handle(command);

      // Assert
      const queue = await repository.findByGameId('g1');
      expect(queue.toItems()).toHaveLength(0);
      expect(eventBus.recorded).toHaveLength(2);
      expect(eventBus.recorded[0].event.type).toBe('PlayerRemovedFromQueue');
      expect(eventBus.recorded[1].event.type).toBe('PlayerRegistrationCancelled');
    });
  });

  describe('When the user is not in the queue', () => {
    it('should do nothing', async () => {
      // Arrange
      const command: CancelMatchRegistrationCommand = {gameId: 'g1', userId: 'u1'};

      // Act
      await handler.handle(command);

      // Assert
      const queue = await repository.findByGameId('g1');
      expect(queue.toItems()).toHaveLength(0);
      expect(eventBus.recorded).toHaveLength(0);
    });
  });
});
