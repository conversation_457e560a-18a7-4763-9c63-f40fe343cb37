import {LoadGameSettingsByGameIdWebPresenter} from '@/src/server/Gaming/presentation/presenters/LoadGameSettingsByGameIdWebPresenter';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

describe('LoadGameSettingsByGameIdWebPresenter', () => {
  let presenter: LoadGameSettingsByGameIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadGameSettingsByGameIdWebPresenter();
  });

  describe('When displaying settings', () => {
    it('should set game settings data', () => {
      // Arrange
      const settings = GameSettings.fromSnapshot({maxCardsInDeck: 60});

      // Act
      presenter.display(settings);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({maxCardsInDeck: 60});
    });
  });

  describe('When displaying null settings', () => {
    it('should set data to null', () => {
      // Arrange
      // Act
      presenter.display(null);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toBeNull();
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
