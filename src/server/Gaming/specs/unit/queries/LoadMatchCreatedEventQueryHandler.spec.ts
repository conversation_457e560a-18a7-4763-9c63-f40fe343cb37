import {beforeEach, describe, expect, it} from 'vitest';
import {LoadMatchCreatedEventQueryHandler} from '@/src/server/Gaming/application/queries/LoadMatchCreatedEvent/LoadMatchCreatedEventQueryHandler';
import {InMemoryMatchMakingEventRepository} from '@/src/server/Gaming/infrastructure/repositories/MatchMakingEvent/InMemoryMatchMakingEventRepository';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadMatchCreatedEventPresenter} from '@/src/server/Gaming/application/ports/LoadMatchCreatedEventPresenter';
import {InMemoryFailingMatchMakingEventRepository} from '@/src/server/Gaming/infrastructure/repositories/MatchMakingEvent/InMemoryFailingMatchMakingEventRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {MatchCreatedEvent} from '@/src/server/Gaming/domain/Match/MatchCreatedEvent';

describe('LoadMatchCreatedEventQueryHandler', () => {
  describe('When retrieving events successfully', () => {
    let eventRepository: InMemoryMatchMakingEventRepository;
    let matchRepository: InMemoryMatchRepository;
    let presenter: MockProxy<LoadMatchCreatedEventPresenter>;
    let handler: LoadMatchCreatedEventQueryHandler;

    beforeEach(() => {
      eventRepository = new InMemoryMatchMakingEventRepository();
      matchRepository = new InMemoryMatchRepository();
      presenter = mock<LoadMatchCreatedEventPresenter>();
      handler = new LoadMatchCreatedEventQueryHandler(eventRepository, matchRepository);
    });

    describe('When an event exists for the user and the match is ongoing', () => {
      it('should display it', async () => {
        // Arrange
        const gameId = 'g1';
        const event: MatchCreatedEvent = {type: 'MatchCreated', payload: {matchId: 'm1', players: ['p1', 'p2']}, occurredAt: Date.now()};
        eventRepository.addEvent(gameId, event);
        await matchRepository.save(Match.create({gameId, players: ['p1', 'p2'], status: 'ongoing'}));

        // Act
        await handler.handle({gameId, userId: 'p1'}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(event);
      });
    });

    describe('When no event exists for the user', () => {
      it('should notify no match found for the user', async () => {
        // Arrange
        const gameId = 'g1';
        const event: MatchCreatedEvent = {type: 'MatchCreated', payload: {matchId: 'm1', players: ['p2', 'p3']}, occurredAt: Date.now()};
        eventRepository.addEvent(gameId, event);

        // Act
        await handler.handle({gameId, userId: 'p1'}, presenter);

        // Assert
        expect(presenter.notifyNoMatchFoundForUser).toHaveBeenCalled();
      });
    });

    describe('When the match is finished', () => {
      it('should notify the match is already finished', async () => {
        // Arrange
        const gameId = 'g1';
        const event: MatchCreatedEvent = {type: 'MatchCreated', payload: {matchId: 'm1', players: ['p1', 'p2']}, occurredAt: Date.now()};
        eventRepository.addEvent(gameId, event);
        const match = Match.create({gameId, players: ['p1', 'p2'], status: 'finished'});
        await matchRepository.save(match);

        // Act
        await handler.handle({gameId, userId: 'p1'}, presenter);

        // Assert
        expect(presenter.notifyMatchAlreadyFinished).toHaveBeenCalled();
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const eventRepository = new InMemoryFailingMatchMakingEventRepository();
      const matchRepository = new InMemoryMatchRepository();
      const presenter = mock<LoadMatchCreatedEventPresenter>();
      const handler = new LoadMatchCreatedEventQueryHandler(eventRepository, matchRepository);

      // Act
      await handler.handle({gameId: 'g1', userId: 'p1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('g1'));
    });
  });
});
