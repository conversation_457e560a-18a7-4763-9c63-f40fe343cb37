import {beforeEach, describe, expect, it} from 'vitest';
import {
  LoadGameFilterListQueryHandler
} from '@/src/server/Gaming/application/queries/LoadGameFilterList/LoadGameFilterListQueryHandler';
import {
  InMemoryGameFilterListRepository
} from '@/src/server/Gaming/infrastructure/repositories/GameFilterList/InMemoryGameFilterListRepository';
import {GameFilter} from '@/src/server/Gaming/domain/GameFilter/GameFilter';
import {mock, MockProxy} from "vitest-mock-extended";
import {LoadGameFilterListPresenter} from "@/src/server/Gaming/application/ports/LoadGameFilterListPresenter";
import {
  InMemoryFailingGameFilterListRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameFilterList/InMemoryFailingGameFilterListRepository";
import {AMBER_FILTER, INKABLE_FILTER} from "@/src/server/Shared/specs/helpers/fakes/fakeGameFilters";

describe('LoadGameFilterListQueryHandler', () => {
  describe('When retrieving filters successfully', () => {
    let repository: InMemoryGameFilterListRepository;
    let presenter: MockProxy<LoadGameFilterListPresenter>;
    let handler: LoadGameFilterListQueryHandler;

    beforeEach(() => {
      repository = new InMemoryGameFilterListRepository();
      presenter = mock<LoadGameFilterListPresenter>();
      handler = new LoadGameFilterListQueryHandler(repository);
    });

    describe('When filters exist for the game', () => {
      it('should display them', async () => {
        // Arrange
        const gameId = 'game-123';
        const filters: GameFilter[] = [
          {...AMBER_FILTER, order: 2},
          {...INKABLE_FILTER, order: 1},

        ];
        repository.addFiltersForGame(gameId, filters);

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          gameFilters: [
            {...INKABLE_FILTER, order: 1},
            {...AMBER_FILTER, order: 2},
          ],
        });
      });
    });

    describe('When no filters exist for the game', () => {
      it('should return an empty list', async () => {
        // Arrange
        const gameId = 'game-123';

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          gameFilters: [],
        });
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingGameFilterListRepository();
      const presenter = mock<LoadGameFilterListPresenter>();
      const handler = new LoadGameFilterListQueryHandler(repository);

      // Act
      await handler.handle({gameId: 'game-123'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('game-123'));
    });
  });
});
