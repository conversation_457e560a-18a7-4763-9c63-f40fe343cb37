import {beforeEach, describe, expect, it} from 'vitest';
import {LoadMatchEventsQueryHandler} from '@/src/server/Gaming/application/queries/LoadMatchEvents/LoadMatchEventsQueryHandler';
import {InMemoryMatchEventListRepository} from '@/src/server/Gaming/infrastructure/repositories/MatchEventList/InMemoryMatchEventListRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadMatchEventsPresenter} from '@/src/server/Gaming/application/ports/LoadMatchEventsPresenter';
import {InMemoryFailingMatchEventListRepository} from '@/src/server/Gaming/infrastructure/repositories/MatchEventList/InMemoryFailingMatchEventListRepository';
import {MatchEvent} from '@/src/server/Gaming/domain/MatchEvent/MatchEvent';

describe('LoadMatchEventsQueryHandler', () => {
  describe('When retrieving events successfully', () => {
    let repository: InMemoryMatchEventListRepository;
    let presenter: MockProxy<LoadMatchEventsPresenter>;
    let handler: LoadMatchEventsQueryHandler;

    beforeEach(() => {
      repository = new InMemoryMatchEventListRepository();
      presenter = mock<LoadMatchEventsPresenter>();
      handler = new LoadMatchEventsQueryHandler(repository);
    });

    describe('When events exist for the match', () => {
      it('should display them ordered by occurredAt', async () => {
        // Arrange
        const matchId = 'm1';
        const events: MatchEvent[] = [
          {id: '2', type: 'Ended', payload: {}, occurredAt: 2},
          {id: '1', type: 'Started', payload: {}, occurredAt: 1},
        ];
        repository.addEventsForMatch(matchId, events);

        // Act
        await handler.handle({matchId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          matchEvents: [
            {id: '1', type: 'Started', payload: {}, occurredAt: 1},
            {id: '2', type: 'Ended', payload: {}, occurredAt: 2},
          ],
        });
      });
    });

    describe('When no events exist for the match', () => {
      it('should return an empty list', async () => {
        // Arrange
        const matchId = 'm1';

        // Act
        await handler.handle({matchId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          matchEvents: [],
        });
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingMatchEventListRepository();
      const presenter = mock<LoadMatchEventsPresenter>();
      const handler = new LoadMatchEventsQueryHandler(repository);

      // Act
      await handler.handle({matchId: 'm1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('m1'));
    });
  });
});
