import {beforeEach, describe, expect, it} from 'vitest';
import {LoadGameSettingsByGameIdQueryHandler} from '@/src/server/Gaming/application/queries/LoadGameSettingsByGameId/LoadGameSettingsByGameIdQueryHandler';
import {InMemoryGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryGameSettingsRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadGameSettingsByGameIdPresenter} from '@/src/server/Gaming/application/ports/LoadGameSettingsByGameIdPresenter';
import {InMemoryFailingGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryFailingGameSettingsRepository';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

describe('LoadGameSettingsByGameIdQueryHandler', () => {
  describe('When retrieving settings successfully', () => {
    let repository: InMemoryGameSettingsRepository;
    let presenter: MockProxy<LoadGameSettingsByGameIdPresenter>;
    let handler: LoadGameSettingsByGameIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryGameSettingsRepository();
      presenter = mock<LoadGameSettingsByGameIdPresenter>();
      handler = new LoadGameSettingsByGameIdQueryHandler(repository);
    });

    describe('When settings exist for the game', () => {
      it('should display them', async () => {
        // Arrange
        const gameId = 'game-123';
        repository.addSettingsForGame(gameId, {maxCardsInDeck: 60});

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(GameSettings.fromSnapshot({maxCardsInDeck: 60}));
      });
    });

    describe('When settings do not exist for the game', () => {
      it('should display null', async () => {
        // Arrange
        const gameId = 'game-123';

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(null);
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingGameSettingsRepository();
      const presenter = mock<LoadGameSettingsByGameIdPresenter>();
      const handler = new LoadGameSettingsByGameIdQueryHandler(repository);

      // Act
      await handler.handle({gameId: 'game-123'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('game-123'));
    });
  });
});
