import {LoadMatchCreatedEventQuery} from "@/src/server/Gaming/application/queries/LoadMatchCreatedEvent/LoadMatchCreatedEventQuery";
import {MatchMakingEventRepository} from "@/src/server/Gaming/application/ports/MatchMakingEventRepository";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {LoadMatchCreatedEventPresenter} from "@/src/server/Gaming/application/ports/LoadMatchCreatedEventPresenter";

export class LoadMatchCreatedEventQueryHandler {
  constructor(
    private readonly eventRepository: MatchMakingEventRepository,
    private readonly matchRepository: MatchRepository,
  ) {}

  async handle({gameId, userId}: LoadMatchCreatedEventQuery, presenter: LoadMatchCreatedEventPresenter) {
    try {
      const events = await this.eventRepository.getByGameId(gameId, 'MatchCreated');
      const event = events.findMatchCreatedForUser(userId);

      if (!event) {
        presenter.notifyNoMatchFoundForUser();
        return;
      }

      const match = await this.matchRepository.findById(event.payload.matchId);
      if (match?.isFinished()) {
        presenter.notifyMatchAlreadyFinished();
        return;
      }

      presenter.display(event);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
