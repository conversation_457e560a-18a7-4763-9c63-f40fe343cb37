import {LoadMatchByIdQuery} from "@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQuery";
import {MatchReadRepository} from "@/src/server/Gaming/application/ports/MatchReadRepository";
import {LoadMatchByIdPresenter} from "@/src/server/Gaming/application/ports/LoadMatchByIdPresenter";

export class LoadMatchByIdQueryHandler {
  constructor(private readonly repository: MatchReadRepository) {}

  async handle({matchId, userId}: LoadMatchByIdQuery, presenter: LoadMatchByIdPresenter) {
    try {
      const match = await this.repository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }
      presenter.display(match, userId);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
