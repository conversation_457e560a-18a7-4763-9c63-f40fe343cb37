import {LoadGameListQuery} from "@/src/server/Gaming/application/queries/LoadGameList/LoadGameListQuery";
import {GameListRepository} from "@/src/server/Gaming/application/ports/GameListRepository";
import {LoadGameListPresenter} from "@/src/server/Gaming/application/ports/LoadGameListPresenter";

export class LoadGameListQueryHandler {
  constructor(private readonly repository: GameListRepository) {}

  async handle(_: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, presenter: LoadGameListPresenter) {
    try {
      const gameList = await this.repository.getAll();
      presenter.display(gameList);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
