import {LoadMatchEventsQuery} from "@/src/server/Gaming/application/queries/LoadMatchEvents/LoadMatchEventsQuery";
import {LoadMatchEventsPresenter} from "@/src/server/Gaming/application/ports/LoadMatchEventsPresenter";
import {MatchEventListRepository} from "@/src/server/Gaming/application/ports/MatchEventListRepository";

export class LoadMatchEventsQueryHandler {
  constructor(private readonly repository: MatchEventListRepository) {}

  async handle({matchId}: LoadMatchEventsQuery, presenter: LoadMatchEventsPresenter) {
    try {
      const events = await this.repository.getByMatchId(matchId);
      events.sortByOccurredAt();
      presenter.display(events);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
