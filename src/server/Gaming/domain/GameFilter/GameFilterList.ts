import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";

export class GameFilterList {
  public readonly gameFilters: GameFilter[];

  private constructor(gameFilters: GameFilter[]) {
    this.gameFilters = gameFilters;
  }

  static createFrom(gameFilters: GameFilter[]) {
    return new GameFilterList(gameFilters);
  }

  sortByOrderId() {
    this.gameFilters.sort((a, b) => a.order - b.order);
  }
}
