import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {Match} from "@/src/server/Gaming/domain/Match/Match";

export class ConvexMatchReadRepository implements MatchRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async save(): Promise<string> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Match | null> {
    const doc = await this.ctx.db.get(id as Id<'matches'>);
    return doc ? Match.fromSnapshot({id: doc._id, ...doc}) : null;
  }
}
