import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {GameRepository} from "@/src/server/Gaming/application/ports/GameRepository";
import {Game} from "@/src/server/Gaming/domain/Game/Game";

export class ConvexGameRepository implements GameRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getById(gameId: string): Promise<Game | undefined> {
    const game = await this.ctx.db.get(gameId as Id<"games">);
    return game ? Game.fromSnapshot({id: game._id, name: game.name}) : undefined;
  }
}
