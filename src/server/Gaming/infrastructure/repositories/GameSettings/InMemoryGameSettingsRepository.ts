import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

export class InMemoryGameSettingsRepository implements GameSettingsRepository {
  private settings: Map<string, GameSettings> = new Map();

  async getByGameId(gameId: string): Promise<GameSettings | null> {
    return this.settings.get(gameId) || null;
  }

  addSettingsForGame(gameId: string, snapshot: {maxCardsInDeck: number}): void {
    this.settings.set(gameId, GameSettings.fromSnapshot(snapshot));
  }

  clear(): void {
    this.settings.clear();
  }
}
