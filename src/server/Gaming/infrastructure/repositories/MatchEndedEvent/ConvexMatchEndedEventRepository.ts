import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {MatchEndedEventRepository} from "@/src/server/Gaming/application/ports/MatchEndedEventRepository";
import {MatchEndedEvent} from "@/src/server/Gaming/domain/MatchEvent/MatchEndedEvent";

export class ConvexMatchEndedEventRepository implements MatchEndedEventRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getByMatchId(matchId: string): Promise<MatchEndedEvent | null> {
    const event = await this.ctx.db
      .query("matchEvents")
      .withIndex('by_matchId_and_type', (q) => q.eq("matchId", matchId as Id<'matches'>).eq("type", "MatchEnded"))
      .first();
    return event ? event.payload as MatchEndedEvent : null;
  }
}
