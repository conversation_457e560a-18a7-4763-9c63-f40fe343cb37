import {beforeEach, describe, expect, it} from 'vitest';
import {LoadDeckBuilderSettingsByIdQueryHandler} from '@/src/server/DeckBuilding/application/queries/LoadDeckBuilderSettingsById/LoadDeckBuilderSettingsByIdQueryHandler';
import {InMemoryDeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/DeckBuilderSettings/InMemoryDeckBuilderSettingsRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadDeckBuilderSettingsByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckBuilderSettingsByIdPresenter';
import {InMemoryFailingDeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/DeckBuilderSettings/InMemoryFailingDeckBuilderSettingsRepository';
import {DeckBuilderSettings} from '@/src/server/DeckBuilding/domain/DeckBuilderSettings/DeckBuilderSettings';

describe('LoadDeckBuilderSettingsByIdQueryHandler', () => {
  describe('When retrieving settings successfully', () => {
    let repository: InMemoryDeckBuilderSettingsRepository;
    let presenter: MockProxy<LoadDeckBuilderSettingsByIdPresenter>;
    let handler: LoadDeckBuilderSettingsByIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryDeckBuilderSettingsRepository();
      presenter = mock<LoadDeckBuilderSettingsByIdPresenter>();
      handler = new LoadDeckBuilderSettingsByIdQueryHandler(repository);
    });

    describe('When settings exist for the game', () => {
      it('should display them', async () => {
        // Arrange
        const gameId = 'game-123';
        repository.addSettingsForGame(gameId, {gameId});

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(
          DeckBuilderSettings.fromSnapshot({gameId})
        );
      });
    });

    describe('When settings do not exist for the game', () => {
      it('should display null', async () => {
        // Arrange
        const gameId = 'game-123';

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(null);
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingDeckBuilderSettingsRepository();
      const presenter = mock<LoadDeckBuilderSettingsByIdPresenter>();
      const handler = new LoadDeckBuilderSettingsByIdQueryHandler(repository);

      // Act
      await handler.handle({gameId: 'game-123'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('game-123'));
    });
  });
});
