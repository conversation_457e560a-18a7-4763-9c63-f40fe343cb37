import {beforeEach, describe, expect, it} from 'vitest';
import {LoadDecksByUserIdAndGameIdQueryHandler} from '@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQueryHandler';
import {InMemoryDeckListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/DeckList/InMemoryDeckListRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadDecksByUserIdAndGameIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDecksByUserIdAndGameIdPresenter';
import {InMemoryFailingDeckListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/DeckList/InMemoryFailingDeckListRepository';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';
import {DECK_A, DECK_B} from '@/src/server/DeckBuilding/specs/helpers/fakes/fakeDecks';

describe('LoadDecksByUserIdAndGameIdQueryHandler', () => {
  describe('When retrieving decks successfully', () => {
    let repository: InMemoryDeckListRepository;
    let presenter: MockProxy<LoadDecksByUserIdAndGameIdPresenter>;
    let handler: LoadDecksByUserIdAndGameIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryDeckListRepository();
      presenter = mock<LoadDecksByUserIdAndGameIdPresenter>();
      handler = new LoadDecksByUserIdAndGameIdQueryHandler(repository);
    });

    describe('When decks exist for the user and game', () => {
      it('should display them ordered by name', async () => {
        // Arrange
        const gameId = 'game-123';
        const userId = 'user-456';
        const decks: Deck[] = [DECK_B, DECK_A];
        repository.addDecksFor(userId, gameId, decks);

        // Act
        await handler.handle({gameId, userId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          decks: [DECK_A, DECK_B],
        });
      });
    });

    describe('When no decks exist for the user and game', () => {
      it('should return an empty list', async () => {
        // Arrange
        const gameId = 'game-123';
        const userId = 'user-456';

        // Act
        await handler.handle({gameId, userId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          decks: [],
        });
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingDeckListRepository();
      const presenter = mock<LoadDecksByUserIdAndGameIdPresenter>();
      const handler = new LoadDecksByUserIdAndGameIdQueryHandler(repository);

      // Act
      await handler.handle({gameId: 'g1', userId: 'u1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('u1-g1'));
    });
  });
});
