import {beforeEach, describe, it, expect} from 'vitest';
import {LoadDeckByIdQueryHandler} from '@/src/server/DeckBuilding/application/queries/LoadDeckById/LoadDeckByIdQueryHandler';
import {InMemoryDeckRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/Deck/InMemoryDeckRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadDeckByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckByIdPresenter';
import {InMemoryFailingDeckRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/Deck/InMemoryFailingDeckRepository';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';

describe('LoadDeckByIdQueryHandler', () => {
  describe('When retrieving the deck successfully', () => {
    let repository: InMemoryDeckRepository;
    let presenter: <PERSON><PERSON><PERSON><PERSON><PERSON><LoadDeckByIdPresenter>;
    let handler: LoadDeckByIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryDeckRepository();
      presenter = mock<LoadDeckByIdPresenter>();
      handler = new LoadDeckByIdQueryHandler(repository);
    });

    describe('When the deck exists', () => {
      it('should display it', async () => {
        // Arrange
        const deck = Deck.create({gameId: 'g1', playerId: 'p1', name: 'Deck', cards: []});
        const deckId = await repository.save(deck);

        // Act
        await handler.handle({deckId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(deck);
      });
    });

    describe('When the deck does not exist', () => {
      it('should display a not found error', async () => {
        // Arrange

        // Act
        await handler.handle({deckId: 'unknown'}, presenter);

        // Assert
        expect(presenter.displayError).toHaveBeenCalledWith(new Error('Deck not found'));
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingDeckRepository();
      const presenter = mock<LoadDeckByIdPresenter>();
      const handler = new LoadDeckByIdQueryHandler(repository);

      // Act
      await handler.handle({deckId: 'd1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('d1'));
    });
  });
});
