import {LoadDecksByUserIdAndGameIdWebPresenter} from '@/src/server/DeckBuilding/presentation/presenters/LoadDecksByUserIdAndGameIdWebPresenter';
import {DeckList} from '@/src/server/DeckBuilding/domain/Deck/DeckList';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';

describe('LoadDecksByUserIdAndGameIdWebPresenter', () => {
  let presenter: LoadDecksByUserIdAndGameIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadDecksByUserIdAndGameIdWebPresenter();
  });

  describe('When displaying decks', () => {
    it('should set deck data', () => {
      // Arrange
      const decks: Deck[] = [
        Deck.fromSnapshot({id: 'd1', gameId: 'g1', playerId: 'p1', name: 'Deck 1', cards: []}),
        Deck.fromSnapshot({id: 'd2', gameId: 'g1', playerId: 'p1', name: 'Deck 2', cards: []}),
      ];
      const deckList = DeckList.createFrom(decks);

      // Act
      presenter.display(deckList);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({
        decks: [
          {id: 'd1', gameId: 'g1', playerId: 'p1', name: 'Deck 1', cards: []},
          {id: 'd2', gameId: 'g1', playerId: 'p1', name: 'Deck 2', cards: []},
        ],
      });
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
