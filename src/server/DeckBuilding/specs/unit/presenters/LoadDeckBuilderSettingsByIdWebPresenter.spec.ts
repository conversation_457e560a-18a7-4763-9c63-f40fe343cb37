import {LoadDeckBuilderSettingsByIdWebPresenter} from '@/src/server/DeckBuilding/presentation/presenters/LoadDeckBuilderSettingsByIdWebPresenter';
import {DeckBuilderSettings} from '@/src/server/DeckBuilding/domain/DeckBuilderSettings/DeckBuilderSettings';

describe('LoadDeckBuilderSettingsByIdWebPresenter', () => {
  let presenter: LoadDeckBuilderSettingsByIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadDeckBuilderSettingsByIdWebPresenter();
  });

  describe('When displaying settings', () => {
    it('should set deck builder settings data', () => {
      // Arrange
      const settings = DeckBuilderSettings.fromSnapshot({gameId: 'g1'});

      // Act
      presenter.display(settings);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({gameId: 'g1'});
    });
  });

  describe('When displaying null settings', () => {
    it('should set data to null', () => {
      // Arrange
      // Act
      presenter.display(null);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toBeNull();
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
