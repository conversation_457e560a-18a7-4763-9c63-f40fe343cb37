import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";
import {CatalogCard} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCard";

export class ConvexCatalogCardListRepository implements CatalogCardListRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getByGameId(gameId: string): Promise<CatalogCardList> {
    const cards = await this.ctx.db
      .query("catalogCards")
      .withIndex("by_gameId", q => q.eq("gameId", gameId as Id<"games">))
      .collect();

    const catalogCards: CatalogCard[] = cards.map(card => ({
      id: card._id,
      name: card.name,
      image: card.image,
      minDeckQuantity: card.minDeckQuantity,
      maxDeckQuantity: card.maxDeckQuantity,
      data: card.data,
    }));

    return CatalogCardList.createFrom(catalogCards);
  }
}
