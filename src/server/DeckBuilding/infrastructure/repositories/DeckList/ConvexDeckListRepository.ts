import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {DeckListRepository} from "@/src/server/DeckBuilding/application/ports/DeckListRepository";
import {DeckList} from "@/src/server/DeckBuilding/domain/Deck/DeckList";
import {Deck} from "@/src/server/DeckBuilding/domain/Deck/Deck";

export class ConvexDeckListRepository implements DeckListRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getByUserIdAndGameId(userId: string, gameId: string): Promise<DeckList> {
    const docs = await this.ctx.db
      .query("decks")
      .withIndex("by_playerId_and_gameId", q =>
        q.eq("playerId", userId).eq("gameId", gameId as Id<'games'>)
      )
      .collect();

    const decks = docs.map(doc =>
      Deck.fromSnapshot({
        id: doc._id,
        gameId: doc.gameId,
        playerId: doc.playerId,
        name: doc.name,
        cards: doc.cards,
      })
    );

    return DeckList.createFrom(decks);
  }
}
