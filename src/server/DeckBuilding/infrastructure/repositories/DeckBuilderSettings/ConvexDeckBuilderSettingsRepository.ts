import {GenericQueryCtx} from 'convex/server';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {DeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/application/ports/DeckBuilderSettingsRepository';
import {DeckBuilderSettings} from '@/src/server/DeckBuilding/domain/DeckBuilderSettings/DeckBuilderSettings';

export class ConvexDeckBuilderSettingsRepository implements DeckBuilderSettingsRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getByGameId(gameId: string): Promise<DeckBuilderSettings | null> {
    const doc = await this.ctx.db
      .query('deckBuilderSettings')
      .withIndex('by_gameId', q => q.eq('gameId', gameId as Id<'games'>))
      .first();
    if (!doc) {
      return null;
    }
    return DeckBuilderSettings.fromSnapshot({gameId: doc.gameId});
  }
}
