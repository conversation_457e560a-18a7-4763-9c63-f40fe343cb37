import {GenericMutationCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {DeckRepository} from "@/src/server/DeckBuilding/application/ports/DeckRepository";
import {Deck} from "@/src/server/DeckBuilding/domain/Deck/Deck";

export class ConvexDeckRepository implements DeckRepository {
  constructor(private readonly ctx: GenericMutationCtx<DataModel>) {}

  async save(deck: Deck) {
    const data = deck.toSnapshot();
    if (data.id) {
      await this.ctx.db.patch(data.id as Id<'decks'>, {
        name: data.name,
        cards: data.cards,
      });
      return data.id;
    }
    return await this.ctx.db.insert('decks', {
      ...data,
      gameId: data.gameId as Id<'games'>,
    });
  }

  async findById(id: string): Promise<Deck | null> {
    const doc = await this.ctx.db.get(id as Id<'decks'>);
    return doc ? Deck.fromSnapshot({id: doc._id, ...doc}) : null;
  }
}
