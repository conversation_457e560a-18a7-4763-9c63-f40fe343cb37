import {GenericQueryCtx} from 'convex/server';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {DeckRepository} from '@/src/server/DeckBuilding/application/ports/DeckRepository';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';

export class ConvexDeckReadRepository implements DeckRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async save(): Promise<string> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<Deck | null> {
    const doc = await this.ctx.db.get(id as Id<'decks'>);
    return doc ? Deck.fromSnapshot({id: doc._id, ...doc}) : null;
  }
}
