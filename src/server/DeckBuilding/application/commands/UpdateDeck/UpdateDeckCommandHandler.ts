import {DeckRepository} from '@/src/server/DeckBuilding/application/ports/DeckRepository';
import {DeckNotOwnedError} from '@/src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError';
import {UpdateDeckCommand} from './UpdateDeckCommand';

export class UpdateDeckCommandHandler {
  constructor(private readonly repository: DeckRepository) {}

  async handle({deckId, userId, name, cards}: UpdateDeckCommand) {
    const existingDeck = await this.repository.findById(deckId);
    if (!existingDeck) return;
    if (!existingDeck.isOwnedBy(userId)) {
      throw new DeckNotOwnedError();
    }
    existingDeck.renameTo(name);
    existingDeck.useCards(cards);
    await this.repository.save(existingDeck);
  }
}
