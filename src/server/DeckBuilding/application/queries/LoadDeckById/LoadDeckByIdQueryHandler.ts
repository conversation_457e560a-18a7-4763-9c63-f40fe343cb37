import {LoadDeckByIdQuery} from '@/src/server/DeckBuilding/application/queries/LoadDeckById/LoadDeckByIdQuery';
import {DeckRepository} from '@/src/server/DeckBuilding/application/ports/DeckRepository';
import {LoadDeckByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckByIdPresenter';

export class LoadDeckByIdQueryHandler {
  constructor(private readonly repository: DeckRepository) {}

  async handle({deckId}: LoadDeckByIdQuery, presenter: LoadDeckByIdPresenter) {
    try {
      const deck = await this.repository.findById(deckId);
      if (!deck) {
        presenter.displayError(new Error('Deck not found'));
        return;
      }
      presenter.display(deck);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
