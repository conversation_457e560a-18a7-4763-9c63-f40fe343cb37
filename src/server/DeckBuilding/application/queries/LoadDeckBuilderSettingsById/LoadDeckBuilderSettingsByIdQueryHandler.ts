import {LoadDeckBuilderSettingsByIdQuery} from '@/src/server/DeckBuilding/application/queries/LoadDeckBuilderSettingsById/LoadDeckBuilderSettingsByIdQuery';
import {DeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/application/ports/DeckBuilderSettingsRepository';
import {LoadDeckBuilderSettingsByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckBuilderSettingsByIdPresenter';

export class LoadDeckBuilderSettingsByIdQueryHandler {
  constructor(private readonly repository: DeckBuilderSettingsRepository) {}

  async handle({gameId}: LoadDeckBuilderSettingsByIdQuery, presenter: LoadDeckBuilderSettingsByIdPresenter) {
    try {
      const settings = await this.repository.getByGameId(gameId);
      presenter.display(settings);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
