import {LoadDecksByUserIdAndGameIdQuery} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQuery";
import {DeckListRepository} from "@/src/server/DeckBuilding/application/ports/DeckListRepository";
import {LoadDecksByUserIdAndGameIdPresenter} from "@/src/server/DeckBuilding/application/ports/LoadDecksByUserIdAndGameIdPresenter";

export class LoadDecksByUserIdAndGameIdQueryHandler {
  constructor(private readonly repository: DeckListRepository) {}

  async handle({gameId, userId}: LoadDecksByUserIdAndGameIdQuery, presenter: LoadDecksByUserIdAndGameIdPresenter) {
    try {
      const deckList = await this.repository.getByUserIdAndGameId(userId, gameId);
      deckList.sortByName();
      presenter.display(deckList);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
