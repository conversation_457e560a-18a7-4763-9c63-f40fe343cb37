import {DeckList} from "@/src/server/DeckBuilding/domain/Deck/DeckList";
import {LoadDecksByUserIdAndGameIdPresenter} from "@/src/server/DeckBuilding/application/ports/LoadDecksByUserIdAndGameIdPresenter";
import {LoadDecksByUserIdAndGameIdViewModel} from "@/src/server/DeckBuilding/presentation/viewModels/LoadDecksByUserIdAndGameIdViewModel";

export class LoadDecksByUserIdAndGameIdWebPresenter implements LoadDecksByUserIdAndGameIdPresenter {
  private viewModel: LoadDecksByUserIdAndGameIdViewModel = { error: null, data: null };

  display(deckList: DeckList): void {
    this.viewModel = {
      error: null,
      data: { decks: deckList.decks.map(deck => deck.toSnapshot()) },
    };
  }

  displayError(error: Error): void {
    this.viewModel = { error: error.message, data: null };
  }

  getViewModel(): LoadDecksByUserIdAndGameIdViewModel {
    return this.viewModel;
  }
}
