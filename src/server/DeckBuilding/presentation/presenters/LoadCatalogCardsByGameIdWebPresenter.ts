import {LoadCatalogCardsByGameIdPresenter} from "@/src/server/DeckBuilding/application/ports/LoadCatalogCardsByGameIdPresenter";
import {LoadCatalogCardsByGameIdViewModel} from "@/src/server/DeckBuilding/presentation/viewModels/LoadCatalogCardsByGameIdViewModel";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";

export class LoadCatalogCardsByGameIdWebPresenter implements LoadCatalogCardsByGameIdPresenter {
  private viewModel: LoadCatalogCardsByGameIdViewModel = {error: null, data: {cards: []}};

  display(cards: CatalogCardList): void {
    this.viewModel = {
      error: null,
      data: {cards: cards.cards},
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadCatalogCardsByGameIdViewModel {
    return this.viewModel;
  }
}
