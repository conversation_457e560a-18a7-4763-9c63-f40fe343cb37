import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadMatchEventsForMatchQuery
} from "@/src/server/Shared/application/queries/Debug/LoadMatchEventsForMatchQuery";

export class LoadMatchEventsForMatchQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({matchId, limit = 20}: LoadMatchEventsForMatchQuery) {
    return this.ctx.db
      .query("matchEvents")
      .withIndex("by_matchId", (q) =>
        q.eq("matchId", matchId as Id<"matches">),
      )
      .order('desc')
      .take(limit);
  }
}
