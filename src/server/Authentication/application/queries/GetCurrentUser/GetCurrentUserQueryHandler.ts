import {CurrentUserRepository} from '@/src/server/Authentication/application/ports/CurrentUserRepository';
import {GetCurrentUserPresenter} from '@/src/server/Authentication/application/ports/GetCurrentUserPresenter';

export class GetCurrentUserQueryHandler {
  constructor(private readonly repository: CurrentUserRepository) {}

  async handle(presenter: GetCurrentUserPresenter) {
    try {
      const user = await this.repository.getCurrentUser();
      presenter.display(user);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
