import {GenericQueryCtx} from 'convex/server';
import {DataModel} from '@/convex/_generated/dataModel';
import {CurrentUserRepository} from '@/src/server/Authentication/application/ports/CurrentUserRepository';
import {CurrentUser} from '@/src/server/Authentication/domain/User/CurrentUser';

export class ConvexCurrentUserRepository implements CurrentUserRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getCurrentUser(): Promise<CurrentUser | null> {
    const identity = await this.ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const convexId = identity.subject.split('|')[0];

    const user = await this.ctx.db
      .query('users')
      .filter(q => q.eq(q.field('_id'), convexId))
      .first();

    if (!user) {
      return null;
    }

    return {userId: user._id, email: user.email};
  }
}
