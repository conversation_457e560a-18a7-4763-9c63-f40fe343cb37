import {createAsyncThunk} from '@reduxjs/toolkit';
import {catalogCardUpdatedEvent} from '@/src/client/CatalogManagement/domain/Catalog/catalogEvents';
import {CatalogCard} from '@/src/client/CatalogManagement/domain/Catalog/CatalogCard';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';

export const updateCatalogCard = createAsyncThunk<void, {gameId: string; card: CatalogCard}, {state: RootState; extra: ThunkExtra}>(
  'catalogManagement/updateCatalogCard',
  async ({gameId, card}, {dispatch}) => {
    dispatch(catalogCardUpdatedEvent({gameId, card}));
  },
);
