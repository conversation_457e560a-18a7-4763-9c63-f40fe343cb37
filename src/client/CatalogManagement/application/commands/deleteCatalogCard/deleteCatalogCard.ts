import {createAsyncThunk} from '@reduxjs/toolkit';
import {catalogCardDeletedEvent} from '@/src/client/CatalogManagement/domain/Catalog/catalogEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';

export const deleteCatalogCard = createAsyncThunk<void, {gameId: string; cardId: string}, {state: RootState; extra: ThunkExtra}>(
  'catalogManagement/deleteCatalogCard',
  async ({gameId, cardId}, {dispatch}) => {
    dispatch(catalogCardDeletedEvent({gameId, cardId}));
  },
);
