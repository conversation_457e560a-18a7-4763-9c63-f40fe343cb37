import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadCatalogCards} from '@/src/client/CatalogManagement/application/commands/loadCatalogCards/loadCatalogCards';
import {isCatalogManagementLoading} from '@/src/client/CatalogManagement/application/queries/isCatalogManagementLoading/isCatalogManagementLoading';
import {getCatalogManagementError} from '@/src/client/CatalogManagement/application/queries/getCatalogManagementError/getCatalogManagementError';
import {getCatalogCards} from '@/src/client/CatalogManagement/application/queries/getCatalogCards/getCatalogCards';
import {CatalogCard} from '@/src/client/CatalogManagement/domain/Catalog/CatalogCard';

describe('loadCatalogCards', () => {
  describe('When the request is undefined', () => {
    it('should start loading the catalog', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(isCatalogManagementLoading(getState())).toBe(true);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogManagementError(getState())).toBeNull();
    });
  });

  describe('When the request has an error', () => {
    it('should return the error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogManagementError(getState())).toBe('An error occurred');
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(isCatalogManagementLoading(getState())).toBe(false);
    });
  });

  describe('When the request is successful', () => {
    const cards: CatalogCard[] = [
      {id: '1', name: 'Card 1', image: '', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}},
      {id: '2', name: 'Card 2', image: '', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}},
    ];

    it('should retrieve the cards', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {cards}};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogCards(getState())).toEqual(cards);
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {cards}};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(isCatalogManagementLoading(getState())).toBe(false);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {cards}};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogManagementError(getState())).toBeNull();
    });
  });
});
