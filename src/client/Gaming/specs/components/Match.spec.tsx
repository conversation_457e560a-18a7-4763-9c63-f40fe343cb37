import {render} from '@testing-library/react';
import Match from '@/src/client/Gaming/infrastructure/components/app/Gaming/Match/Match';
import {buildGameUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';
import {Id} from '@/convex/_generated/dataModel';
import {vi} from 'vitest';
import {useQuery} from 'convex/react';

vi.mock('convex/react');

const pushMock = vi.fn();
vi.mock('next/navigation', () => ({
  useRouter: () => ({ push: pushMock }),
}));

describe('Match', () => {
  describe('When the match has not ended', () => {
    it('should not redirect to the game page', () => {
      // Arrange
      vi.mocked(useQuery).mockReturnValue({error: null, data: null});
      const match = {players: ['u1', 'u2'], status: 'setup', gameId: 'g1' as Id<'games'>};

      // Act
      render(<Match locale="en" matchId={'m1' as Id<'matches'>} match={match}/>);

      // Assert
      expect(pushMock).not.toHaveBeenCalled();
    });
  });

  describe('When the match has ended', () => {
    it('should redirect to the game page', () => {
      // Arrange
      vi.mocked(useQuery).mockReturnValue({error: null, data: {winner: 'u1'}});
      const match = {players: ['u1', 'u2'], status: 'setup', gameId: 'g1' as Id<'games'>};

      // Act
      render(<Match locale="en" matchId={'m1' as Id<'matches'>} match={match}/>);

      // Assert
      expect(pushMock).toHaveBeenCalledWith(buildGameUrl('en', match.gameId));
    });
  });
});
