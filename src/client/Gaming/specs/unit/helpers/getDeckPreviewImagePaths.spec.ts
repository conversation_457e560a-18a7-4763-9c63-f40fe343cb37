import {getDeckPreviewImagePaths} from '@/src/client/Gaming/helpers/getDeckPreviewImagePaths';
import {CatalogCard} from '@/src/client/DeckBuilding/domain/Catalog/CatalogCard';

describe('When building deck preview images', () => {
  it('should return the first three image paths', () => {
    // Arrange
    const deck = {
      cards: [
        {cardId: 'c1', quantity: 1},
        {cardId: 'c2', quantity: 1},
        {cardId: 'c3', quantity: 1},
        {cardId: 'c4', quantity: 1},
      ],
    };
    const catalog: Record<string, CatalogCard> = {
      c1: {id: 'c1', name: 'A', image: '1.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
      c2: {id: 'c2', name: 'B', image: '2.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
      c3: {id: 'c3', name: 'C', image: '3.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
      c4: {id: 'c4', name: 'D', image: '4.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
    };
    const locale = 'en';

    // Act
    const result = getDeckPreviewImagePaths(deck, catalog, locale);

    // Assert
    expect(result).toEqual([
      '/game-assets/cards/en/thumbnail/1.jpg',
      '/game-assets/cards/en/thumbnail/2.jpg',
      '/game-assets/cards/en/thumbnail/3.jpg',
    ]);
  });

  it('should ignore unknown card ids', () => {
    // Arrange
    const deck = {
      cards: [
        {cardId: 'c1', quantity: 1},
        {cardId: 'unk', quantity: 1},
        {cardId: 'c2', quantity: 1},
      ],
    };
    const catalog: Record<string, CatalogCard> = {
      c1: {id: 'c1', name: 'A', image: '1.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
      c2: {id: 'c2', name: 'B', image: '2.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
    };
    const locale = 'en';

    // Act
    const result = getDeckPreviewImagePaths(deck, catalog, locale);

    // Assert
    expect(result).toEqual([
      '/game-assets/cards/en/thumbnail/1.jpg',
      '/game-assets/cards/en/thumbnail/2.jpg',
    ]);
  });

  it('should return an empty array when no cards', () => {
    // Arrange
    const deck = {cards: []};
    const catalog: Record<string, CatalogCard> = {};
    const locale = 'en';

    // Act
    const result = getDeckPreviewImagePaths(deck, catalog, locale);

    // Assert
    expect(result).toEqual([]);
  });
});
