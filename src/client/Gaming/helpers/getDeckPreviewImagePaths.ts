import {CatalogCard} from '@/src/client/DeckBuilding/domain/Catalog/CatalogCard';

export type DeckForPreview = { cards: { cardId: string; quantity: number }[] };

export function getDeckPreviewImagePaths(
  deck: DeckForPreview,
  catalog: Record<string, CatalogCard>,
  locale: string,
): string[] {
  const images: string[] = [];
  for (const {cardId} of deck.cards) {
    const card = catalog[cardId];
    if (!card) continue;
    images.push(`/game-assets/cards/${locale}/thumbnail/${card.image}`);
    if (images.length === 3) break;
  }
  return images;
}
