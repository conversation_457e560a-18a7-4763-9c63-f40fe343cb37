import {FC} from "react";
import {Con<PERSON><PERSON>, Flex, Grid, Head<PERSON>} from "@radix-ui/themes";
import GameDetailsButton
  from "@/src/client/Gaming/infrastructure/components/app/Gaming/GameDetailsButton/GameDetailsButton";
import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {convexAuthNextjsToken} from "@convex-dev/auth/nextjs/server";

const GameListPage: FC = async () => {
  const viewModel = await fetchQuery(api.queries.loadGameList.endpoint, {}, {token: await convexAuthNextjsToken()});
  const games = viewModel.data ?? [];

  return (
    <Container p="3">
      <Flex direction="column" gap="3">
        <Heading size="4">
          Catalog
        </Heading>
        <Grid columns="6" gap="3">
          {games.map((game) => (
            <GameDetailsButton key={game.id} gameId={game.id}>
              {game.name}
            </GameDetailsButton>
          ))}
        </Grid>
      </Flex>
    </Container>
  );
};

export default GameListPage;