'use client'

import React, {FC} from "react";
import {<PERSON>, Button, Container, Flex, Grid, Heading, Text,} from "@radix-ui/themes";
import {PlayIcon,} from "@radix-ui/react-icons";
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";
import ShiningButton from "@/src/client/Shared/components/ShiningButton/ShiningButton";
import Image, {ImageProps} from "next/image";
import {CardDeck} from "@/src/client/Shared/components/CardDeck/CardDeck";
import {SquareCheckBig} from "lucide-react";
import {usePlayGamePage} from "@/src/client/Gaming/infrastructure/pages/Gaming/PlayGamePage/usePlayGamePage";
import {useCardsByGameId} from "@/src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId";
import {useSelector} from "react-redux";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {getDeckPreviewImagePaths} from "@/src/client/Gaming/helpers/getDeckPreviewImagePaths";

type Props = {
  locale: string;
  gameId: string;
};

const PlayGamePage: FC<Props> = ({gameId, locale}) => {
  useCardsByGameId({gameId});
  const catalog = useSelector((state: RootState) => state.catalog.cards);

  const {
    searchTime,
    loading,
    deckId,
    decks,
    handleStart,
    handleCancel,
    formatTime,
    setDeckId,
    loadingDecks
  } = usePlayGamePage({gameId, locale});

  const selectedDeck = decks.find(d => d.id === deckId);
  const selectedImages = selectedDeck ? getDeckPreviewImagePaths(selectedDeck, catalog, locale) : [];
  const selectedElements = selectedImages.slice(0, 3).map((src, index) => (
    <Image key={index} src={src} alt={`Card ${index + 1}`} fill className="object-cover rounded-lg"/>
  )) as [React.ReactElement<ImageProps>, React.ReactElement<ImageProps>, React.ReactElement<ImageProps>];

  return (
    <Container p="3">
      <Flex direction="column" className="h-full w-full">

        {!loading && (
          <Flex direction="column" gap="6" mt="8">
            <Heading size="8" align="center">Select your deck for the next match, then click Play!</Heading>

            {deckId && selectedImages.length >= 3 && (
              <Flex direction="column" justify="center" align="center" gap="2">
                <Heading size="4">{selectedDeck?.name}</Heading>
                <Box width="150px" mt="5">
                  <CardDeck cover={`/game-assets/cards/${locale}/thumbnail/1.jpg`}>
                    {selectedElements[0]}
                    {selectedElements[1]}
                    {selectedElements[2]}
                  </CardDeck>
                </Box>
              </Flex>
            )}

            <Flex justify="center" align="center">
              <ShiningButton
                color="iris"
                size="4"
                className="w-4"
                disabled={!deckId}
                onClick={handleStart}
              >
                <PlayIcon width="24" height="24"/>
                Click here to Play
              </ShiningButton>
            </Flex>

            {!deckId && !loadingDecks && (
              <Text size="3" color="gray" align="center">
                You must select a deck in your collection to play.
              </Text>
            )}

            {loadingDecks && (
              <Text size="3" color="gray" align="center">
                 Loading...
              </Text>
            )}

            <Grid columns="5" gap="3">
              {decks.map((deck) => (
                <Flex direction="column" gap="3" key={deck.id}>
                  <ShiningCard
                    disableScaling
                    selected={deckId === deck.id}
                    key={deck.id}
                    onClick={() => setDeckId(deck.id)}
                    className="h-[300px]"
                  >
                    <Flex direction="column" gap="3" align="center" justify="center" className="relative">

                      <Box
                        display={deckId === deck.id ? "block" : "none"}
                        className={`absolute top-0 right-0 left-0 bottom-0 z-10`}>
                        <Flex direction="column" align="center" justify="end" className="h-full">
                          <SquareCheckBig width="120" height="120" className="mt-4"/>
                        </Flex>
                      </Box>

                      <Heading size="3" style={{marginBottom: "4px"}} align="center" weight="regular">
                        {deck.name}
                      </Heading>

                      <Box width="150px" mt="5" height="120px"
                           className={deckId === deck.id ? "opacity-50" : "opacity-100"}>
                        {(() => {
                          const elems = getDeckPreviewImagePaths(deck, catalog, locale)
                            .slice(0, 3)
                            .map((src, index) => (
                              <Image key={index} src={src} alt={`Card ${index + 1}`} fill
                                     className="object-cover rounded-lg"/>
                            ));
                          return (
                            <CardDeck cover={`/game-assets/cards/${locale}/thumbnail/1.jpg`}>
                              {elems[0]}
                              {elems[1]}
                              {elems[2]}
                            </CardDeck>
                          );
                        })()}
                      </Box>

                    </Flex>
                  </ShiningCard>
                </Flex>
              ))}
            </Grid>

          </Flex>
        )}

        {loading && (
          <Flex direction="column" justify="center" align="center" mt="8">
            <ShiningCard size="3" disableScaling selected>
              <Flex direction="column" gap="3">
                <Box style={{textAlign: "center"}}>
                  <Flex justify="center" align="center" gap="2" mb="2">
                    <Text size="6" weight="medium">
                      Searching for opponent...
                    </Text>
                  </Flex>
                  <Text size="9" weight="bold">
                    {formatTime(searchTime)}
                  </Text>
                  <Text
                    size="4"
                    color="gray"
                    style={{marginTop: "8px", display: "block"}}
                  >
                    Estimated wait: 2-5 minutes
                  </Text>
                </Box>

                <Button
                  variant="outline"
                  color="red"
                  size="4"
                  style={{width: "100%"}}
                  onClick={handleCancel}
                >
                  Cancel Search
                </Button>
              </Flex>
            </ShiningCard>
          </Flex>
        )}
      </Flex>
    </Container>
  );
}

export default PlayGamePage;
