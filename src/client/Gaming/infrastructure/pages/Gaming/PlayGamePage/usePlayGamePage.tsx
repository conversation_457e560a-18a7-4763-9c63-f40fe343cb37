import {useEffect, useState} from "react";
import {useMutation, useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {useRouter} from "next/navigation";
import {buildMatchUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";

export const usePlayGamePage = ({gameId, locale}: { gameId: string, locale: string }) => {
  const [searchTime, setSearchTime] = useState(0);
  const [loading, setLoading] = useState(false);
  const [deckId, setDeckId] = useState<string | null>(null);
  const matchCreated = useQuery(api.queries.onMatchCreated.endpoint, {gameId: gameId as Id<"games">});
  const decks = useQuery(api.queries.loadDecksByUserIdAndGameId.endpoint, {gameId: gameId as Id<"games">});
  const router = useRouter();

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (loading) {
      interval = setInterval(() => {
        setSearchTime((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [loading]);

  useEffect(() => {
    if (matchCreated?.data) {
      router.push(buildMatchUrl(locale, matchCreated.data.payload.matchId));
    }
  }, [matchCreated, router, locale, gameId]);

  const addPlayerToMatchMakingQueue = useMutation(api.mutations.addPlayerToMatchMakingQueue.endpoint);
  const cancelMatchRegistration = useMutation(api.mutations.cancelMatchRegistration.endpoint);

  const handleStart = async () => {
    await addPlayerToMatchMakingQueue({gameId, deckId: deckId!});
    setLoading(true);
    setSearchTime(0);
  };

  const handleCancel = async () => {
    await cancelMatchRegistration({gameId});
    setLoading(false);
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return {
    searchTime,
    loading,
    loadingDecks: !decks?.data,
    deckId,
    decks: decks?.data?.decks || [],
    handleStart,
    handleCancel,
    formatTime,
    setDeckId,
  };
}