'use client'

import {<PERSON><PERSON>, Con<PERSON>er, <PERSON>lex, <PERSON>rid, <PERSON><PERSON>, <PERSON>rollArea, Tabs, Text} from '@radix-ui/themes'
import Image from 'next/image'
import PlayGameButton from "@/src/client/Gaming/infrastructure/components/app/Gaming/PlayGameButton/PlayGameButton";
import {FC, useState} from "react";
import {useAutoAnimate} from "@formkit/auto-animate/react";
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";

type Props = {
  locale: string;
  gameId: string;
};

const GameDetailsPage: FC<Props> = ({gameId}) => {
  const [parentRef] = useAutoAnimate<HTMLDivElement>();
  const [view, setView] = useState<'screenshots' | 'videos' | 'reviews'>('screenshots');
  return (
    <Container pt="3">
      <Flex direction="column" gap="3" className="h-full">
        <Image
          src="/game-assets/lorcana-banner.png"
          alt="Game banner"
          width={1200}
          height={400}
          className="object-cover w-full h-full"
        />

        <PlayGameButton gameId={gameId}/>

        <Flex direction="column">
          <Flex justify="between">
            <Flex align="baseline" gap="2">
              <Heading size="8">Lorcana</Heading>
              <Text size="3" color="gray">
                by <Text as="span" className="text-white font-medium">Disney</Text>
              </Text>
            </Flex>

            <Flex align="center" gap="2">
              <Badge size="2">1280 players online</Badge>
              <Badge size="2" color="blue">512045 matches played</Badge>
            </Flex>
          </Flex>

          <Text size="4">
            Lorcana est un jeu dans lequel vous faites équipe avec vos personnages Disney préférés pour embarquer dans
            des aventures passionnantes...
          </Text>
        </Flex>

        <Flex direction="column" gap="3" className="h-full">
          <Tabs.Root value={view} onValueChange={(v) => setView(v as typeof view)} className="h-full">
            <Tabs.List size="2">
              <Tabs.Trigger value="screenshots">Screenshots</Tabs.Trigger>
              <Tabs.Trigger value="videos">Videos</Tabs.Trigger>
              <Tabs.Trigger value="reviews">Reviews</Tabs.Trigger>
            </Tabs.List>
            <ScrollArea type="always" scrollbars="vertical" className="h-full">
              <Tabs.Content value="screenshots" className="h-full">
                <Grid columns="3" gap="3" pr="4" ref={parentRef}>
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <ShiningCard key={i} className="w-[300px] h-[170px] overflow-hidden rounded-xl p-0">
                      <Image
                        src={`/game-assets/screenshot-${i}.png`}
                        alt={`Screenshot ${i}`}
                        width={600}
                        height={340}
                        className="object-cover w-full h-full"
                      />
                    </ShiningCard>
                  ))}
                </Grid>
              </Tabs.Content>
              <Tabs.Content value="videos" className="h-full">
                <Grid columns="3" gap="3" pr="4" ref={parentRef}>
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <ShiningCard key={i} className="w-[300px] h-[170px] overflow-hidden rounded-xl p-0">
                      <Image
                        src={`/game-assets/screenshot-${i}.png`}
                        alt={`Video ${i}`}
                        width={600}
                        height={340}
                        className="object-cover w-full h-full"
                      />
                    </ShiningCard>
                  ))}
                </Grid>
              </Tabs.Content>
              <Tabs.Content value="reviews" className="h-full">
                <Grid columns="3" gap="3" pr="4" ref={parentRef}>
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <ShiningCard key={i} className="w-[300px] h-[170px] overflow-hidden rounded-xl p-0">
                      <Image
                        src={`/game-assets/screenshot-${i}.png`}
                        alt={`Review ${i}`}
                        width={600}
                        height={340}
                        className="object-cover w-full h-full"
                      />
                    </ShiningCard>
                  ))}
                </Grid>
              </Tabs.Content>
            </ScrollArea>
          </Tabs.Root>
        </Flex>
      </Flex>
    </Container>
  )
}

export default GameDetailsPage;
