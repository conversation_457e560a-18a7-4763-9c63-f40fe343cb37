'use client';

import {FC, useRef} from "react";
import {Canvas, useLoader} from "@react-three/fiber";
import {Text, Plane} from "@react-three/drei";
import * as THREE from "three";
import {Id} from "@/convex/_generated/dataModel";
import MatchConsoleEvents
  from "@/src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents";
import {
  LeaveMatchButton
} from "@/src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton";
import FinishedMatchPage from "@/src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage";
import ErrorLoadingMatchPage
  from "@/src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";

type Props = {
  matchId: string;
};

// Simple 3D scene component
const HelloWorldScene: FC = () => {
  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} />
      <Text
        position={[0, 0, 0]}
        fontSize={2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        Hello World
      </Text>
    </>
  );
};

const MatchPage: FC<Props> = ({matchId}) => {
  const viewModel = useQuery(
    api.queries.match.loadMatchById.endpoint,
    {matchId}
  );

  if (viewModel?.data?.status === "finished") {
    return <FinishedMatchPage matchId={matchId} match={viewModel.data}/>;
  }

  if (viewModel?.error) {
    return <ErrorLoadingMatchPage errorMessage={viewModel.error}/>;
  }

  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      {/* Full-screen Three.js Canvas */}
      <Canvas
        style={{ width: '100%', height: '100%' }}
        camera={{ position: [0, 0, 5] }}
      >
        <HelloWorldScene />
      </Canvas>

      {/* MatchConsoleEvents positioned absolutely in bottom-right */}
      <div style={{
        position: 'absolute',
        bottom: '20px',
        right: '20px',
        zIndex: 10,
        maxWidth: '400px',
        maxHeight: '300px',
        overflow: 'auto'
      }}>
        <MatchConsoleEvents
          matchId={matchId as Id<"matches">}
        />
      </div>

      {/* LeaveMatchButton positioned absolutely in top-left */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 10
      }}>
        <LeaveMatchButton matchId={matchId as Id<"matches">}/>
      </div>
    </div>
  );
};

export default MatchPage;