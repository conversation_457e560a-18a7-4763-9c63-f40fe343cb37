'use client';

import {FC, useEffect, useState} from "react";
import {Canvas} from "@react-three/fiber";
import {Text} from "@react-three/drei";
import {Id} from "@/convex/_generated/dataModel";
import MatchConsoleEvents
  from "@/src/client/Gaming/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents";
import {
  LeaveMatchButton
} from "@/src/client/Gaming/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton";
import FinishedMatchPage from "@/src/client/Gaming/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage";
import ErrorLoadingMatchPage
  from "@/src/client/Gaming/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";

type Props = {
  locale: string;
  matchId: string;
};

const MatchPage: FC<Props> = async ({locale, matchId}) => {
  const {data: match, error} = await fetchQuery(
    api.queries.match.loadMatchById.endpoint,
    {matchId},
    {token: await convexAuthNextjsToken()}
  );

  if (match?.status === "finished") {
    return <FinishedMatchPage matchId={matchId} match={match}/>;
  }

  if (error) {
    return <ErrorLoadingMatchPage errorMessage={error}/>;
  }

  return (
    <Container p="3">
      <Flex direction="column" gap="4">
        <Heading size="5">Match in progress</Heading>

        <Match matchId={matchId as Id<"matches">} locale={locale} match={match!}/>

        <MatchConsoleEvents
          matchId={matchId as Id<"matches">}
        />

        <LeaveMatchButton matchId={matchId as Id<"matches">}/>
      </Flex>
    </Container>
  );
};

export default MatchPage;