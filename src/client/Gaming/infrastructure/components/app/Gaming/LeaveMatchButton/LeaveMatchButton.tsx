'use client';

import {useMutation} from "convex/react";
import {api} from "@/convex/_generated/api";
import {Button, Flex, Text} from "@radix-ui/themes";
import {useState} from "react";
import {Id} from "@/convex/_generated/dataModel";

type Props = {
  matchId: Id<"matches">;
};

export const LeaveMatchButton = ({matchId}: Props) => {
  const leaveMatch = useMutation(api.mutations.leaveMatch.endpoint);
  const [error, setError] = useState<string | null>(null)

  const handleLeaveMatch = async () => {
    try {
      setError(null)
      await leaveMatch({matchId});
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message);
    }
  };

  return (
    <Flex direction="column" gap="3">
      {error && <Text color="red">{error}</Text>}
      <Button
        size="3"
        color="red"
        variant="surface"
        onClick={handleLeaveMatch}
      >
        Leave Match
      </Button>
    </Flex>
  );
};
