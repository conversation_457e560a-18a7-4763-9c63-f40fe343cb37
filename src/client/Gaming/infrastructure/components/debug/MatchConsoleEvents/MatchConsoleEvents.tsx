'use client';

import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {Badge, Card, Code, Flex} from "@radix-ui/themes";

type Props = {
  matchId: Id<'matches'>;
};

const MatchConsoleEvents = ({matchId}: Props) => {
  const viewModel = useQuery(api.queries.match.matchEvents.endpoint, {matchId});
  const events = viewModel?.data?.events;

  return (
    <Card>
      <Flex direction="column" gap="1">
        {!events && <div>Loading...</div>}
        {events?.length === 0 && <div>No events</div>}
        {events?.map((event) => (
          <Flex key={event.id} align="center" gap="3">
            <Badge variant="outline" color="lime" size="2">
              {new Date(event.occurredAt).toLocaleTimeString()}
            </Badge>
            <Badge
              variant="outline"
              color={event.type === 'MatchEnded' ? 'red' : 'lime'}
              size="2"
            >
              {event.type}
            </Badge>
            <Code color="teal">{JSON.stringify(event.payload, null, 2)}</Code>
          </Flex>
        ))}
      </Flex>
    </Card>
  );
};

export default MatchConsoleEvents;
