import {DeckBuilderState} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {CatalogState} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {CatalogState as CatalogManagementState} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {CatalogFilterState} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {CatalogSearchState} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {GameSettingsState} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {GameManagementState} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {DeckBuilderUiState} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";

export interface RootState {
  deckBuilder: DeckBuilderState;
  deckBuilderUi: DeckBuilderUiState;
  catalog: CatalogState;
  catalogFilters: CatalogFilterState;
  catalogSearch: CatalogSearchState;
  gameSettings: GameSettingsState;
  gameManagement: GameManagementState;
  catalogManagement: CatalogManagementState;
}