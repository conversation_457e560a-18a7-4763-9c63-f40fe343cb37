import {deckBuilderReducer} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {catalogReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {catalogReducer as catalogManagementReducer} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {catalogFiltersReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {catalogSearchReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {gameSettingsReducer} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {gameManagementReducer} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {deckBuilderUiReducer} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";

export const rootReducers = {
  deckBuilder: deckBuilderReducer,
  deckBuilderUi: deckBuilderUiReducer,
  catalog: catalogReducer,
  catalogFilters: catalogFiltersReducer,
  catalogSearch: catalogSearchReducer,
  gameSettings: gameSettingsReducer,
  gameManagement: gameManagementReducer,
  catalogManagement: catalogManagementReducer,
};