import {initialDeckBuilderState} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {initialCatalogState} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {initialCatalogState as initialCatalogManagementState} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {initialCatalogFiltersState} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {initialCatalogSearchState} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {initialGameSettingsState} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {initialGameManagementState} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {initialDeckBuilderUiState} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";

export const rootInitialState = {
  deckBuilder: initialDeckBuilderState,
  deckBuilderUi: initialDeckBuilderUiState,
  catalog: initialCatalogState,
  catalogFilters: initialCatalogFiltersState,
  catalogSearch: initialCatalogSearchState,
  gameSettings: initialGameSettingsState,
  gameManagement: initialGameManagementState,
  catalogManagement: initialCatalogManagementState,
};