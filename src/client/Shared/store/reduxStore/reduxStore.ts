import {configureStore} from "@reduxjs/toolkit";
import {RealLocationService} from "@/src/client/Shared/services/Location/RealLocationService";

import {rootReducers} from "@/src/client/Shared/store/appStore/rootReducers";

export const locationService = new RealLocationService();

export const reduxStore = configureStore({
  reducer: rootReducers,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      thunk: {extraArgument: {locationService}},
      serializableCheck: {
        ignoredActions: ['catalog/cardsLoaded'],
      },
    }),
});
