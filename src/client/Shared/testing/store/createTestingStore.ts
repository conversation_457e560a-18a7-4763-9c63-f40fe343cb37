import {configureStore} from "@reduxjs/toolkit";
import {deepMerge} from "@/src/client/Shared/helpers/DeepMerge/deepMerge";

import {LocationService} from "@/src/client/Shared/services/Location/LocationService";
import {FakeLocationService} from "@/src/client/Shared/services/Location/FakeLocationService";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {rootReducers} from "@/src/client/Shared/store/appStore/rootReducers";
import {rootInitialState} from "@/src/client/Shared/store/appStore/rootInitialState";

type OverrideState = Partial<{
  [K in keyof RootState]: Partial<RootState[K]>
}>;

export const createTestingStore = (
  overrides: OverrideState = {},
  extra?: { locationService?: LocationService }
) => {
  const store = configureStore({
    reducer: rootReducers,
    preloadedState: Object.fromEntries(
      Object.entries(rootInitialState).map(([key, initial]) => {
        const override = overrides[key as keyof RootState];
        const merged = override ? deepMerge(initial, override) : initial;
        return [key, merged];
      })
    ) as unknown as RootState,
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware({
        thunk: {
          extraArgument: {
            locationService: extra?.locationService ?? new FakeLocationService(),
          },
        },
      });
    },
  });
  return store;
}
