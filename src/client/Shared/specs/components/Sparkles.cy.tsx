/// <reference types="cypress" />
import {mount} from '@cypress/react'
import {Sparkles} from '@/src/client/Shared/components/Sparkles/Sparkles'

describe('When rendering Sparkles', () => {
  it('should generate an id when none is provided', () => {
    // Arrange
    const component = <Sparkles />

    // Act
    mount(component)

    // Assert
    cy.get('div[id]').should('have.length', 1)
  })

  it('should use the provided id when set', () => {
    // Arrange
    const component = <Sparkles id="custom" />

    // Act
    mount(component)

    // Assert
    cy.get('#custom').should('exist')
  })
})
