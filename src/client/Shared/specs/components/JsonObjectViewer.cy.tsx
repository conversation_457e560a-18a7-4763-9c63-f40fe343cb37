/// <reference types="cypress" />
import {mount} from '@cypress/react'
import JsonObjectViewer from '@/src/client/Shared/components/JsonObjectViewer/JsonObjectViewer'

describe('When rendering JsonObjectViewer', () => {
  it('should display the provided object as formatted JSON', () => {
    // Arrange
    const data = {foo: 'bar'}
    const component = <JsonObjectViewer data-testid="viewer" data={data} />

    // Act
    mount(component)

    // Assert
    cy.get('[data-testid="viewer"]').should('contain.text', '{\n  "foo": "bar"\n}')
  })
})
