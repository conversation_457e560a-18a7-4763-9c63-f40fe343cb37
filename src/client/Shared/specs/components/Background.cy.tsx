/// <reference types="cypress" />
import {mount} from '@cypress/react'
import Background from '@/src/client/Shared/components/Background/Background'

describe('When rendering Background', () => {
  it('should render an svg element with fixed positioning', () => {
    // Arrange
    const component = <Background />

    // Act
    mount(component)

    // Assert
    cy.get('svg').should('have.css', 'position', 'fixed')
  })
})
