/// <reference types="cypress" />
import {mount} from '@cypress/react'
import SkeletonHelper from '@/src/client/Shared/components/SkeletonHelper/SkeletonHelper'
import React from 'react'

describe('When rendering SkeletonHelper', () => {
  it('should render the provided component the specified number of times', () => {
    // Arrange
    const Dummy = () => <div data-testid="dummy" />
    const component = <SkeletonHelper amount={3} component={Dummy} />

    // Act
    mount(component)

    // Assert
    cy.get('[data-testid="dummy"]').should('have.length', 3)
  })
})
