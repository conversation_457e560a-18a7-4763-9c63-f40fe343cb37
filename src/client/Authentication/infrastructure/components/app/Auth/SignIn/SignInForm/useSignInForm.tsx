import {useAuthActions} from '@convex-dev/auth/react';
import {useCallback, useState} from 'react';
import {useLocale} from "@/src/client/Shared/hooks/useLocale/useLocale";
import {buildGameListUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";

export const useSignInForm = () => {
  const [isLoading, setLoading] = useState(false);
  const locale = useLocale();
  const {signIn} = useAuthActions();

  const handleSignIn = useCallback(async () => {
    setLoading(true);
    await signIn('github', {redirectTo: buildGameListUrl(locale)});
  }, [signIn, locale]);

  return {
    isLoading,
    handleSignIn,
  };
};
