import {render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import DeckBuilderMessageSection from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderMessageSection/DeckBuilderMessageSection';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';

describe('DeckBuilderMessageSection', () => {





  describe('When the builder has cards', () => {
    it('should display nothing', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
        deckBuilderUi: {status: 'idle'},
      });

      // Act
      const { container } = render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(container.firstChild).toBeNull();
    });
  });

  describe('When the deck is empty', () => {
    it('should display the onboarding message', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {},
      });

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(
        screen.getByText(
          /Click on the plus button at the bottom right of each card to start building your deck./
        )
      ).toBeTruthy();
    });
  });
});
