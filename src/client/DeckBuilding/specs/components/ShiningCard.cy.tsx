/// <reference types="cypress" />
import {mount} from '@cypress/react'
import ShiningCard from '@/src/client/Shared/components/ShiningCard/ShiningCard'

describe('When rendering ShiningCard', () => {
  it('should not apply selected or no-scaling classes by default', () => {
    // Arrange
    const content = 'Hello';
    const component = <ShiningCard data-testid="card">{content}</ShiningCard>

    // Act
    mount(component);

    // Assert
    cy.get('[data-testid="card"]').should('not.have.class', 'selected');
    cy.get('[data-testid="card"]').should('not.have.class', 'no-scaling');
  });

  it('should apply selected and no-scaling classes when props are set', () => {
    // Arrange
    const content = 'Hello';
    const component = (
      <ShiningCard selected disableScaling data-testid="card">{content}</ShiningCard>
    );

    // Act
    mount(component);

    // Assert
    cy.get('[data-testid="card"]').should('have.class', 'selected');
    cy.get('[data-testid="card"]').should('have.class', 'no-scaling');
  });
});
