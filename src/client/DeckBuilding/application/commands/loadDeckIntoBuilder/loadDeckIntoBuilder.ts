import {createAsyncThunk} from "@reduxjs/toolkit";
import {deckLoadedEvent} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";
import {getCatalogCardById} from "@/src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById";
import {
  LoadDeckIntoBuilderRequest
} from "@/src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";

export const loadDeckIntoBuilder = createAsyncThunk<void, LoadDeckIntoBuilderRequest, {state: RootState}>(
  'deckBuilder/loadDeck',
  async ({deck}, {dispatch, getState}) => {
    const state = getState();
    const deckCards = deck.cards.map(({cardId, quantity}) => ({
      card: getCatalogCardById(state, cardId),
      quantity,
    }));
    dispatch(deckLoadedEvent({deckId: deck.id, name: deck.name, cards: deckCards}));
  }
);
