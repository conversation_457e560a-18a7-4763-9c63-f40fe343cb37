import {createSelector} from "reselect";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {isGameSettingsLoading} from "@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading";
import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";

export type DeckBuilderMessageStatus =
  | 'loading'
  | 'onboarding'
  | null;

export const getDeckBuilderMessageStatus = createSelector(
  isCatalogLoading,
  isGameSettingsLoading,
  getTotalCardsInDeck,
  (
    catalogLoading,
    settingsLoading,
    totalCards,
  ): DeckBuilderMessageStatus => {
    if (catalogLoading || settingsLoading) return 'loading';
    if (totalCards === 0) return 'onboarding';
    return null;
  },
);
