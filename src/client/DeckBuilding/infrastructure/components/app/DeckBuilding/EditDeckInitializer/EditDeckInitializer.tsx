'use client';

import {useEffect, useRef} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {loadDeckIntoBuilder} from '@/src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder';
import {getCatalogCardById} from '@/src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById';
import {isCatalogLoading} from '@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export function EditDeckInitializer({deck}: {deck: {id: string; name: string; cards: {cardId: string; quantity: number}[]} | null | undefined}) {
  const dispatch = useDispatch<AppDispatch>();
  const initialized = useRef(false);
  const catalogLoaded = useSelector((state: RootState) => !isCatalogLoading(state));
  const allCardsAvailable = useSelector((state: RootState) =>
    deck ? deck.cards.every(({cardId}) => !!getCatalogCardById(state, cardId)) : false
  );

  useEffect(() => {
    if (initialized.current) return;
    if (!deck) return;
    if (!catalogLoaded) return;
    if (!allCardsAvailable) return;
    if (deck.cards.length === 0) return;
    dispatch(loadDeckIntoBuilder({deck}));
    initialized.current = true;
  }, [dispatch, catalogLoaded, allCardsAvailable, deck]);

  return null;
}
