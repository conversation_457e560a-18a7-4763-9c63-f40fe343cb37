import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {initializeDeckBuilderFromLocation} from '@/src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation';
import {getAvailableFilters} from '@/src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters';
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export const useInitializeDeckBuilderFromLocation = () => {
  const dispatch = useDispatch<AppDispatch>();
  const availableFilters = useSelector(getAvailableFilters);

  useEffect(() => {
    if (availableFilters.length > 0) {
      dispatch(initializeDeckBuilderFromLocation());
    }
  }, [dispatch, availableFilters]);
};
