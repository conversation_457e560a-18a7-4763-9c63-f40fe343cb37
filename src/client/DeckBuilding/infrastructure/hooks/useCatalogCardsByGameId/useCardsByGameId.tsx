import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {useQuery} from "convex-helpers/react/cache/hooks";
import {useEffect} from "react";
import {useDispatch, useSelector} from "react-redux";
import {loadCatalogCards} from "@/src/client/DeckBuilding/application/commands/loadCatalogCards/loadCatalogCards";
import {getCatalogError} from "@/src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {getFilteredCards} from "@/src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export function useCardsByGameId({gameId}: { gameId: string }) {
  const dispatch = useDispatch<AppDispatch>();

  const result = useQuery(
    api.queries.loadCatalogCardsByGameId.endpoint,
    {gameId: gameId as Id<"games">},
  );

  useEffect(() => {
    dispatch(loadCatalogCards(result));
  }, [dispatch, result]);

  return {
    error: useSelector(getCatalogError),
    isLoading: useSelector(isCatalogLoading),
    displayedCards: useSelector(getFilteredCards)
  }
}