import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { RootState } from '@/src/client/Shared/store/appStore/rootState';
import { useDeckId } from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId';

export const useDeckName = () => {
  const { name: deckName, deckId: statedeckId, cardsInDeck } = useSelector((state: RootState) => state.deckBuilder);
  const urlDeckId = useDeckId();
  const updateDeck = useMutation(api.mutations.updateDeck.endpoint);

  const [localName, setLocalName] = useState(deckName || '');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialMount = useRef(true);

  useEffect(() => {
    if (deckName !== null) {
      setLocalName(deckName);
    }
  }, [deckName]);

  const saveNameChange = useCallback(async (newName: string) => {
    if (!newName.trim()) return;

    const cards = Object.values(cardsInDeck).map(({ card, quantity }) => ({
      cardId: card.id,
      quantity,
    }));

    try {
      const effectiveDeckId = statedeckId || urlDeckId;
      
      if (effectiveDeckId) {
        await updateDeck({
          deckId: effectiveDeckId as Id<'decks'>,
          name: newName.trim(),
          cards,
        });
      }
      // If no deck exists yet, do nothing - let useAutoSaveDeck create it first
    } catch (error) {
      console.error('Failed to save deck name:', error);
    }
  }, [cardsInDeck, statedeckId, urlDeckId, updateDeck]);

  const handleNameChange = useCallback((newName: string) => {
    setLocalName(newName);

    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      saveNameChange(newName);
    }, 1000);
  }, [saveNameChange]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    name: localName,
    setName: handleNameChange,
  };
};
