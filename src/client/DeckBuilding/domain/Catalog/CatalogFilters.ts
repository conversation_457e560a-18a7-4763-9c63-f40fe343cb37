import {CatalogFilterState} from './catalogFiltersReducer';
import {filtersUpdatedEvent} from './catalogFilterEvents';

export class CatalogFilters {
  private constructor(private readonly state: CatalogFilterState) {
  }

  static fromState(state: CatalogFilterState): CatalogFilters {
    return new CatalogFilters({
      active: state.active.map(f => ({...f})),
      available: state.available.map(f => ({...f})),
    });
  }

  toState(): CatalogFilterState {
    return {
      active: this.state.active.map(f => ({...f})),
      available: this.state.available.map(f => ({...f})),
    };
  }

  applyFilters(names: string[]) {
    const upToDateFilters = this.state.available.filter(f => names.includes(f.name));
    this.state.active = upToDateFilters;
    return [filtersUpdatedEvent({filters: upToDateFilters})];
  }
}
